(()=>{"use strict";const e=window.React,t=window.wp.i18n,l=window.wp.components,n=window.wp.blockEditor,r=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-button-read-more","title":"Course Button Read More","category":"learnpress-course-elements","icon":"button","description":"Renders template Button Course Read More PHP templates.","textdomain":"learnpress","keywords":["button read more single course","learnpress"],"ancestor":["learnpress/course-item-template"],"usesContext":["lpCourseData"],"attributes":{"textAlign":{"type":"string","default":"center"},"justifyContent":{"type":"string","default":"center"},"alignItems":{"type":"string","default":"top"},"width":{"type":"string","default":"100"}},"supports":{"multiple":true,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":true,"text":true,"__experimentalDefaultControls":{"background":true,"text":true}},"__experimentalBorder":{"color":true,"radius":true,"width":true,"__experimentalDefaultControls":{"width":false,"color":false,"radius":false}},"spacing":{"margin":true,"padding":true,"content":true,"__experimentalDefaultControls":{"margin":false,"padding":false,"content":true}}}}'),o=window.wp.blocks;window.wp.data,(0,o.registerBlockType)(r.name,{...r,edit:r=>{const{attributes:o,setAttributes:a,context:s}=r,i=(0,n.useBlockProps)({style:{textAlign:o.textAlign,width:"100%"}});let u=i.className;return u=u.split(" ").filter(e=>e.startsWith("align")).join(" "),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(n.BlockControls,null,(0,e.createElement)(n.AlignmentToolbar,{value:o.textAlign,onChange:e=>a({textAlign:e})}),(0,e.createElement)(n.JustifyToolbar,{value:o.justifyContent,onChange:e=>a({justifyContent:e})}),(0,e.createElement)(n.BlockVerticalAlignmentToolbar,{value:o.alignItems,onChange:e=>a({alignItems:e})})),(0,e.createElement)(n.InspectorControls,null,(0,e.createElement)(l.PanelBody,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(l.__experimentalToggleGroupControl,{label:(0,t.__)("Width","learnpress"),value:o.width||"100",onChange:e=>{a({width:e||"100"})},isBlock:!0},(0,e.createElement)(l.__experimentalToggleGroupControlOption,{value:"25",label:"25%"}),(0,e.createElement)(l.__experimentalToggleGroupControlOption,{value:"50",label:"50%"}),(0,e.createElement)(l.__experimentalToggleGroupControlOption,{value:"75",label:"75%"}),(0,e.createElement)(l.__experimentalToggleGroupControlOption,{value:"100",label:"100%"})))),(0,e.createElement)("div",{class:"course-button-read-more",className:u,style:{display:"flex",textAlign:o.textAlign,alignItems:{top:"flex-start",center:"center",bottom:"flex-end"}[o.alignItems]||"flex-start",justifyContent:o.justifyContent}},(0,e.createElement)("a",{style:{width:o.width?`${o.width}%`:void 0}},(0,e.createElement)("button",{...i},(0,t.__)("Read more","learnpress")))))},save:e=>null})})();