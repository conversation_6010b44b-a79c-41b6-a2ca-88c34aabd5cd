(()=>{"use strict";var e={d:(s,t)=>{for(var n in t)e.o(t,n)&&!e.o(s,n)&&Object.defineProperty(s,n,{enumerable:!0,get:t[n]})},o:(e,s)=>Object.prototype.hasOwnProperty.call(e,s),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},s={};e.r(s),e.d(s,{FillInBlanks:()=>f,MultipleChoices:()=>w,QuestionBase:()=>u,SingleChoice:()=>d,TrueOrFalse:()=>m,default:()=>C});const t=window.React,n=window.wp.element,r=window.wp.compose,i=window.wp.data,o=window.wp.i18n,{isArray:a,get:p,set:l}=lodash;class c extends n.Component{constructor(e){super(...arguments);const{question:s}=e;this.state={optionClass:["answer-option"],questionId:0,options:s?this.parseOptions(s.options):[],self:this},e.$wrap&&(this.$wrap=e.$wrap)}static getDerivedStateFromProps(e,s){return s.self.prepare(e,s)}componentDidMount(){const e=this.prepare(this.props,this.state);e&&this.setState(e)}prepare=(e,s)=>{const{question:t}=e;return t&&t.id!==s.questionId?{options:s.self.parseOptions(t.options)}:null};setInputRef=(e,s)=>{this.inputs||(this.inputs={}),this.inputs[s]=e};maybeShowCorrectAnswer=()=>{const{status:e,isCheckedAnswer:s,showCorrectReview:t,isReviewing:n}=this.props;return"completed"===e&&t||s&&!n};maybeDisabledOption=e=>{const{answered:s,status:t,isCheckedAnswer:n}=this.props;return n||"started"!==t};setAnswerChecked=()=>e=>{const{updateUserQuestionAnswers:s,question:t,status:n}=this.props;if("started"!==n)return(0,o.__)("LP Error: can not set answers","learnpress");const r=this.$wrap.find(".option-check"),i=[],a="multi_choice"!==t.type;r.each((e,s)=>{if(s.checked&&(i.push(s.value),a))return!1}),s(t.id,a?i[0]:i)};maybeCheckedAnswer=e=>{const{answered:s}=this.props;return a(s)?!!s.find(s=>s==e):e==s};getOptionType=(e,s)=>{let t="radio";return"multi_choice"===e&&(t="checkbox"),t};isDefaultType=()=>this.props.supportOptions;getWarningMessage=()=>(0,t.createElement)(t.Fragment,null,(0,o.__)("The render function should be overwritten from the base.","learnpress"));getOptionClass=e=>{const{answered:s}=this.props;return["answer-option"]};parseOptions=e=>(e&&(e=a(e)?e:JSON.parse(CryptoJS.AES.decrypt(e.data,e.key,{format:CryptoJSAesJson}).toString(CryptoJS.enc.Utf8)),e=a(e)?e:JSON.parse(e)),e||[]);getOptions=()=>this.state.options||[];isCorrect=()=>{const{answered:e}=this.props;if(!e)return!1;let s,t,n;for(s=0,n=this.getOptions();s<n.length;s++)if(t=n[s],"yes"===t.isTrue&&e==t.value)return!0;return!1};isChecked=()=>{const{question:e}=this.props;return(0,i.select)("learnpress/quiz").isCheckedAnswer(e.id)};getCorrectLabel=()=>{const{status:e,answered:s,question:n}=this.props,r=(LP.config.isQuestionCorrect[n.type]||this.isCorrect).call(this);return this.maybeShowCorrectAnswer()&&(0,t.createElement)("div",{className:"question-response"+(r?" correct":" incorrect")},(0,t.createElement)("span",{className:"label"},r?(0,o.__)("Correct","learnpress"):(0,o.__)("Incorrect","learnpress")),(0,t.createElement)("span",{className:"point"},sprintf((0,o.__)("%d/%d point","learnpress"),r?n.point:0,n.point)))};render(){const{question:e,status:s}=this.props;return(0,t.createElement)("div",{className:"question-answers"},this.isDefaultType()&&(0,t.createElement)("ul",{id:`answer-options-${e.id}`,className:"answer-options"},this.getOptions().map(n=>{const r=`learn-press-answer-option-${n.uid}`;return(0,t.createElement)("li",{className:this.getOptionClass(n).join(" "),key:`answer-option-${n.uid}`},(0,t.createElement)("input",{type:this.getOptionType(e.type,n),className:"option-check",name:"started"===s?`learn-press-question-${e.id}`:"",id:r,ref:e=>{this.setInputRef(e,n.value)},onChange:this.setAnswerChecked(),disabled:this.maybeDisabledOption(n),checked:this.maybeCheckedAnswer(n.value),value:"started"===s?n.value:""}),(0,t.createElement)("label",{htmlFor:r,className:"option-title",dangerouslySetInnerHTML:{__html:n.title||n.value}}))})),!this.isDefaultType()&&this.getWarningMessage(),this.getCorrectLabel())}}const u=c,d=class extends u{getOptionClass=e=>{const{answered:s}=this.props,t=[...this.state.optionClass];return this.maybeShowCorrectAnswer()&&("yes"===e.isTrue&&t.push("answer-correct"),s&&("yes"===e.isTrue?s===e.value&&t.push("answered-correct"):s===e.value&&t.push("answered-wrong"))),t}},{isBoolean:h}=lodash,w=class extends u{isCorrect=()=>{const{answered:e}=this.props;if(h(e)||!e)return!1;let s,t,n;for(s=0,n=this.getOptions();s<n.length;s++)if(t=n[s],"yes"===t.isTrue){if(-1===e.indexOf(t.value))return!1}else if(-1!==e.indexOf(t.value))return!1;return!0};getOptionClass=e=>{const{answered:s}=this.props,t=[...this.state.optionClass];return this.maybeShowCorrectAnswer()&&("yes"===e.isTrue&&t.push("answer-correct"),s&&("yes"===e.isTrue?-1!==s.indexOf(e.value)&&t.push("answered-correct"):-1!==s.indexOf(e.value)&&t.push("answered-wrong"))),t}},m=class extends u{getOptionClass=e=>{const{answered:s}=this.props,t=[...this.state.optionClass];return this.maybeShowCorrectAnswer()&&("yes"===e.isTrue&&t.push("answer-correct"),s&&("yes"===e.isTrue?s===e.value&&t.push("answered-correct"):s===e.value&&t.push("answered-wrong"))),t}};let y=!1;const f=class extends u{componentDidMount(){const{answered:e,question:s}=this.props;e&&[...document.querySelectorAll(".lp-fib-input > input")].map(t=>{parseInt(t.closest(".question").dataset.id)===s.id&&e[t.dataset.id]&&(t.value=e[t.dataset.id])}),this.updateFibAnswer()}componentDidUpdate(e){e.answered||this.updateFibAnswer()}updateFibAnswer=()=>{y||document.addEventListener("input",e=>{const s=e.target;if(s.closest(".lp-fib-input")){const e=s.closest(".question-fill_in_blanks").dataset.id;this.setAnswered(e,s.dataset.id,s.value)}}),y=!0};setAnswered=(e,s,t)=>{const{updateUserQuestionFibAnswers:n,question:r,status:i}=this.props;if("started"!==i)return"LP Error: can not set answers";n(e,s,t)};getCorrectLabel=()=>{const{question:e,mark:s}=this.props;let n=s||0;return s&&(Number.isInteger(s)||(n=s.toFixed(2))),this.maybeShowCorrectAnswer()&&(0,t.createElement)("div",{className:"question-response correct"},(0,t.createElement)("span",{className:"label"},(0,o.__)("Points","learnpress")),(0,t.createElement)("span",{className:"point"},`${n}/${e.point} ${(0,o.__)("point","learnpress")}`),(0,t.createElement)("span",{className:"lp-fib-note"},(0,t.createElement)("span",{style:{background:"#00adff"}}),(0,o.__)("Correct","learnpress")),(0,t.createElement)("span",{className:"lp-fib-note"},(0,t.createElement)("span",{style:{background:"#d85554"}}),(0,o.__)("Incorrect","learnpress")))};convertInputField=e=>{const{answered:s,isReviewing:t,showCorrectReview:n,isCheckedAnswer:r}=this.props;let i=e.title;const o=e?.answers;return e.ids.map((e,a)=>{const p="{{FIB_"+e+"}}";let l="";const c=o?o?.[e]:void 0;var u,d;c||t?(l+=`<span class="lp-fib-answered ${(n||r)&&c?.correct?c?.isCorrect?"correct":"fail":""}">`,c?.isCorrect||(l+=`<span class="lp-fib-answered__answer">${null!==(d=s?.[e])&&void 0!==d?d:""}</span>`),!c?.isCorrect&&c?.correct&&(l+=" → "),l+=`<span class="lp-fib-answered__fill">${null!==(u=c?.correct)&&void 0!==u?u:""}</span>`,l+="</span>"):(l+='<div class="lp-fib-input" style="display: inline-block; width: auto;">',l+='<input type="text" data-id="'+e+'" value="" />',l+="</div>");i=i.replace(p,l)}),i};render(){return(0,t.createElement)(t.Fragment,null,(0,t.createElement)("div",{className:"lp-fib-content"},this.getOptions().map(e=>(0,t.createElement)("div",{key:`blank-${e.uid}`,dangerouslySetInnerHTML:{__html:this.convertInputField(e)||e.value}}))),!this.isDefaultType()&&this.getWarningMessage(),this.getCorrectLabel())}};class g extends n.Component{getQuestion=()=>{const{question:e}=this.props;return LP.Hook.applyFilters("question-types",{single_choice:LP.questionTypes.SingleChoice,multi_choice:LP.questionTypes.MultipleChoices,true_or_false:LP.questionTypes.TrueOrFalse,fill_in_blanks:LP.questionTypes.FillInBlanks})[e.type]};render(){const{question:e,supportOptions:s}=this.props,n={...this.props};n.supportOptions=-1!==s.indexOf(e.type);const r=this.getQuestion()||function(){return(0,t.createElement)("div",{className:"question-types",dangerouslySetInnerHTML:{__html:(0,o.sprintf)((0,o.__)("Question <code>%s</code> invalid!","learnpress"),e.type)}})};return(0,t.createElement)(t.Fragment,null,(0,t.createElement)(r,{...n}))}}const C=(0,r.compose)((0,i.withSelect)((e,{question:{id:s}})=>{const{getData:t,isCheckedAnswer:n}=e("learnpress/quiz");return{supportOptions:t("supportOptions"),isCheckedAnswer:n(s),keyPressed:t("keyPressed"),showCorrectReview:t("showCorrectReview"),isReviewing:"reviewing"===t("mode")}}),(0,i.withDispatch)(()=>({})))(g);(window.LP=window.LP||{}).questionTypes=s})();