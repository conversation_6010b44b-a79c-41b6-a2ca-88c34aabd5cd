(()=>{"use strict";const e="lp-hidden",s=(s,t=0)=>{s&&(t?s.classList.remove(e):s.classList.add(e))};document.addEventListener("click",e=>{const s=e.target,o=s.closest(".course-section-header");if(o){const s=o.closest(".course-section");if(!s)return;e.preventDefault(),c(s)}s.classList.contains("course-toggle-all-sections")&&(e.preventDefault(),t(s))}),document.addEventListener("keyup",e=>{const s=e.target;if("s"===s.name&&s.closest("form.search-course")){const e=s.value;l(e)}}),document.addEventListener("submit",e=>{e.target.closest("form.search-course")&&e.preventDefault()});const t=e=>{const t=e.closest(".lp-course-curriculum"),c=t.querySelectorAll(".course-section"),o=t.querySelector(".course-toggle-all-sections"),l=t.querySelector(".course-toggle-all-sections.lp-collapse");e.classList.contains("lp-collapse")?(s(o,1),s(l,0),c.forEach(e=>{e.classList.contains("lp-collapse")||e.classList.add("lp-collapse")})):c.forEach(e=>{s(o,0),s(l,1),e.classList.contains("lp-collapse")&&e.classList.remove("lp-collapse")})},c=e=>{const s=e.closest(".lp-course-curriculum");e.classList.toggle("lp-collapse"),o(s)},o=e=>{const t=e.querySelectorAll(".course-section"),c=e.querySelector(".course-toggle-all-sections"),o=e.querySelector(".course-toggle-all-sections.lp-collapse");let l=!1;t.forEach(e=>{e.classList.contains("lp-collapse")&&(l=!0)}),l?(s(c,1),s(o,0)):(s(c,0),s(o,1))},l=e=>{document.querySelector(".lp-course-curriculum").querySelectorAll(".course-section").forEach(t=>{let c=!1;t.querySelectorAll(".course-item").forEach(t=>{const o=t.closest(".course-section"),l=t.querySelector(".course-item-title").textContent;n(l,e)?(c=!0,s(t,1),o.classList.remove("lp-collapse")):(s(t,0),t.classList.add("lp-hide"))}),s(t,c?1:0)})},r=e=>e.normalize("NFD").replace(/[\u0300-\u036f]/g,""),n=(e,s)=>{const t=r(e.toLowerCase()),c=s.trim().split(" "),o=c.length;let l=0;return c.forEach(e=>{const s=r(e.toLowerCase());new RegExp(s,"gi").test(t)&&l++}),l===o};((e,s)=>{const t=document.querySelector(e);if(t)return void s(t);const c=new MutationObserver((t,c)=>{const o=document.querySelector(e);o&&(c.disconnect(),s(o))});c.observe(document.documentElement,{childList:!0,subtree:!0})})(".lp-course-curriculum",e=>{o(e);const s=setInterval(()=>{"complete"===document.readyState&&(clearInterval(s),(e=>{const s=e.querySelector("li.current");s&&s.scrollIntoView({behavior:"smooth"})})(e))},300)})})();