(()=>{"use strict";const e=window.React,s=(window.wp.i18n,window.wp.components,window.wp.blockEditor),t=t=>{const r=(0,s.useBlockProps)();return(0,e.createElement)("div",{...r},(0,e.createElement)("a",{class:"back-course","aria-label":"Back to course"},(0,e.createElement)("i",{class:"lp-icon-times"})))},r=e=>null,n=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/item-close","title":"Item Close","category":"learnpress-category","icon":"no-alt","description":"Renders template Single Course Legacy PHP templates.","textdomain":"learnpress","keywords":["item close course","learnpress"],"usesContext":[],"supports":{"align":true}}'),o=window.wp.blocks,l=window.wp.data;let c=null;var a,i,p;a=["learnpress/learnpress//single-lp_course_item"],i=n,p=e=>{(0,o.registerBlockType)(e.name,{...e,edit:t,save:r})},(0,l.subscribe)(()=>{const e={...i},s=(0,l.select)("core/editor")||null;if(!s||"function"!=typeof s.getCurrentPostId||!s.getCurrentPostId())return;const t=s.getCurrentPostId();null!==t&&c!==t&&(c=t,(0,o.getBlockType)(e.name)&&((0,o.unregisterBlockType)(e.name),a.includes(t)?(e.ancestor=null,p(e)):(e.ancestor||(e.ancestor=[]),p(e))))}),(0,o.registerBlockType)(n.name,{...n,edit:t,save:r})})();