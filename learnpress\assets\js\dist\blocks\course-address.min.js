(()=>{"use strict";const e=window.React,t=window.wp.i18n,r=window.wp.blockEditor,s=s=>{const n=(0,r.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...n},(0,e.createElement)("span",null,(0,t.__)("Address","learnpress")," ")))},n=e=>null,l=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-address","title":"Course Address","category":"learnpress-course-elements","description":"Renders template Address Course PHP templates.","textdomain":"learnpress","keywords":["address","single","offline","course","learnpress"],"icon":"location","ancestor":["learnpress/single-course"],"usesContext":["lpCourseData"],"attributes":{"showText":{"type":"boolean","default":true},"isLink":{"type":"boolean","default":true},"target":{"type":"boolean","default":false}},"supports":{"multiple":true,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"__experimentalDefaultControls":{"text":true}}}}'),o=window.wp.blocks,a=window.wp.data;let i=null;var u,p,c;u=["learnpress/learnpress//single-lp_course-offline"],p=l,c=e=>{(0,o.registerBlockType)(e.name,{...e,edit:s,save:n})},(0,a.subscribe)(()=>{const e={...p},t=(0,a.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&i!==r&&(i=r,(0,o.getBlockType)(e.name)&&((0,o.unregisterBlockType)(e.name),u.includes(r)?(e.ancestor=null,c(e)):(e.ancestor||(e.ancestor=[]),c(e))))}),(0,o.registerBlockType)(l.name,{...l,edit:s,save:n})})();