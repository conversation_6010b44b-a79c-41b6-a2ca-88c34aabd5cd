# Copyright (C) 2025 ThimPress
# This file is distributed under the same license as the LearnPress plugin.
msgid ""
msgstr ""
"Project-Id-Version: LearnPress *******.3\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/learnpress\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-06-30T12:43:55+03:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: learnpress\n"

#: assets/js/dist/admin/pages/tools.min.js:118
msgid "Reset Course Progress"
msgstr ""

#: assets/js/dist/admin/pages/tools.min.js:120
msgid "This action will reset course progress of all users who have enrolled."
msgstr ""

#: assets/js/dist/admin/pages/tools.min.js:121
msgid "Search results only show if courses have user data."
msgstr ""

#: assets/js/dist/admin/pages/tools.min.js:124
msgid "Search course by name"
msgstr ""

#: assets/js/dist/admin/pages/tools.min.js:154
#: inc/admin/meta-box/fields/payment-order.php:8
#: inc/admin/views/tools/course/html-user.php:28
msgid "ID"
msgstr ""

#: assets/js/dist/admin/pages/tools.min.js:155
#: assets/src/apps/js/blocks/course-elements/course-material/edit.js:17
#: config/elementor/course/materials.php:66
#: config/settings/profile.php:89
#: inc/admin/views/quiz/editor.php:47
#: inc/admin/views/tools/course/html-user.php:29
#: inc/TemplateHooks/Course/CourseMaterialTemplate.php:82
#: templates/global/become-teacher-form.php:35
#: templates/profile/tabs/courses/course-list.php:31
msgid "Name"
msgstr ""

#: assets/js/dist/admin/pages/tools.min.js:156
#: inc/admin/views/statistics/users.php:35
#: inc/custom-post-types/course.php:354
#: inc/custom-post-types/course.php:371
msgid "Students"
msgstr ""

#: assets/js/dist/admin/pages/tools.min.js:185
msgid "Reset now"
msgstr ""

#: assets/src/apps/js/admin/react/question/index.js:288
msgid "Words fill"
msgstr ""

#: assets/src/apps/js/admin/react/question/index.js:306
msgid "Tip"
msgstr ""

#: assets/src/apps/js/admin/react/question/index.js:313
#: inc/admin/views/meta-boxes/fields/materials.php:110
#: inc/admin/views/tools/course/html-unassign-course.php:97
#: inc/class-lp-assets.php:144
#: inc/TemplateHooks/Profile/ProfileTemplate.php:185
#: inc/TemplateHooks/Profile/ProfileTemplate.php:281
#: templates/profile/tabs/settings/avatar.php:26
msgid "Remove"
msgstr ""

#: assets/src/apps/js/admin/react/question/index.js:319
msgid "Add Blank"
msgstr ""

#: assets/src/apps/js/blocks/archive-course/edit.js:11
msgid "Archive Course"
msgstr ""

#: assets/src/apps/js/blocks/archive-course/edit.js:15
msgid "This is an editor placeholder for the Archive Course page. Content will render content of list courses. Should be not remove it"
msgstr ""

#: assets/src/apps/js/blocks/archive-course-legacy/edit.js:11
msgid "Archive Course (Legacy)"
msgstr ""

#: assets/src/apps/js/blocks/archive-course-legacy/edit.js:15
msgid "The block will display the full content of the course archive page. Elements on it cannot be modified!"
msgstr ""

#: assets/src/apps/js/blocks/breadcrumb/edit.js:10
#: assets/src/apps/js/blocks/course-elements/course-button/edit.js:56
#: assets/src/apps/js/blocks/course-elements/course-button-read-more/edit.js:54
#: assets/src/apps/js/blocks/course-elements/course-capacity/edit.js:15
#: assets/src/apps/js/blocks/course-elements/course-categories/edit.js:13
#: assets/src/apps/js/blocks/course-elements/course-delivery/edit.js:15
#: assets/src/apps/js/blocks/course-elements/course-duration/edit.js:17
#: assets/src/apps/js/blocks/course-elements/course-image/edit.js:15
#: assets/src/apps/js/blocks/course-elements/course-instructor/edit.js:13
#: assets/src/apps/js/blocks/course-elements/course-lesson/edit.js:17
#: assets/src/apps/js/blocks/course-elements/course-level/edit.js:17
#: assets/src/apps/js/blocks/course-elements/course-offline-lesson/edit.js:16
#: assets/src/apps/js/blocks/course-elements/course-quiz/edit.js:17
#: assets/src/apps/js/blocks/course-elements/course-student/edit.js:17
#: assets/src/apps/js/blocks/course-elements/course-title/edit.js:26
#: assets/src/apps/js/blocks/course-filter/course-filter/edit.js:10
#: assets/src/apps/js/blocks/course-filter/edit.js:10
#: assets/src/apps/js/blocks/instructor-elements/instructor-background/edit.js:25
#: assets/src/apps/js/blocks/instructor-elements/instructor-course/edit.js:16
#: assets/src/apps/js/blocks/instructor-elements/instructor-social/edit.js:10
#: assets/src/apps/js/blocks/instructor-elements/instructor-student/edit.js:16
#: config/profile-tabs.php:51
#: config/settings/permalink.php:141
#: inc/admin/sub-menus/class-lp-submenu-settings.php:18
#: inc/admin/views/addons.php:181
#: learnpress.php:756
msgid "Settings"
msgstr ""

#: assets/src/apps/js/blocks/breadcrumb/edit.js:12
msgid "Show Home"
msgstr ""

#: assets/src/apps/js/blocks/breadcrumb/edit.js:22
msgid "Home Label"
msgstr ""

#: assets/src/apps/js/blocks/breadcrumb/edit.js:50
msgid "Navigation"
msgstr ""

#: assets/src/apps/js/blocks/breadcrumb/edit.js:56
msgid "Path"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-address/edit.js:9
#: inc/admin/views/meta-boxes/course/settings.php:330
msgid "Address"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-button/edit.js:58
#: assets/src/apps/js/blocks/course-elements/course-button-read-more/edit.js:56
#: config/elementor/course/filter-course-el.php:251
#: config/elementor/course/filter-course-el.php:532
#: config/elementor/course/filter-course-el.php:588
#: config/elementor/course/filter-course-el.php:689
#: config/elementor/course/filter-course-el.php:743
msgid "Width"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-button/edit.js:88
#: inc/admin/helpers/class-lp-plugins-helper.php:271
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:739
#: templates/single-course/buttons/purchase.php:33
msgid "Buy Now"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-button-read-more/edit.js:87
#: inc/Gutenberg/Blocks/SingleCourseElements/CourseButtonReadMoreBlockType.php:98
#: inc/Gutenberg/Blocks/SingleCourseElements/CourseButtonReadMoreBlockType.php:99
#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:171
#: inc/TemplateHooks/Course/ListCoursesTemplate.php:266
msgid "Read more"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-capacity/edit.js:17
#: assets/src/apps/js/blocks/course-elements/course-delivery/edit.js:17
#: assets/src/apps/js/blocks/course-elements/course-duration/edit.js:19
#: assets/src/apps/js/blocks/course-elements/course-lesson/edit.js:19
#: assets/src/apps/js/blocks/course-elements/course-level/edit.js:19
#: assets/src/apps/js/blocks/course-elements/course-offline-lesson/edit.js:18
#: assets/src/apps/js/blocks/course-elements/course-quiz/edit.js:19
#: assets/src/apps/js/blocks/course-elements/course-student/edit.js:19
msgid "Show Label"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-capacity/edit.js:26
#: assets/src/apps/js/blocks/course-elements/course-delivery/edit.js:26
#: assets/src/apps/js/blocks/course-elements/course-duration/edit.js:28
#: assets/src/apps/js/blocks/course-elements/course-lesson/edit.js:28
#: assets/src/apps/js/blocks/course-elements/course-level/edit.js:28
#: assets/src/apps/js/blocks/course-elements/course-offline-lesson/edit.js:27
#: assets/src/apps/js/blocks/course-elements/course-quiz/edit.js:28
#: assets/src/apps/js/blocks/course-elements/course-student/edit.js:28
msgid "Show Icon"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-capacity/edit.js:45
msgid "Capacity:"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-categories/edit.js:15
#: assets/src/apps/js/blocks/course-elements/course-instructor/edit.js:15
msgid "Show text 'by'"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-categories/edit.js:24
msgid "Make the category a link"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-categories/edit.js:34
#: assets/src/apps/js/blocks/course-elements/course-image/edit.js:27
#: assets/src/apps/js/blocks/course-elements/course-instructor/edit.js:34
#: assets/src/apps/js/blocks/course-elements/course-title/edit.js:45
msgid "Open is new tab"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-curriculum/edit.js:10
#: inc/admin/views/meta-boxes/course/settings.php:43
#: inc/lp-template-functions.php:72
#: inc/TemplateHooks/Course/SingleCourseClassicTemplate.php:268
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:1453
msgid "Curriculum"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-delivery/edit.js:43
msgid "Delivery type:"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-faqs/edit.js:10
#: inc/admin/views/meta-boxes/course/settings.php:616
#: inc/lp-template-functions.php:88
#: inc/TemplateHooks/Course/SingleCourseClassicTemplate.php:271
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:964
msgid "FAQs"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-feature-review/edit.js:9
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:665
#: templates/single-course/featured-review.php:18
msgid "Featured Review"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-featured/edit.js:9
#: config/elementor/course/list-courses.php:75
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:1298
#: templates/loop/course/badge-featured.php:23
msgid "Featured"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-features/edit.js:10
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:1094
#: inc/templates/class-lp-template-course.php:913
msgid "Features"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-image/edit.js:17
msgid "Make the image a link"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-instructor/edit.js:24
msgid "Make the instructor a link"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-instructor-info/edit.js:17
#: inc/ExternalPlugin/Elementor/Widgets/Instructor/Sections/InstructorTitleElementor.php:20
#: inc/ExternalPlugin/Elementor/Widgets/Instructor/Sections/InstructorTitleElementor.php:44
msgid "Instructor Name"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-item-curriculum/edit.js:9
msgid "Course item curriculum (Legacy) - Don't remove"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-item-curriculum/edit.js:13
msgid "Displays the course curriculum, including lessons, quizzes, and other learning items, organized by sections or topics!"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-material/edit.js:11
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:1207
msgid "Course Material"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-material/edit.js:18
#: config/elementor/course/materials.php:71
#: config/settings/profile.php:95
#: inc/admin/views/quiz/editor.php:48
#: inc/custom-post-types/question.php:314
#: inc/TemplateHooks/Course/CourseMaterialTemplate.php:83
#: inc/TemplateHooks/Course/FilterCourseTemplate.php:740
#: inc/Widgets/course-extra.php:30
msgid "Type"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-material/edit.js:19
#: config/elementor/course/materials.php:76
#: inc/TemplateHooks/Course/CourseMaterialTemplate.php:84
msgid "Size"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-material/edit.js:20
#: config/elementor/course/materials.php:81
#: inc/TemplateHooks/Course/CourseMaterialTemplate.php:85
msgid "Download"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-material/edit.js:37
#: assets/src/apps/js/blocks/courses/list-courses/edit.js:40
#: config/elementor/course/list-courses-by-page.php:172
#: inc/TemplateHooks/Course/ListCoursesTemplate.php:350
msgid "Load more"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-offline-lesson/edit.js:44
msgid "Lesson:"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-requirements/edit.js:10
#: inc/admin/views/meta-boxes/course/settings.php:601
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:1046
#: inc/templates/class-lp-template-course.php:328
#: inc/templates/class-lp-template-course.php:909
#: inc/Widgets/course-extra.php:35
msgid "Requirements"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-share/edit.js:10
#: inc/TemplateHooks/Course/SingleCourseModernLayout.php:381
msgid "Share"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-target-audiences/edit.js:10
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:1142
#: inc/templates/class-lp-template-course.php:380
#: inc/templates/class-lp-template-course.php:917
msgid "Target audiences"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-title/edit.js:21
msgid "Course Title"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-title/edit.js:28
#: inc/custom-post-types/course.php:141
#: inc/custom-post-types/question.php:184
#: inc/custom-post-types/question.php:185
msgid "Tag"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-title/edit.js:35
msgid "Make the title a link"
msgstr ""

#: assets/src/apps/js/blocks/course-filter/button-reset-filter/edit.js:8
#: assets/src/apps/js/blocks/course-filter-elements/button-reset-filter/edit.js:8
#: inc/admin/views/tools/course/html-user-item.php:26
#: inc/TemplateHooks/Course/FilterCourseTemplate.php:785
msgid "Reset"
msgstr ""

#: assets/src/apps/js/blocks/course-filter/button-submit-filter/edit.js:8
#: assets/src/apps/js/blocks/course-filter-elements/button-submit-filter/edit.js:8
#: inc/TemplateHooks/Course/FilterCourseTemplate.php:757
msgid "Apply"
msgstr ""

#: assets/src/apps/js/blocks/course-filter/course-author-filter/edit.js:9
#: assets/src/apps/js/blocks/course-filter-elements/course-author-filter/edit.js:9
#: config/elementor/course/filter-course-el.php:43
#: config/widgets/course/filter-course.php:65
#: inc/admin/views/meta-boxes/course/settings.php:92
#: inc/admin/views/meta-boxes/course/settings.php:502
#: inc/custom-post-types/course.php:352
#: inc/custom-post-types/course.php:369
#: inc/custom-post-types/lesson.php:238
#: inc/custom-post-types/question.php:312
#: inc/custom-post-types/quiz.php:239
#: inc/TemplateHooks/Course/FilterCourseTemplate.php:600
msgid "Author"
msgstr ""

#: assets/src/apps/js/blocks/course-filter/course-categories-filter/edit.js:9
#: assets/src/apps/js/blocks/course-filter-elements/course-categories-filter/edit.js:9
#: inc/custom-post-types/course.php:113
#: inc/TemplateHooks/Course/SingleCourseClassicTemplate.php:249
#: templates/single-course/meta/category.php:15
msgid "Category"
msgstr ""

#: assets/src/apps/js/blocks/course-filter/course-filter/edit.js:12
#: assets/src/apps/js/blocks/course-filter/edit.js:12
#: config/elementor/course/filter-course-el.php:430
#: config/elementor/instructor/become-a-teacher.php:20
#: config/elementor/instructor/become-a-teacher.php:46
#: config/elementor/login-user-form.php:19
#: config/elementor/register-user-form.php:19
#: config/widgets/course/filter-course.php:9
#: inc/admin/views/meta-boxes/fields/extra-faq.php:38
#: inc/admin/views/meta-boxes/fields/extra-faq.php:58
#: inc/admin/views/setup/setup-stripe.php:7
#: inc/Widgets/course-extra.php:25
#: inc/Widgets/course-info.php:32
#: inc/Widgets/course-progress.php:29
#: inc/Widgets/featured-courses.php:28
#: inc/Widgets/popular-courses.php:24
#: inc/Widgets/recent-courses.php:29
msgid "Title"
msgstr ""

#: assets/src/apps/js/blocks/course-filter/course-filter/edit.js:22
#: assets/src/apps/js/blocks/course-filter/edit.js:22
#: config/widgets/course/filter-course.php:14
msgid "Level of category to display on Frontend"
msgstr ""

#: assets/src/apps/js/blocks/course-filter/course-filter/edit.js:34
#: assets/src/apps/js/blocks/course-filter/edit.js:34
#: config/elementor/course/filter-course-el.php:135
#: config/widgets/course/filter-course.php:25
msgid "Load widget via REST"
msgstr ""

#: assets/src/apps/js/blocks/course-filter/course-filter/edit.js:44
#: assets/src/apps/js/blocks/course-filter/edit.js:44
#: config/widgets/course/filter-course.php:30
msgid "Hide field has count is zero"
msgstr ""

#: assets/src/apps/js/blocks/course-filter/course-filter/edit.js:54
#: assets/src/apps/js/blocks/course-filter/edit.js:54
#: config/elementor/course/filter-course-el.php:146
#: config/widgets/course/filter-course.php:35
msgid "Enable Keyword Search Suggestion"
msgstr ""

#: assets/src/apps/js/blocks/course-filter/course-level-filter/edit.js:9
#: assets/src/apps/js/blocks/course-filter-elements/course-level-filter/edit.js:9
#: config/elementor/course/filter-course-el.php:44
#: config/widgets/course/filter-course.php:69
#: inc/admin/views/meta-boxes/course/settings.php:200
#: inc/Gutenberg/Blocks/SingleCourseElements/CourseLevelBlockType.php:79
#: inc/TemplateHooks/Course/SingleCourseModernLayout.php:222
#: inc/TemplateHooks/Course/SingleCourseOfflineTemplate.php:243
msgid "Level"
msgstr ""

#: assets/src/apps/js/blocks/course-filter/course-price-filter/edit.js:9
#: assets/src/apps/js/blocks/course-filter-elements/course-price-filter/edit.js:9
#: config/elementor/course/filter-course-el.php:40
#: config/elementor/course/list-courses.php:123
#: config/widgets/course/filter-course.php:53
#: inc/custom-post-types/course.php:355
#: inc/custom-post-types/course.php:372
#: inc/TemplateHooks/Course/FilterCourseTemplate.php:293
#: inc/TemplateHooks/Course/SingleCourseOfflineTemplate.php:231
#: templates/emails/order-items-table.php:89
msgid "Price"
msgstr ""

#: assets/src/apps/js/blocks/course-filter/course-search-filter/edit.js:9
#: assets/src/apps/js/blocks/course-filter-elements/course-search-filter/edit.js:9
#: config/elementor/course/filter-course-el.php:488
#: inc/admin/views/search-author-field.php:64
#: inc/admin/views/tools/course/html-user.php:21
#: inc/TemplateHooks/Course/FilterCourseTemplate.php:189
msgid "Search"
msgstr ""

#: assets/src/apps/js/blocks/course-filter/course-tag-filter/edit.js:9
#: assets/src/apps/js/blocks/course-filter-elements/course-tag-filter/edit.js:9
#: inc/admin/lp-admin-actions.php:224
#: inc/admin/sub-menus/class-lp-submenu-tags.php:11
#: inc/admin/sub-menus/class-lp-submenu-tags.php:12
#: inc/TemplateHooks/Course/FilterCourseTemplate.php:526
msgid "Tags"
msgstr ""

#: assets/src/apps/js/blocks/courses/course-item-template/edit.js:159
msgid "Courses Fetching…"
msgstr ""

#: assets/src/apps/js/blocks/courses/course-item-template/edit.js:168
msgid "Course One"
msgstr ""

#: assets/src/apps/js/blocks/courses/course-item-template/edit.js:172
msgid "Course two"
msgstr ""

#: assets/src/apps/js/blocks/courses/course-item-template/edit.js:182
#: config/settings/course.php:121
msgid "Load More"
msgstr ""

#: assets/src/apps/js/blocks/courses/list-courses/edit.js:39
#: config/elementor/course/list-courses-by-page.php:171
#: config/settings/course.php:120
#: config/settings/profile.php:102
msgid "Number"
msgstr ""

#: assets/src/apps/js/blocks/courses/list-courses/edit.js:41
#: config/settings/course.php:122
msgid "Infinite Scroll"
msgstr ""

#: assets/src/apps/js/blocks/courses/list-courses/edit.js:47
msgid "Query Settings"
msgstr ""

#: assets/src/apps/js/blocks/courses/list-courses/edit.js:49
msgid "Posts per page"
msgstr ""

#: assets/src/apps/js/blocks/courses/list-courses/edit.js:60
msgid "Related Course"
msgstr ""

#: assets/src/apps/js/blocks/courses/list-courses/edit.js:82
msgid "Enable AJAX"
msgstr ""

#: assets/src/apps/js/blocks/courses/list-courses/edit.js:97
msgid "Order by"
msgstr ""

#: assets/src/apps/js/blocks/courses/list-courses/edit.js:110
#: config/elementor/course/list-courses-by-page.php:163
#: inc/admin/views/meta-boxes/quiz/settings.php:77
msgid "Pagination"
msgstr ""

#: assets/src/apps/js/blocks/courses/list-courses/edit.js:126
#: config/settings/course.php:114
msgid "Pagination Type"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-background/edit.js:27
msgid "Background Position"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-background/edit.js:38
msgid "Background Size"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-background/edit.js:49
msgid "Background Repeat"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-course/edit.js:18
#: assets/src/apps/js/blocks/instructor-elements/instructor-student/edit.js:18
msgid "Display Modes"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-name/edit.js:11
msgid "Instructor's name"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-social/edit.js:12
msgid "Open links in new tab"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-social/edit.js:21
msgid "Add nofollow attribute"
msgstr ""

#: assets/src/apps/js/blocks/item-curriculum-course/edit.js:13
msgid "Item Curriculum Course"
msgstr ""

#: assets/src/apps/js/blocks/item-curriculum-course/edit.js:17
msgid "This is an editor placeholder for the Item Curriculum Course page. Content will render content of single item curriculum course. Should be not remove it"
msgstr ""

#: assets/src/apps/js/blocks/single-course/course-title/edit.js:7
#: assets/src/apps/js/blocks/single-course-legacy/edit.js:11
msgid "Single Course (Legacy)"
msgstr ""

#: assets/src/apps/js/blocks/single-course/course-title/edit.js:11
#: assets/src/apps/js/blocks/single-course/edit.js:15
msgid "This is an editor placeholder for the Single Course page. Content will render content of single course. Should be not remove it"
msgstr ""

#: assets/src/apps/js/blocks/single-course/edit.js:11
msgid "Single Course"
msgstr ""

#: assets/src/apps/js/blocks/single-course-legacy/edit.js:15
msgid "Display full content of Single Course, can not edit."
msgstr ""

#: assets/src/apps/js/blocks/single-course-types/course-title/index.js:47
msgid "test"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:41
#: inc/class-lp-assets.php:117
#: inc/lp-core-functions.php:2383
msgid "OK"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:44
#: inc/admin/class-lp-admin.php:257
#: inc/admin/views/addons.php:233
#: inc/admin/views/addons.php:246
#: inc/admin/views/meta-boxes/fields/date.php:56
#: inc/class-lp-assets.php:118
#: inc/lp-core-functions.php:2384
#: inc/Models/UserItems/UserItemModel.php:528
#: inc/order/class-lp-order.php:983
#: inc/TemplateHooks/Profile/ProfileTemplate.php:180
#: inc/TemplateHooks/Profile/ProfileTemplate.php:276
#: templates/profile/tabs/settings/avatar.php:28
msgid "Cancel"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:98
msgid "LP Error: can not set answers"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:147
msgid "The render function should be overwritten from the base."
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:207
#: assets/js/dist/frontend/quiz.min.js:81
#: assets/js/dist/frontend/quiz.min.js:204
#: inc/custom-post-types/question.php:127
msgid "Correct"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:207
#: assets/js/dist/frontend/quiz.min.js:82
#: inc/custom-post-types/question.php:133
msgid "Incorrect"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:208
msgid "%d/%d point"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:79
#: assets/js/dist/frontend/quiz.min.js:196
#: inc/admin/views/meta-boxes/question/settings.php:17
#: inc/admin/views/quiz/question-meta.php:28
msgid "Points"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:80
msgid "point"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:34
msgid "Question <code>%s</code> invalid!"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:15
msgid "Last Attempt"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:21
#: assets/js/dist/frontend/quiz.min.js:200
#: inc/admin/views/quiz/modal-choose-items.php:55
#: inc/custom-post-types/question.php:208
#: inc/custom-post-types/quiz.php:178
#: inc/custom-post-types/quiz.php:241
#: inc/custom-post-types/quiz.php:448
msgid "Questions"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:22
#: assets/js/dist/frontend/quiz.min.js:192
#: inc/TemplateHooks/Profile/ProfileQuizzesTemplate.php:132
#: templates/profile/tabs/quizzes.php:58
msgid "Time spent"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:23
msgid "Marks"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:24
msgid "Passing grade"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:25
#: assets/js/dist/frontend/quiz.min.js:300
#: inc/TemplateHooks/Profile/ProfileQuizzesTemplate.php:129
#: templates/profile/tabs/courses/course-list.php:32
#: templates/profile/tabs/quizzes.php:57
msgid "Result"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:54
msgid "Check answers"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:57
msgid "You need to answer the question before checking the answer key."
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:22
#: assets/js/dist/frontend/quiz.min.js:177
#: inc/admin/views/meta-boxes/question/settings.php:30
#: inc/admin/views/quiz/question-meta.php:39
msgid "Hint"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:17
msgid "Are you sure you want to retake the quiz?"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:106
#: assets/js/dist/frontend/quiz.min.js:55
msgid "Are you sure to submit the quiz?"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:160
msgid "Prev"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:204
msgid "Next"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:268
#: inc/admin/views/meta-boxes/quiz/settings.php:64
msgid "Retake"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:268
msgid "Start"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:289
#: assets/js/dist/frontend/quiz.min.js:116
msgid "Finish Quiz"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:309
#: inc/admin/views/meta-boxes/quiz/settings.php:91
msgid "Review"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:20
#: templates/single-course/sidebar/user-time.php:41
msgid "Duration:"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:25
msgid "Passing grade:"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:30
msgid "Questions:"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:87
#: inc/admin/class-lp-install-sample-data.php:157
#: inc/custom-post-types/abstract.php:905
#: inc/custom-post-types/question.php:357
#: templates/content-lesson/content.php:27
msgid "Edit"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:163
#: inc/admin/views/meta-boxes/question/settings.php:35
#: inc/admin/views/quiz/question-meta.php:50
msgid "Explanation"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:165
#: inc/course/lp-course-functions.php:939
#: inc/lp-core-functions.php:2012
#: inc/Models/UserItems/UserItemModel.php:534
#: inc/Models/UserItems/UserQuizModel.php:627
#: inc/TemplateHooks/Profile/ProfileQuizzesTemplate.php:241
#: inc/TemplateHooks/UserItem/UserItemBaseTemplate.php:167
#: inc/templates/class-lp-template-profile.php:161
#: inc/user-item/class-lp-user-item-quiz.php:147
#: inc/user/class-lp-profile.php:776
msgid "Passed"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:167
#: inc/course/lp-course-functions.php:942
#: inc/lp-core-functions.php:2015
#: inc/Models/UserItems/UserItemModel.php:537
#: inc/Models/UserItems/UserQuizModel.php:628
#: inc/order/class-lp-order.php:292
#: inc/TemplateHooks/Profile/ProfileQuizzesTemplate.php:242
#: inc/TemplateHooks/UserItem/UserItemBaseTemplate.php:170
#: inc/templates/class-lp-template-profile.php:162
#: inc/user-item/class-lp-user-item-quiz.php:148
#: inc/user/class-lp-profile.php:777
msgid "Failed"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:174
msgid "Your Result"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:208
msgid "Wrong"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:212
msgid "Skipped"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:217
msgid "Minus points"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:93
msgid "Question <span>%d to %d of %d</span>"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:95
#: assets/js/dist/frontend/quiz.min.js:98
msgid "Question <span>%d of %d</span>"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:107
msgid "Earned Point: %s"
msgstr ""

#: assets/js/dist/frontend/quiz.min.js:116
msgid "Submitting…"
msgstr ""

#: assets/js/dist/frontend/profile.min.js:131
msgid "The file size is too large. You need to upload a file < 2MB."
msgstr ""

#: assets/js/dist/frontend/profile.min.js:133
msgid "The image size must be greater than or equal to %1$sx%2$spx"
msgstr ""

#: assets/js/dist/frontend/profile.min.js:146
msgid "Are you sure you want to remove your avatar?"
msgstr ""

#. Plugin Name of the plugin
#: learnpress.php
#: inc/admin/class-lp-admin.php:143
#: inc/admin/class-lp-admin.php:151
#: inc/admin/lp-admin-functions.php:1503
#: inc/admin/settings/class-lp-settings-emails.php:153
#: inc/ExternalPlugin/Elementor/LPElementor.php:56
#: inc/ExternalPlugin/Elementor/LPElementor.php:112
#: inc/Gutenberg/GutenbergHandleMain.php:408
msgid "LearnPress"
msgstr ""

#. Plugin URI of the plugin
#: learnpress.php
msgid "http://thimpress.com/learnpress"
msgstr ""

#. Description of the plugin
#: learnpress.php
msgid "LearnPress is a WordPress complete solution for creating a Learning Management System (LMS). It can help you to create courses, lessons and quizzes."
msgstr ""

#. Author of the plugin
#: learnpress.php
msgid "ThimPress"
msgstr ""

#. Author URI of the plugin
#: learnpress.php
msgid "http://thimpress.com"
msgstr ""

#: config/course-deliver-type.php:10
msgid "Private 1-1"
msgstr ""

#: config/course-deliver-type.php:11
msgid "In-person class"
msgstr ""

#: config/course-deliver-type.php:12
msgid "Live class"
msgstr ""

#: config/elementor/course/course-price.php:16
msgid "Regular Price"
msgstr ""

#: config/elementor/course/course-price.php:21
#: config/elementor/course/filter-course-el.php:507
#: config/elementor/course/filter-course-el.php:563
#: config/elementor/course/filter-course-el.php:664
msgid "Alignment"
msgstr ""

#: config/elementor/course/course-price.php:47
#: config/elementor/course/course-price.php:66
#: config/elementor/course/course-price.php:97
msgid "Color"
msgstr ""

#: config/elementor/course/course-price.php:61
msgid "Origin Price"
msgstr ""

#: config/elementor/course/course-price.php:78
#: config/elementor/course/filter-course-el.php:312
#: config/elementor/course/filter-course-el.php:370
#: inc/ExternalPlugin/Elementor/LPElementorControls.php:426
#: inc/ExternalPlugin/Elementor/LPElementorControls.php:560
msgid "Padding"
msgstr ""

#: config/elementor/course/course-price.php:92
msgid "Free Price"
msgstr ""

#: config/elementor/course/filter-course-el.php:17
msgid "Filter Area"
msgstr ""

#: config/elementor/course/filter-course-el.php:22
#: config/widgets/course/filter-course.php:44
msgid "Fields"
msgstr ""

#: config/elementor/course/filter-course-el.php:36
msgid "Filter By"
msgstr ""

#: config/elementor/course/filter-course-el.php:39
#: config/widgets/course/filter-course.php:49
msgid "Keyword"
msgstr ""

#: config/elementor/course/filter-course-el.php:41
#: config/widgets/course/filter-course.php:57
#: inc/custom-post-types/course.php:112
msgid "Course Category"
msgstr ""

#: config/elementor/course/filter-course-el.php:42
#: config/widgets/course/filter-course.php:61
msgid "Course Tag"
msgstr ""

#: config/elementor/course/filter-course-el.php:45
#: config/elementor/course/filter-course-el.php:499
#: config/elementor/instructor/become-a-teacher.php:82
#: config/elementor/login-user-form.php:46
#: config/elementor/register-user-form.php:46
#: config/widgets/course/filter-course.php:77
msgid "Button Submit"
msgstr ""

#: config/elementor/course/filter-course-el.php:46
#: config/elementor/course/filter-course-el.php:555
#: config/widgets/course/filter-course.php:81
msgid "Button Reset"
msgstr ""

#: config/elementor/course/filter-course-el.php:64
msgid "Show Count"
msgstr ""

#: config/elementor/course/filter-course-el.php:67
#: config/elementor/course/filter-course-el.php:91
#: config/elementor/course/filter-course-el.php:100
#: config/elementor/course/filter-course-el.php:112
#: config/elementor/course/filter-course-el.php:175
#: config/elementor/course/filter-course-el.php:198
#: config/elementor/course/materials.php:40
#: inc/ExternalPlugin/Elementor/LPElementorControls.php:543
msgid "Show"
msgstr ""

#: config/elementor/course/filter-course-el.php:68
#: config/elementor/course/filter-course-el.php:92
#: config/elementor/course/filter-course-el.php:101
#: config/elementor/course/filter-course-el.php:113
#: config/elementor/course/filter-course-el.php:176
#: config/elementor/course/filter-course-el.php:199
#: config/elementor/course/materials.php:41
#: inc/ExternalPlugin/Elementor/LPElementorControls.php:542
msgid "Hide"
msgstr ""

#: config/elementor/course/filter-course-el.php:76
msgid "Heading Setting"
msgstr ""

#: config/elementor/course/filter-course-el.php:78
#: config/elementor/course/list-courses.php:72
#: config/elementor/instructor/list-instructors.php:55
msgid "Default"
msgstr ""

#: config/elementor/course/filter-course-el.php:79
#: config/elementor/instructor/list-instructors.php:56
#: inc/admin/views/statistics/courses.php:16
#: inc/admin/views/statistics/orders.php:16
#: inc/admin/views/statistics/overview.php:15
#: inc/admin/views/statistics/users.php:16
msgid "Custom"
msgstr ""

#: config/elementor/course/filter-course-el.php:88
msgid "Enable Heading"
msgstr ""

#: config/elementor/course/filter-course-el.php:97
msgid "Toggle Content"
msgstr ""

#: config/elementor/course/filter-course-el.php:109
msgid "Default Toggle On"
msgstr ""

#: config/elementor/course/filter-course-el.php:130
msgid "Extra Option"
msgstr ""

#: config/elementor/course/filter-course-el.php:139
#: config/elementor/course/filter-course-el.php:150
#: config/elementor/course/filter-course-el.php:161
#: config/elementor/course/filter-course-el.php:241
#: config/elementor/course/filter-course-el.php:282
#: config/elementor/course/list-courses-by-page.php:55
#: config/elementor/course/list-courses-by-page.php:67
#: config/elementor/course/list-courses-by-page.php:89
#: config/elementor/course/list-courses-by-page.php:101
#: inc/class-lp-assets.php:119
#: inc/lp-core-functions.php:2385
msgid "Yes"
msgstr ""

#: config/elementor/course/filter-course-el.php:140
#: config/elementor/course/filter-course-el.php:151
#: config/elementor/course/filter-course-el.php:162
#: config/elementor/course/filter-course-el.php:242
#: config/elementor/course/filter-course-el.php:283
#: config/elementor/course/list-courses-by-page.php:56
#: config/elementor/course/list-courses-by-page.php:68
#: config/elementor/course/list-courses-by-page.php:90
#: config/elementor/course/list-courses-by-page.php:102
#: inc/class-lp-assets.php:120
#: inc/lp-core-functions.php:2386
msgid "No"
msgstr ""

#: config/elementor/course/filter-course-el.php:157
#: config/elementor/course/filter-course-el.php:171
msgid "Filter Toggle Button"
msgstr ""

#: config/elementor/course/filter-course-el.php:182
msgid "Text Button"
msgstr ""

#: config/elementor/course/filter-course-el.php:183
#: inc/admin/views/statistics/courses.php:20
#: inc/admin/views/statistics/orders.php:20
#: inc/admin/views/statistics/overview.php:19
#: inc/admin/views/statistics/users.php:20
#: inc/ExternalPlugin/Elementor/Widgets/Course/FilterCourseElementor.php:148
msgid "Filter"
msgstr ""

#: config/elementor/course/filter-course-el.php:194
msgid "Button Icon"
msgstr ""

#: config/elementor/course/filter-course-el.php:208
msgid "Icon"
msgstr ""

#: config/elementor/course/filter-course-el.php:222
msgid "Icon Position"
msgstr ""

#: config/elementor/course/filter-course-el.php:224
msgid "Before"
msgstr ""

#: config/elementor/course/filter-course-el.php:225
msgid "After"
msgstr ""

#: config/elementor/course/filter-course-el.php:237
msgid "Filter Selected Number"
msgstr ""

#: config/elementor/course/filter-course-el.php:278
msgid "Filter Selected List"
msgstr ""

#: config/elementor/course/filter-course-el.php:295
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:127
msgid "Section"
msgstr ""

#: config/elementor/course/filter-course-el.php:300
#: config/elementor/course/filter-course-el.php:358
#: inc/ExternalPlugin/Elementor/LPElementorControls.php:414
#: inc/ExternalPlugin/Elementor/LPElementorControls.php:548
msgid "Margin"
msgstr ""

#: config/elementor/course/filter-course-el.php:329
#: config/elementor/course/filter-course-el.php:387
msgid "Background"
msgstr ""

#: config/elementor/course/filter-course-el.php:334
#: config/elementor/course/filter-course-el.php:392
msgid "Radius"
msgstr ""

#: config/elementor/course/filter-course-el.php:353
#: inc/admin/class-lp-admin-assets.php:559
#: inc/admin/views/meta-boxes/order/content-tab-preview-exports-invoice.php:66
#: inc/admin/views/meta-boxes/order/details.php:209
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:140
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:231
#: templates/checkout/order-received.php:70
#: templates/order/order-details.php:28
msgid "Item"
msgstr ""

#: config/elementor/course/filter-course-el.php:404
msgid "Toggle Offset X (px)"
msgstr ""

#: config/elementor/course/filter-course-el.php:416
msgid "Toggle Offset Y (px)"
msgstr ""

#: config/elementor/course/filter-course-el.php:441
msgid "Label"
msgstr ""

#: config/elementor/course/filter-course-el.php:453
msgid "Horizontal Align"
msgstr ""

#: config/elementor/course/filter-course-el.php:459
#: config/elementor/course/filter-course-el.php:513
#: config/elementor/course/filter-course-el.php:569
#: config/elementor/course/filter-course-el.php:670
#: inc/lp-core-functions.php:864
msgid "Left"
msgstr ""

#: config/elementor/course/filter-course-el.php:463
#: config/elementor/course/filter-course-el.php:521
#: config/elementor/course/filter-course-el.php:577
#: config/elementor/course/filter-course-el.php:678
#: inc/lp-core-functions.php:865
msgid "Right"
msgstr ""

#: config/elementor/course/filter-course-el.php:477
msgid "Count"
msgstr ""

#: config/elementor/course/filter-course-el.php:517
#: config/elementor/course/filter-course-el.php:573
#: config/elementor/course/filter-course-el.php:674
msgid "Center"
msgstr ""

#: config/elementor/course/filter-course-el.php:607
msgid "Position"
msgstr ""

#: config/elementor/course/filter-course-el.php:609
msgid "Static"
msgstr ""

#: config/elementor/course/filter-course-el.php:610
msgid "Absolute"
msgstr ""

#: config/elementor/course/filter-course-el.php:621
msgid "Offset X (px)"
msgstr ""

#: config/elementor/course/filter-course-el.php:636
msgid "Offset Y (px)"
msgstr ""

#: config/elementor/course/filter-course-el.php:655
msgid "Button Popup"
msgstr ""

#: config/elementor/course/filter-course-el.php:712
msgid "Selected List"
msgstr ""

#: config/elementor/course/filter-course-el.php:729
msgid "Selected Number"
msgstr ""

#: config/elementor/course/list-courses-by-page.php:42
msgid "Skin"
msgstr ""

#: config/elementor/course/list-courses-by-page.php:46
msgid "Option load REST API"
msgstr ""

#: config/elementor/course/list-courses-by-page.php:80
#: config/elementor/course/list-courses.php:41
msgid "Layout"
msgstr ""

#: config/elementor/course/list-courses-by-page.php:111
msgid "Query"
msgstr ""

#: config/elementor/course/list-courses-by-page.php:116
msgid "Courses limit"
msgstr ""

#: config/elementor/course/list-courses-by-page.php:122
msgid "Total courses you want to query, default 0 is no limit"
msgstr ""

#: config/elementor/course/list-courses-by-page.php:127
msgid "Courses Per Page"
msgstr ""

#: config/elementor/course/list-courses-by-page.php:133
msgid "Number courses show on 1 page. Default 0 is show all of Courses Limit"
msgstr ""

#: config/elementor/course/list-courses-by-page.php:138
msgid "Order By Default"
msgstr ""

#: config/elementor/course/list-courses-by-page.php:140
msgid "Newest"
msgstr ""

#: config/elementor/course/list-courses-by-page.php:141
#: inc/TemplateHooks/Course/ListCoursesTemplate.php:538
msgid "Title a-z"
msgstr ""

#: config/elementor/course/list-courses-by-page.php:142
#: inc/TemplateHooks/Course/ListCoursesTemplate.php:539
msgid "Title z-a"
msgstr ""

#: config/elementor/course/list-courses-by-page.php:143
msgid "Price High to Low"
msgstr ""

#: config/elementor/course/list-courses-by-page.php:144
msgid "Price Low to High"
msgstr ""

#: config/elementor/course/list-courses-by-page.php:145
#: config/elementor/course/list-courses.php:74
#: inc/TemplateHooks/Course/ListCoursesTemplate.php:542
msgid "Popular"
msgstr ""

#: config/elementor/course/list-courses-by-page.php:151
#: config/elementor/course/list-courses.php:60
msgid "Select Categories"
msgstr ""

#: config/elementor/course/list-courses-by-page.php:168
msgid "Pagination type"
msgstr ""

#: config/elementor/course/list-courses-by-page.php:170
msgid "Nonce"
msgstr ""

#: config/elementor/course/list-courses-by-page.php:173
msgid "Infinite scroll"
msgstr ""

#: config/elementor/course/list-courses.php:36
#: config/elementor/instructor/become-a-teacher.php:15
#: config/elementor/instructor/list-instructors.php:178
#: config/elementor/instructor/single-instructor.php:16
#: inc/admin/views/meta-boxes/fields/extra-faq.php:42
#: inc/admin/views/meta-boxes/fields/extra-faq.php:62
#: inc/custom-post-types/course.php:353
#: inc/custom-post-types/course.php:370
#: inc/emails/class-lp-email.php:909
msgid "Content"
msgstr ""

#: config/elementor/course/list-courses.php:43
#: config/elementor/instructor/single-instructor.php:23
#: inc/lp-template-functions.php:1345
msgid "List"
msgstr ""

#: config/elementor/course/list-courses.php:44
#: config/elementor/instructor/single-instructor.php:24
#: inc/lp-template-functions.php:1344
msgid "Grid"
msgstr ""

#: config/elementor/course/list-courses.php:50
msgid "Select Courses"
msgstr ""

#: config/elementor/course/list-courses.php:70
msgid "Sort In"
msgstr ""

#: config/elementor/course/list-courses.php:73
msgid "Recent"
msgstr ""

#: config/elementor/course/list-courses.php:81
#: config/elementor/instructor/list-instructors.php:202
msgid "Order By"
msgstr ""

#: config/elementor/course/list-courses.php:83
msgid "DESC"
msgstr ""

#: config/elementor/course/list-courses.php:84
msgid "ASC"
msgstr ""

#: config/elementor/course/list-courses.php:90
#: config/elementor/instructor/list-instructors.php:211
#: inc/Widgets/featured-courses.php:43
#: inc/Widgets/popular-courses.php:39
#: inc/Widgets/recent-courses.php:44
msgid "Limit"
msgstr ""

#: config/elementor/course/list-courses.php:105
msgid "Title Course"
msgstr ""

#: config/elementor/course/list-courses.php:114
msgid "Title Instructor"
msgstr ""

#: config/elementor/course/list-courses.php:132
msgid "Course Image"
msgstr ""

#: config/elementor/course/materials.php:15
msgid "Course/Lesson Materials"
msgstr ""

#: config/elementor/course/materials.php:20
#: config/elementor/instructor/list-instructors.php:27
msgid "Add layout and drag to top to set Active"
msgstr ""

#: config/elementor/course/materials.php:32
msgid "Column Name"
msgstr ""

#: config/elementor/course/materials.php:38
msgid "Show Column"
msgstr ""

#: config/elementor/course/materials.php:53
msgid "Download Icon"
msgstr ""

#: config/elementor/course/materials.php:67
#: config/elementor/course/materials.php:72
#: config/elementor/course/materials.php:77
#: config/elementor/course/materials.php:82
msgid "yes"
msgstr ""

#: config/elementor/course/skin/courses-grid.php:26
#: config/elementor/course/skin/courses-loop-item.php:39
msgid "Columns"
msgstr ""

#: config/elementor/instructor/become-a-teacher.php:21
#: inc/ExternalPlugin/Elementor/Widgets/BecomeATeacherElementor.php:16
msgid "Become a teacher"
msgstr ""

#: config/elementor/instructor/become-a-teacher.php:25
#: config/elementor/instructor/become-a-teacher.php:55
#: inc/admin/meta-box/fields/list-emails.php:8
#: inc/admin/meta-box/fields/payment-order.php:9
#: inc/admin/views/quiz/question-meta.php:18
#: inc/admin/views/setup/setup-stripe.php:11
msgid "Description"
msgstr ""

#: config/elementor/instructor/become-a-teacher.php:26
#: inc/Shortcodes/class-lp-shortcode-become-a-teacher.php:88
msgid "Fill in your information and send it to us to become a teacher."
msgstr ""

#: config/elementor/instructor/become-a-teacher.php:31
msgid "Button text"
msgstr ""

#: config/elementor/instructor/become-a-teacher.php:32
#: inc/admin/views/addons.php:225
#: inc/Shortcodes/class-lp-shortcode-become-a-teacher.php:89
#: templates/global/become-teacher-form.php:58
msgid "Submit"
msgstr ""

#: config/elementor/instructor/become-a-teacher.php:36
msgid "Button Processing text"
msgstr ""

#: config/elementor/instructor/become-a-teacher.php:37
#: inc/class-lp-assets.php:135
#: inc/order/class-lp-order.php:286
#: inc/Shortcodes/class-lp-shortcode-become-a-teacher.php:90
msgid "Processing"
msgstr ""

#: config/elementor/instructor/become-a-teacher.php:64
#: config/elementor/login-user-form.php:28
#: config/elementor/register-user-form.php:28
msgid "Form Label"
msgstr ""

#: config/elementor/instructor/become-a-teacher.php:73
#: config/elementor/login-user-form.php:37
#: config/elementor/register-user-form.php:37
msgid "Form Input"
msgstr ""

#: config/elementor/instructor/list-instructors.php:22
msgid "Layout Item Instructor"
msgstr ""

#: config/elementor/instructor/list-instructors.php:39
msgid "Layout Name"
msgstr ""

#: config/elementor/instructor/list-instructors.php:45
msgid "Layout HTML"
msgstr ""

#: config/elementor/instructor/list-instructors.php:53
msgid "Ul Style"
msgstr ""

#: config/elementor/instructor/list-instructors.php:64
msgid "Column number"
msgstr ""

#: config/elementor/instructor/list-instructors.php:77
msgid "Gap"
msgstr ""

#: config/elementor/instructor/list-instructors.php:94
msgid "Item Padding"
msgstr ""

#: config/elementor/instructor/list-instructors.php:111
msgid "Item border"
msgstr ""

#: config/elementor/instructor/list-instructors.php:119
msgid "Item Border"
msgstr ""

#: config/elementor/instructor/list-instructors.php:144
#: inc/ExternalPlugin/Elementor/LPElementorControls.php:500
#: inc/ExternalPlugin/Elementor/LPElementorControls.php:577
msgid "Border Radius"
msgstr ""

#: config/elementor/instructor/list-instructors.php:154
msgid "Advanced Css"
msgstr ""

#: config/elementor/instructor/list-instructors.php:162
msgid "Custom CSS"
msgstr ""

#: config/elementor/instructor/list-instructors.php:204
msgid "Name a-z"
msgstr ""

#: config/elementor/instructor/list-instructors.php:205
msgid "Name z-a"
msgstr ""

#: config/elementor/instructor/sections/avatar.php:16
#: config/elementor/instructor/sections/button_view.php:16
#: config/elementor/instructor/sections/count_courses.php:16
#: config/elementor/instructor/sections/count_students.php:16
#: config/elementor/instructor/sections/description.php:16
#: config/elementor/instructor/sections/title.php:16
msgid "Wrapper"
msgstr ""

#: config/elementor/instructor/sections/avatar.php:21
#: config/elementor/instructor/sections/button_view.php:21
#: config/elementor/instructor/sections/count_courses.php:21
#: config/elementor/instructor/sections/count_students.php:21
#: config/elementor/instructor/sections/description.php:21
#: config/elementor/instructor/sections/title.php:21
msgid "Set Instructor ID"
msgstr ""

#: config/elementor/instructor/sections/avatar.php:37
msgid "Instructor avatar"
msgstr ""

#: config/elementor/instructor/sections/button_view.php:30
msgid "Add html tag wrapper Instructor Button View"
msgstr ""

#: config/elementor/instructor/sections/button_view.php:42
#: config/elementor/instructor/sections/description.php:42
#: config/elementor/instructor/sections/title.php:42
msgid "Html Open tag"
msgstr ""

#: config/elementor/instructor/sections/button_view.php:48
#: config/elementor/instructor/sections/description.php:48
#: config/elementor/instructor/sections/title.php:48
msgid "Html Close tag"
msgstr ""

#: config/elementor/instructor/sections/button_view.php:66
#: inc/ExternalPlugin/Elementor/Widgets/Instructor/Sections/InstructorButtonViewElementor.php:44
msgid "Instructor Button View"
msgstr ""

#: config/elementor/instructor/sections/count_courses.php:37
#: inc/ExternalPlugin/Elementor/Widgets/Instructor/Sections/InstructorCountCoursesElementor.php:20
#: inc/ExternalPlugin/Elementor/Widgets/Instructor/Sections/InstructorCountCoursesElementor.php:44
msgid "Instructor Count Courses"
msgstr ""

#: config/elementor/instructor/sections/count_students.php:37
#: inc/ExternalPlugin/Elementor/Widgets/Instructor/Sections/InstructorCountStudentsElementor.php:20
#: inc/ExternalPlugin/Elementor/Widgets/Instructor/Sections/InstructorCountStudentsElementor.php:44
msgid "Instructor Count Students"
msgstr ""

#: config/elementor/instructor/sections/description.php:30
msgid "Add html tag wrapper Instructor Description"
msgstr ""

#: config/elementor/instructor/sections/description.php:66
#: inc/ExternalPlugin/Elementor/Widgets/Instructor/Sections/InstructorDescriptionElementor.php:18
#: inc/ExternalPlugin/Elementor/Widgets/Instructor/Sections/InstructorDescriptionElementor.php:42
msgid "Instructor Description"
msgstr ""

#: config/elementor/instructor/sections/title.php:30
msgid "Add html tag wrapper Instructor Name"
msgstr ""

#: config/elementor/instructor/sections/title.php:66
msgid "Instructor name"
msgstr ""

#: config/elementor/instructor/single-instructor.php:20
msgid "Layout Type"
msgstr ""

#: config/profile-tabs.php:16
#: config/settings/permalink.php:87
#: inc/admin/class-lp-admin.php:320
#: inc/admin/lp-admin-actions.php:214
#: inc/admin/sub-menus/class-lp-submenu-statistics.php:22
#: inc/admin/views/tools/course/html-user.php:30
#: inc/class-lp-page-controller.php:243
#: inc/custom-post-types/course.php:53
#: inc/custom-post-types/course.php:55
#: inc/lp-template-functions.php:322
#: inc/settings/class-lp-settings-courses.php:16
#: inc/user/class-lp-profile.php:517
msgid "Courses"
msgstr ""

#: config/profile-tabs.php:23
#: config/settings/permalink.php:99
msgid "My Courses"
msgstr ""

#: config/profile-tabs.php:30
#: config/settings/permalink.php:111
#: inc/custom-post-types/quiz.php:87
#: inc/custom-post-types/quiz.php:88
#: inc/custom-post-types/quiz.php:92
#: inc/user/class-lp-profile.php:524
#: templates/widgets/course-info.php:30
msgid "Quizzes"
msgstr ""

#: config/profile-tabs.php:37
#: config/settings/permalink.php:119
#: inc/admin/sub-menus/class-lp-submenu-statistics.php:21
#: inc/custom-post-types/order.php:594
#: inc/custom-post-types/order.php:595
#: inc/custom-post-types/order.php:599
msgid "Orders"
msgstr ""

#: config/profile-tabs.php:44
#: config/settings/permalink.php:127
#: inc/admin/views/meta-boxes/order/content-tab-preview-exports-invoice.php:55
#: inc/admin/views/meta-boxes/order/details.php:44
msgid "Order details"
msgstr ""

#: config/profile-tabs.php:56
#: config/settings/course.php:36
#: config/settings/profile.php:30
#: inc/admin/settings/class-lp-settings-emails.php:63
#: inc/admin/settings/class-lp-settings-general.php:18
#: inc/admin/settings/class-lp-settings-payments.php:52
#: inc/admin/views/meta-boxes/course/settings.php:57
msgid "General"
msgstr ""

#: config/profile-tabs.php:63
msgid "Avatar"
msgstr ""

#: config/profile-tabs.php:70
msgid "Cover Image"
msgstr ""

#: config/profile-tabs.php:77
#: inc/jwt/rest-api/version1/class-lp-rest-users-v1-controller.php:121
#: templates/checkout/account-login.php:35
#: templates/checkout/account-login.php:36
#: templates/checkout/account-register.php:32
#: templates/checkout/account-register.php:33
#: templates/checkout/account-register.php:37
#: templates/global/form-login.php:31
#: templates/global/form-login.php:32
#: templates/global/form-register.php:36
#: templates/global/form-register.php:37
#: templates/global/form-register.php:41
msgid "Password"
msgstr ""

#: config/profile-tabs.php:88
msgid "Logout"
msgstr ""

#: config/profile-tabs.php:97
msgid "Privacy"
msgstr ""

#: config/settings/advanced.php:11
msgid "Style"
msgstr ""

#: config/settings/advanced.php:15
msgid "Width container"
msgstr ""

#: config/settings/advanced.php:16
msgid "With of container, Ex: 1140px, 80rem"
msgstr ""

#: config/settings/advanced.php:23
msgid "Primary color"
msgstr ""

#: config/settings/advanced.php:24
#: config/settings/advanced.php:34
msgid "Default: %s"
msgstr ""

#: config/settings/advanced.php:33
msgid "Secondary color"
msgstr ""

#: config/settings/advanced.php:48
#: config/settings/general.php:132
#: inc/admin/views/addons/html-themes.php:28
msgid "Other"
msgstr ""

#: config/settings/advanced.php:52
msgid "Enable gutenberg"
msgstr ""

#: config/settings/advanced.php:57
#: config/settings/permalink.php:36
#: inc/admin/lp-admin-functions.php:57
#: inc/admin/meta-box/fields/course-permalink.php:22
#: inc/class-lp-helper.php:551
#: inc/custom-post-types/abstract.php:878
#: inc/custom-post-types/quiz.php:240
#: inc/TemplateHooks/Instructor/SingleInstructorTemplate.php:64
#: templates/emails/order-items-table.php:87
msgid "Course"
msgid_plural "Courses"
msgstr[0] ""
msgstr[1] ""

#: config/settings/advanced.php:64
#: config/settings/permalink.php:42
#: inc/class-lp-helper.php:557
#: inc/course/lp-course-functions.php:411
#: inc/custom-post-types/lesson.php:176
#: inc/Gutenberg/Blocks/SingleCourseElements/CourseLessonBlockType.php:79
#: inc/Gutenberg/Blocks/SingleCourseElements/CourseOfflineLessonBlockType.php:80
#: inc/lp-template-functions.php:1103
#: inc/Models/CourseModel.php:1397
#: inc/TemplateHooks/Course/SingleCourseModernLayout.php:210
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:527
msgid "Lesson"
msgid_plural "Lessons"
msgstr[0] ""
msgstr[1] ""

#: config/settings/advanced.php:71
#: config/settings/permalink.php:50
#: inc/class-lp-helper.php:563
#: inc/course/lp-course-functions.php:412
#: inc/custom-post-types/question.php:313
#: inc/custom-post-types/quiz.php:89
#: inc/Gutenberg/Blocks/SingleCourseElements/CourseQuizBlockType.php:79
#: inc/lp-template-functions.php:1093
#: inc/Models/CourseModel.php:1400
#: inc/TemplateHooks/Course/SingleCourseModernLayout.php:218
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:530
#: inc/TemplateHooks/Profile/ProfileQuizzesTemplate.php:126
#: templates/profile/tabs/quizzes.php:56
msgid "Quiz"
msgid_plural "Quizzes"
msgstr[0] ""
msgstr[1] ""

#: config/settings/advanced.php:78
#: inc/class-lp-helper.php:569
#: inc/custom-post-types/question.php:207
#: inc/custom-post-types/quiz.php:177
msgid "Question"
msgid_plural "Questions"
msgstr[0] ""
msgstr[1] ""

#: config/settings/advanced.php:81
msgid "Debug Mode"
msgstr ""

#: config/settings/advanced.php:85
msgid "Enable debug mode for the developer."
msgstr ""

#: config/settings/course.php:8
msgid "The site will be redirected to the URL added after clicking the finish course button."
msgstr ""

#: config/settings/course.php:9
msgid "Set blank, the site will be redirected to the single course page"
msgstr ""

#: config/settings/course.php:40
msgid "Layout single course"
msgstr ""

#: config/settings/course.php:41
msgid "Layout default display for single course. (Not apply for theme Gutenberg)"
msgstr ""

#: config/settings/course.php:46
msgid "Modern"
msgstr ""

#: config/settings/course.php:47
msgid "Classic"
msgstr ""

#: config/settings/course.php:51
msgid "Review courses"
msgstr ""

#: config/settings/course.php:52
msgid "Courses created by instructors will be pending review first."
msgstr ""

#: config/settings/course.php:58
msgid "Auto start"
msgstr ""

#: config/settings/course.php:62
msgid "Students will get started on courses immediately after successfully purchasing them."
msgstr ""

#: config/settings/course.php:65
msgid "Confirmation popup"
msgstr ""

#: config/settings/course.php:66
msgid "Show a confirmation popup before finishing the course or completing the item."
msgstr ""

#: config/settings/course.php:72
msgid "Archive page layout"
msgstr ""

#: config/settings/course.php:73
msgid "Display the List course type on the Course Archive page."
msgstr ""

#: config/settings/course.php:80
msgid "Courses per page"
msgstr ""

#: config/settings/course.php:81
msgid "The number of displayed courses per page."
msgstr ""

#: config/settings/course.php:91
msgid "Include courses in subcategory"
msgstr ""

#: config/settings/course.php:92
msgid "Show all courses within the subcategory that have not been chosen in the parent category."
msgstr ""

#: config/settings/course.php:99
msgid "Loading ajax Courses"
msgstr ""

#: config/settings/course.php:100
msgid "On/Off <i>loading ajax courses on the Course Archive page </i>."
msgstr ""

#: config/settings/course.php:106
msgid "Do not run Ajax when reloading the Course Archive page"
msgstr ""

#: config/settings/course.php:107
msgid "Ajax is only applied when selecting pagination, filtering, searching, and sorting. Not applicable when reloading the Course Archive page."
msgstr ""

#: config/settings/course.php:115
msgid "Display the pagination type on the Course Archive page."
msgstr ""

#: config/settings/course.php:127
msgid "Thumbnail dimensions"
msgstr ""

#: config/settings/course.php:133
msgid "Redirect when finishing the course"
msgstr ""

#: config/settings/course.php:150
msgid "Curriculum Settings"
msgstr ""

#: config/settings/course.php:154
msgid "Curriculum display"
msgstr ""

#: config/settings/course.php:159
msgid "Expand first section"
msgstr ""

#: config/settings/course.php:160
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:1479
msgid "Expand all sections"
msgstr ""

#: config/settings/course.php:161
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:1483
msgid "Collapse all sections"
msgstr ""

#: config/settings/course.php:163
msgid "Currently, apply for the Modern single course layout only."
msgstr ""

#: config/settings/course.php:166
msgid "Section Per Page"
msgstr ""

#: config/settings/course.php:171
msgid "The number of displayed sections per page (Enter -1 to display all sections)."
msgstr ""

#: config/settings/course.php:174
msgid "Course Item Per Page"
msgstr ""

#: config/settings/course.php:179
msgid "The number of displayed course items per page in a section (Enter -1 to display all course items)."
msgstr ""

#: config/settings/course.php:192
#: inc/admin/views/meta-boxes/quiz/settings.php:15
msgid "Quiz Settings"
msgstr ""

#: config/settings/course.php:196
msgid "Sticky Quiz Paging"
msgstr ""

#: config/settings/course.php:200
msgid "The question's navigation position is sticky. If this option is disabled, the question navigation position will be below the quiz content"
msgstr ""

#: config/settings/course.php:213
msgid "Material Settings"
msgstr ""

#: config/settings/course.php:217
msgid "File Type"
msgstr ""

#: config/settings/course.php:223
msgid "Which types of file will be allowed uploading?"
msgstr ""

#: config/settings/course.php:263
msgid "Upload Files"
msgstr ""

#: config/settings/course.php:267
msgid "Number files the user can upload. Set to 0 to disable"
msgstr ""

#: config/settings/course.php:273
msgid "File Size Limit"
msgstr ""

#: config/settings/course.php:277
msgid "Set Maximum Attachment size for upload (MB)"
msgstr ""

#: config/settings/course.php:283
msgid "File Per Page"
msgstr ""

#: config/settings/course.php:287
msgid "The number of displayed files per page (Enter -1 to display all files, set to 0 to disable)."
msgstr ""

#: config/settings/course.php:293
msgid "Url nofollow"
msgstr ""

#: config/settings/course.php:294
msgid "Add rel=\"nofollow\" to material external link."
msgstr ""

#: config/settings/course.php:310
msgid "Instructor Settings"
msgstr ""

#: config/settings/course.php:314
msgid "Instructor Per Page"
msgstr ""

#: config/settings/course.php:318
msgid "The number of displayed instructors per page (Enter -1 to display all sections)."
msgstr ""

#: config/settings/course.php:321
msgid "Show Admin on list"
msgstr ""

#: config/settings/course.php:325
msgid "Show author Admin on list instructors."
msgstr ""

#: config/settings/currencies.php:9
msgid "United Arab Emirates dirham"
msgstr ""

#: config/settings/currencies.php:10
msgid "Afghan afghani"
msgstr ""

#: config/settings/currencies.php:11
msgid "Albanian lek"
msgstr ""

#: config/settings/currencies.php:12
msgid "Armenian dram"
msgstr ""

#: config/settings/currencies.php:13
msgid "Netherlands Antillean guilder"
msgstr ""

#: config/settings/currencies.php:14
msgid "Angolan kwanza"
msgstr ""

#: config/settings/currencies.php:15
msgid "Argentine peso"
msgstr ""

#: config/settings/currencies.php:16
msgid "Australian dollar"
msgstr ""

#: config/settings/currencies.php:17
msgid "Aruban florin"
msgstr ""

#: config/settings/currencies.php:18
msgid "Azerbaijani manat"
msgstr ""

#: config/settings/currencies.php:19
msgid "Bosnia and Herzegovina convertible mark"
msgstr ""

#: config/settings/currencies.php:20
msgid "Barbadian dollar"
msgstr ""

#: config/settings/currencies.php:21
msgid "Bangladeshi taka"
msgstr ""

#: config/settings/currencies.php:22
msgid "Bulgarian lev"
msgstr ""

#: config/settings/currencies.php:23
msgid "Bahraini dinar"
msgstr ""

#: config/settings/currencies.php:24
msgid "Burundian franc"
msgstr ""

#: config/settings/currencies.php:25
msgid "Bermudian dollar"
msgstr ""

#: config/settings/currencies.php:26
msgid "Brunei dollar"
msgstr ""

#: config/settings/currencies.php:27
msgid "Bolivian boliviano"
msgstr ""

#: config/settings/currencies.php:28
msgid "Brazilian real"
msgstr ""

#: config/settings/currencies.php:29
msgid "Bahamian dollar"
msgstr ""

#: config/settings/currencies.php:30
msgid "Bitcoin"
msgstr ""

#: config/settings/currencies.php:31
msgid "Bhutanese ngultrum"
msgstr ""

#: config/settings/currencies.php:32
msgid "Botswana pula"
msgstr ""

#: config/settings/currencies.php:33
msgid "Belarusian ruble (old)"
msgstr ""

#: config/settings/currencies.php:34
msgid "Belarusian ruble"
msgstr ""

#: config/settings/currencies.php:35
msgid "Belize dollar"
msgstr ""

#: config/settings/currencies.php:36
msgid "Canadian dollar"
msgstr ""

#: config/settings/currencies.php:37
msgid "Congolese franc"
msgstr ""

#: config/settings/currencies.php:38
msgid "Swiss franc"
msgstr ""

#: config/settings/currencies.php:39
msgid "Chilean peso"
msgstr ""

#: config/settings/currencies.php:40
msgid "Chinese yuan"
msgstr ""

#: config/settings/currencies.php:41
msgid "Colombian peso"
msgstr ""

#: config/settings/currencies.php:42
msgid "Costa Rican col&oacute;n"
msgstr ""

#: config/settings/currencies.php:43
msgid "Cuban convertible peso"
msgstr ""

#: config/settings/currencies.php:44
msgid "Cuban peso"
msgstr ""

#: config/settings/currencies.php:45
msgid "Cape Verdean escudo"
msgstr ""

#: config/settings/currencies.php:46
msgid "Czech koruna"
msgstr ""

#: config/settings/currencies.php:47
msgid "Djiboutian franc"
msgstr ""

#: config/settings/currencies.php:48
msgid "Danish krone"
msgstr ""

#: config/settings/currencies.php:49
msgid "Dominican peso"
msgstr ""

#: config/settings/currencies.php:50
msgid "Algerian dinar"
msgstr ""

#: config/settings/currencies.php:51
msgid "Egyptian pound"
msgstr ""

#: config/settings/currencies.php:52
msgid "Eritrean nakfa"
msgstr ""

#: config/settings/currencies.php:53
msgid "Ethiopian birr"
msgstr ""

#: config/settings/currencies.php:54
msgid "Euro"
msgstr ""

#: config/settings/currencies.php:55
msgid "Fijian dollar"
msgstr ""

#: config/settings/currencies.php:56
msgid "Falkland Islands pound"
msgstr ""

#: config/settings/currencies.php:57
msgid "Pound sterling"
msgstr ""

#: config/settings/currencies.php:58
msgid "Georgian lari"
msgstr ""

#: config/settings/currencies.php:59
msgid "Guernsey pound"
msgstr ""

#: config/settings/currencies.php:60
msgid "Ghana cedi"
msgstr ""

#: config/settings/currencies.php:61
msgid "Gibraltar pound"
msgstr ""

#: config/settings/currencies.php:62
msgid "Gambian dalasi"
msgstr ""

#: config/settings/currencies.php:63
msgid "Guinean franc"
msgstr ""

#: config/settings/currencies.php:64
msgid "Guatemalan quetzal"
msgstr ""

#: config/settings/currencies.php:65
msgid "Guyanese dollar"
msgstr ""

#: config/settings/currencies.php:66
msgid "Hong Kong dollar"
msgstr ""

#: config/settings/currencies.php:67
msgid "Honduran lempira"
msgstr ""

#: config/settings/currencies.php:68
msgid "Croatian kuna"
msgstr ""

#: config/settings/currencies.php:69
msgid "Haitian gourde"
msgstr ""

#: config/settings/currencies.php:70
msgid "Hungarian forint"
msgstr ""

#: config/settings/currencies.php:71
msgid "Indonesian rupiah"
msgstr ""

#: config/settings/currencies.php:72
msgid "Israeli new shekel"
msgstr ""

#: config/settings/currencies.php:73
msgid "Manx pound"
msgstr ""

#: config/settings/currencies.php:74
msgid "Indian rupee"
msgstr ""

#: config/settings/currencies.php:75
msgid "Iraqi dinar"
msgstr ""

#: config/settings/currencies.php:76
msgid "Iranian rial"
msgstr ""

#: config/settings/currencies.php:77
msgid "Iranian toman"
msgstr ""

#: config/settings/currencies.php:78
msgid "Icelandic kr&oacute;na"
msgstr ""

#: config/settings/currencies.php:79
msgid "Jersey pound"
msgstr ""

#: config/settings/currencies.php:80
msgid "Jamaican dollar"
msgstr ""

#: config/settings/currencies.php:81
msgid "Jordanian dinar"
msgstr ""

#: config/settings/currencies.php:82
msgid "Japanese yen"
msgstr ""

#: config/settings/currencies.php:83
msgid "Kenyan shilling"
msgstr ""

#: config/settings/currencies.php:84
msgid "Kyrgyzstani som"
msgstr ""

#: config/settings/currencies.php:85
msgid "Cambodian riel"
msgstr ""

#: config/settings/currencies.php:86
msgid "Comorian franc"
msgstr ""

#: config/settings/currencies.php:87
msgid "North Korean won"
msgstr ""

#: config/settings/currencies.php:88
msgid "South Korean won"
msgstr ""

#: config/settings/currencies.php:89
msgid "Kuwaiti dinar"
msgstr ""

#: config/settings/currencies.php:90
msgid "Cayman Islands dollar"
msgstr ""

#: config/settings/currencies.php:91
msgid "Kazakhstani tenge"
msgstr ""

#: config/settings/currencies.php:92
msgid "Lao kip"
msgstr ""

#: config/settings/currencies.php:93
msgid "Lebanese pound"
msgstr ""

#: config/settings/currencies.php:94
msgid "Sri Lankan rupee"
msgstr ""

#: config/settings/currencies.php:95
msgid "Liberian dollar"
msgstr ""

#: config/settings/currencies.php:96
msgid "Lesotho loti"
msgstr ""

#: config/settings/currencies.php:97
msgid "Libyan dinar"
msgstr ""

#: config/settings/currencies.php:98
msgid "Moroccan dirham"
msgstr ""

#: config/settings/currencies.php:99
msgid "Moldovan leu"
msgstr ""

#: config/settings/currencies.php:100
msgid "Malagasy ariary"
msgstr ""

#: config/settings/currencies.php:101
msgid "Macedonian denar"
msgstr ""

#: config/settings/currencies.php:102
msgid "Burmese kyat"
msgstr ""

#: config/settings/currencies.php:103
msgid "Mongolian t&ouml;gr&ouml;g"
msgstr ""

#: config/settings/currencies.php:104
msgid "Macanese pataca"
msgstr ""

#: config/settings/currencies.php:105
msgid "Mauritanian ouguiya"
msgstr ""

#: config/settings/currencies.php:106
msgid "Mauritian rupee"
msgstr ""

#: config/settings/currencies.php:107
msgid "Maldivian rufiyaa"
msgstr ""

#: config/settings/currencies.php:108
msgid "Malawian kwacha"
msgstr ""

#: config/settings/currencies.php:109
msgid "Mexican peso"
msgstr ""

#: config/settings/currencies.php:110
msgid "Malaysian ringgit"
msgstr ""

#: config/settings/currencies.php:111
msgid "Mozambican metical"
msgstr ""

#: config/settings/currencies.php:112
msgid "Namibian dollar"
msgstr ""

#: config/settings/currencies.php:113
msgid "Nigerian naira"
msgstr ""

#: config/settings/currencies.php:114
msgid "Nicaraguan c&oacute;rdoba"
msgstr ""

#: config/settings/currencies.php:115
msgid "Norwegian krone"
msgstr ""

#: config/settings/currencies.php:116
msgid "Nepalese rupee"
msgstr ""

#: config/settings/currencies.php:117
msgid "New Zealand dollar"
msgstr ""

#: config/settings/currencies.php:118
msgid "Omani rial"
msgstr ""

#: config/settings/currencies.php:119
msgid "Panamanian balboa"
msgstr ""

#: config/settings/currencies.php:120
msgid "Sol"
msgstr ""

#: config/settings/currencies.php:121
msgid "Papua New Guinean kina"
msgstr ""

#: config/settings/currencies.php:122
msgid "Philippine peso"
msgstr ""

#: config/settings/currencies.php:123
msgid "Pakistani rupee"
msgstr ""

#: config/settings/currencies.php:124
msgid "Polish z&#x142;oty"
msgstr ""

#: config/settings/currencies.php:125
msgid "Transnistrian ruble"
msgstr ""

#: config/settings/currencies.php:126
msgid "Paraguayan guaran&iacute;"
msgstr ""

#: config/settings/currencies.php:127
msgid "Qatari riyal"
msgstr ""

#: config/settings/currencies.php:128
msgid "Romanian leu"
msgstr ""

#: config/settings/currencies.php:129
msgid "Serbian dinar"
msgstr ""

#: config/settings/currencies.php:130
msgid "Russian ruble"
msgstr ""

#: config/settings/currencies.php:131
msgid "Rwandan franc"
msgstr ""

#: config/settings/currencies.php:132
msgid "Saudi riyal"
msgstr ""

#: config/settings/currencies.php:133
msgid "Solomon Islands dollar"
msgstr ""

#: config/settings/currencies.php:134
msgid "Seychellois rupee"
msgstr ""

#: config/settings/currencies.php:135
msgid "Sudanese pound"
msgstr ""

#: config/settings/currencies.php:136
msgid "Swedish krona"
msgstr ""

#: config/settings/currencies.php:137
msgid "Singapore dollar"
msgstr ""

#: config/settings/currencies.php:138
msgid "Saint Helena pound"
msgstr ""

#: config/settings/currencies.php:139
msgid "Sierra Leonean leone"
msgstr ""

#: config/settings/currencies.php:140
msgid "Somali shilling"
msgstr ""

#: config/settings/currencies.php:141
msgid "Surinamese dollar"
msgstr ""

#: config/settings/currencies.php:142
msgid "South Sudanese pound"
msgstr ""

#: config/settings/currencies.php:143
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe dobra"
msgstr ""

#: config/settings/currencies.php:144
msgid "Syrian pound"
msgstr ""

#: config/settings/currencies.php:145
msgid "Swazi lilangeni"
msgstr ""

#: config/settings/currencies.php:146
msgid "Thai baht"
msgstr ""

#: config/settings/currencies.php:147
msgid "Tajikistani somoni"
msgstr ""

#: config/settings/currencies.php:148
msgid "Turkmenistan manat"
msgstr ""

#: config/settings/currencies.php:149
msgid "Tunisian dinar"
msgstr ""

#: config/settings/currencies.php:150
msgid "Tongan pa&#x2bb;anga"
msgstr ""

#: config/settings/currencies.php:151
msgid "Turkish lira"
msgstr ""

#: config/settings/currencies.php:152
msgid "Trinidad and Tobago dollar"
msgstr ""

#: config/settings/currencies.php:153
msgid "New Taiwan dollar"
msgstr ""

#: config/settings/currencies.php:154
msgid "Tanzanian shilling"
msgstr ""

#: config/settings/currencies.php:155
msgid "Ukrainian hryvnia"
msgstr ""

#: config/settings/currencies.php:156
msgid "Ugandan shilling"
msgstr ""

#: config/settings/currencies.php:157
msgid "United States (US) dollar"
msgstr ""

#: config/settings/currencies.php:158
msgid "Uruguayan peso"
msgstr ""

#: config/settings/currencies.php:159
msgid "Uzbekistani som"
msgstr ""

#: config/settings/currencies.php:160
msgid "Venezuelan bol&iacute;var (2008–2018)"
msgstr ""

#: config/settings/currencies.php:161
msgid "Venezuelan bol&iacute;var"
msgstr ""

#: config/settings/currencies.php:162
msgid "Vietnamese &#x111;&#x1ed3;ng"
msgstr ""

#: config/settings/currencies.php:163
msgid "Vanuatu vatu"
msgstr ""

#: config/settings/currencies.php:164
msgid "Samoan t&#x101;l&#x101;"
msgstr ""

#: config/settings/currencies.php:165
msgid "Central African CFA franc"
msgstr ""

#: config/settings/currencies.php:166
msgid "East Caribbean dollar"
msgstr ""

#: config/settings/currencies.php:167
msgid "West African CFA franc"
msgstr ""

#: config/settings/currencies.php:168
msgid "CFP franc"
msgstr ""

#: config/settings/currencies.php:169
msgid "Yemeni rial"
msgstr ""

#: config/settings/currencies.php:170
msgid "South African rand"
msgstr ""

#: config/settings/currencies.php:171
msgid "Zambian kwacha"
msgstr ""

#: config/settings/gateway/offline-payment.php:20
#: inc/admin/views/setup/setup-stripe.php:3
msgid "Enable"
msgstr ""

#: config/settings/gateway/offline-payment.php:26
msgid "Testing Mode"
msgstr ""

#: config/settings/gateway/offline-payment.php:30
msgid "Auto complete the order for testing purpose."
msgstr ""

#: config/settings/gateway/offline-payment.php:33
msgid "Instruction"
msgstr ""

#: config/settings/gateway/paypal.php:13
#: inc/admin/meta-box/fields/payment-order.php:10
#: inc/emails/class-lp-email.php:877
msgid "Enable/Disable"
msgstr ""

#: config/settings/gateway/paypal.php:17
msgid "Enable PayPal"
msgstr ""

#: config/settings/gateway/paypal.php:26
msgid "Sandbox mode"
msgstr ""

#: config/settings/gateway/paypal.php:30
msgid "Enable PayPal sandbox"
msgstr ""

#: config/settings/gateway/paypal.php:46
msgid "Client ID"
msgstr ""

#: config/settings/gateway/paypal.php:50
msgid "How to get <a href=\"%s\" target=\"_blank\">Client ID</a>"
msgstr ""

#: config/settings/gateway/paypal.php:55
msgid "Client Secret"
msgstr ""

#: config/settings/gateway/paypal.php:59
msgid "How to get <a href=\"%s\" target=\"_blank\">Client Secret</a>"
msgstr ""

#: config/settings/general.php:19
msgid "Pages setup"
msgstr ""

#: config/settings/general.php:23
msgid "All courses page"
msgstr ""

#: config/settings/general.php:30
msgid "All instructors page"
msgstr ""

#: config/settings/general.php:37
msgid "Single instructor page"
msgstr ""

#: config/settings/general.php:44
msgid "Profile page"
msgstr ""

#: config/settings/general.php:51
msgid "Checkout page"
msgstr ""

#: config/settings/general.php:58
msgid "Become an instructors page"
msgstr ""

#: config/settings/general.php:65
msgid "Terms and conditions page"
msgstr ""

#: config/settings/general.php:72
msgid "Logout Redirect"
msgstr ""

#: config/settings/general.php:76
msgid "The page where the user will be redirected to after logging out."
msgstr ""

#: config/settings/general.php:83
#: config/settings/general.php:88
#: inc/admin/views/setup/setup-paypal.php:29
#: inc/admin/views/setup/steps/currency.php:18
#: inc/admin/views/setup/steps/currency.php:22
msgid "Currency"
msgstr ""

#: config/settings/general.php:85
msgid "Setting up your currency unit and its formatting."
msgstr ""

#: config/settings/general.php:97
#: inc/admin/views/setup/steps/currency.php:42
msgid "Currency position"
msgstr ""

#: config/settings/general.php:98
msgid "This controls the position of the currency symbol."
msgstr ""

#: config/settings/general.php:105
msgid "Thousands separator"
msgstr ""

#: config/settings/general.php:106
msgid "This sets the thousands separator of displayed prices."
msgstr ""

#: config/settings/general.php:113
#: inc/admin/views/setup/steps/currency.php:64
msgid "Decimal separator"
msgstr ""

#: config/settings/general.php:114
msgid "This sets the decimal separator of displayed prices."
msgstr ""

#: config/settings/general.php:121
#: inc/admin/views/setup/steps/currency.php:71
msgid "The number of decimals"
msgstr ""

#: config/settings/general.php:122
msgid "This sets the number of decimal points shown in the displayed prices."
msgstr ""

#: config/settings/general.php:136
msgid "Publish profile"
msgstr ""

#: config/settings/general.php:140
msgid "This option will add a sub-item \\\"Privacy\\\" under the Setting tab on the Profile page. If users want to publish or hide their course, or quiz tab when other users visit their profile page, they need to enable/disable that option in the Privacy section."
msgstr ""

#: config/settings/general.php:141
msgid "Public all user profile pages (only the overview tab)."
msgstr ""

#: config/settings/general.php:144
msgid "Instructor registration"
msgstr ""

#: config/settings/general.php:145
msgid "Enable the option in all registration forms."
msgstr ""

#: config/settings/general.php:151
msgid "Store IP Guest to handle checkout"
msgstr ""

#: config/settings/general.php:152
msgid "Enable the option, IP of client is identifier user instead $_COOKIE"
msgstr ""

#: config/settings/permalink.php:32
msgid "Permalinks Course"
msgstr ""

#: config/settings/permalink.php:58
msgid "Category base"
msgstr ""

#: config/settings/permalink.php:66
msgid "Tag base"
msgstr ""

#: config/settings/permalink.php:82
msgid "Permalinks Profile"
msgstr ""

#: config/settings/permalink.php:94
msgid "Courses created by user"
msgstr ""

#: config/settings/permalink.php:106
msgid "Courses enrolled by user"
msgstr ""

#: config/settings/permalink.php:149
msgid "Basic Information <small>Settings</small>"
msgstr ""

#: config/settings/permalink.php:157
msgid "Avatar <small>Settings</small>"
msgstr ""

#: config/settings/permalink.php:165
msgid "Change Password <small>Settings</small>"
msgstr ""

#: config/settings/permalink.php:173
msgid "Privacy <small>Settings</small>"
msgstr ""

#: config/settings/profile.php:35
msgid "Avatar Dimensions"
msgstr ""

#: config/settings/profile.php:41
msgid "Cover Image Dimensions"
msgstr ""

#: config/settings/profile.php:47
msgid "Enable login form"
msgstr ""

#: config/settings/profile.php:51
msgid "If the user is not logged in, enable login from profile."
msgstr ""

#: config/settings/profile.php:54
msgid "Enable register form"
msgstr ""

#: config/settings/profile.php:58
msgid "If the user is not logged in, enable register from profile."
msgstr ""

#: config/settings/profile.php:61
msgid "Enable default fields"
msgstr ""

#: config/settings/profile.php:66
#: inc/user/lp-user-functions.php:1743
#: inc/user/lp-user-functions.php:1745
#: templates/profile/tabs/settings/basic-information.php:33
msgid "First name"
msgstr ""

#: config/settings/profile.php:73
#: inc/user/lp-user-functions.php:1756
#: inc/user/lp-user-functions.php:1758
#: templates/profile/tabs/settings/basic-information.php:39
msgid "Last name"
msgstr ""

#: config/settings/profile.php:80
#: config/settings/profile.php:92
#: inc/user/lp-user-functions.php:1769
#: inc/user/lp-user-functions.php:1771
#: templates/profile/tabs/settings/basic-information.php:45
msgid "Display name"
msgstr ""

#: config/settings/profile.php:83
msgid "Custom register fields"
msgstr ""

#: config/settings/profile.php:91
msgid "Display field name."
msgstr ""

#: config/settings/profile.php:98
msgid "Text"
msgstr ""

#: config/settings/profile.php:99
msgid "Textarea"
msgstr ""

#: config/settings/profile.php:100
msgid "Checkbox"
msgstr ""

#: config/settings/profile.php:101
msgid "URL"
msgstr ""

#: config/settings/profile.php:106
msgid "Required?"
msgstr ""

#: config/settings/profile.php:110
msgid "Custom fields to the registration form."
msgstr ""

#: config/widgets/course/filter-course.php:11
msgid "Course Filter"
msgstr ""

#: config/widgets/course/filter-course.php:20
msgid "Class of list courses want to filter"
msgstr ""

#: config/widgets/course/filter-course.php:73
msgid "Type (Online/Offline)"
msgstr ""

#: inc/abstract-settings.php:89
#: inc/settings/abstract-settings-page.php:52
msgid "No settings available."
msgstr ""

#: inc/abstracts/abstract-addon.php:266
msgid "<strong>LearnPress version %1$s require %2$s</strong> version %3$s or higher"
msgstr ""

#: inc/abstracts/abstract-addon.php:287
msgid "<strong>%1$s</strong> add-on version %2$s requires <strong>LearnPress</strong> version %3$s or higher %4$s"
msgstr ""

#: inc/abstracts/abstract-addon.php:354
msgid "%s plugin file does not exist."
msgstr ""

#: inc/abstracts/abstract-addon.php:373
msgid "%s plugin class does not exist."
msgstr ""

#: inc/abstracts/abstract-object-data.php:346
msgid "The following functions %1$s do not exist in %2$s"
msgstr ""

#: inc/admin/class-lp-admin-ajax.php:188
msgid "Nonce is invalid!"
msgstr ""

#: inc/admin/class-lp-admin-ajax.php:195
msgid "Oops! ID not found"
msgstr ""

#: inc/admin/class-lp-admin-ajax.php:206
msgid "You cannot duplicate this item."
msgstr ""

#: inc/admin/class-lp-admin-ajax.php:248
msgid "Duplicate post failed. Please try again"
msgstr ""

#: inc/admin/class-lp-admin-ajax.php:469
msgid "Access denied"
msgstr ""

#: inc/admin/class-lp-admin-ajax.php:475
msgid "Nonce check failed"
msgstr ""

#: inc/admin/class-lp-admin-ajax.php:481
#: inc/admin/class-lp-admin-ajax.php:552
#: templates/order/order-details.php:18
msgid "Invalid order"
msgstr ""

#: inc/admin/class-lp-admin-ajax.php:487
#: inc/admin/class-lp-admin-ajax.php:558
msgid "Invalid item"
msgstr ""

#: inc/admin/class-lp-admin-ajax.php:546
msgid "Permission denied"
msgstr ""

#: inc/admin/class-lp-admin-ajax.php:665
#: inc/admin/class-lp-admin-ajax.php:684
msgid "Edit Page"
msgstr ""

#: inc/admin/class-lp-admin-ajax.php:666
#: inc/admin/class-lp-admin-ajax.php:686
msgid "View Page"
msgstr ""

#: inc/admin/class-lp-admin-ajax.php:668
msgid "Error! Page creation failed. Please try again."
msgstr ""

#: inc/admin/class-lp-admin-ajax.php:671
msgid "Empty page name!"
msgstr ""

#: inc/admin/class-lp-admin-assets.php:64
msgid "Select page"
msgstr ""

#: inc/admin/class-lp-admin-assets.php:96
msgid "Oops! Error."
msgstr ""

#: inc/admin/class-lp-admin-assets.php:97
#: inc/admin/views/meta-boxes/order/details.php:147
#: inc/emails/guest/class-lp-email-cancelled-order-guest.php:25
#: inc/emails/guest/class-lp-email-completed-order-guest.php:29
#: inc/emails/guest/class-lp-email-new-order-guest.php:25
#: inc/emails/guest/class-lp-email-processing-order-guest.php:22
#: inc/emails/types/class-lp-email-type-order-admin.php:47
msgid "Guest"
msgstr ""

#: inc/admin/class-lp-admin-assets.php:103
msgid "Before taking this action, we strongly recommend you backup your site first before proceeding. If you encounter any problems, please do not hesitate to contact our support team. Are you sure to proceed with the update protocol?"
msgstr ""

#: inc/admin/class-lp-admin-assets.php:552
#: inc/class-lp-query-list-table.php:32
#: inc/TemplateHooks/Table/TableListTemplate.php:138
msgid "item"
msgid_plural "items"
msgstr[0] ""
msgstr[1] ""

#: inc/admin/class-lp-admin-assets.php:553
msgid "Create a new"
msgstr ""

#: inc/admin/class-lp-admin-assets.php:554
#: inc/admin/views/setup/content.php:50
#: inc/custom-post-types/quiz.php:172
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:559
msgid "Back"
msgstr ""

#: inc/admin/class-lp-admin-assets.php:555
#: inc/admin/views/course/modal-choose-items.php:54
#: inc/admin/views/quiz/modal-choose-items.php:52
#: inc/custom-post-types/quiz.php:173
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:554
msgid "Selected items"
msgstr ""

#: inc/admin/class-lp-admin-assets.php:556
msgid "Do you want to remove the \"{{ITEM_NAME}}\" item from the course?"
msgstr ""

#: inc/admin/class-lp-admin-assets.php:557
msgid "Do you want to move the \"{{ITEM_NAME}}\" item to the trash?"
msgstr ""

#: inc/admin/class-lp-admin-assets.php:560
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:141
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:232
msgid "Items"
msgstr ""

#: inc/admin/class-lp-admin-assets.php:562
msgid "The course sale price must be less than the regular price"
msgstr ""

#: inc/admin/class-lp-admin-assets.php:563
msgid "The course price must be greater than the sale price"
msgstr ""

#: inc/admin/class-lp-admin-assets.php:564
msgid "The sale start date must be before the sale end date"
msgstr ""

#: inc/admin/class-lp-admin-assets.php:565
msgid "The sale end date must be after the sale start date"
msgstr ""

#: inc/admin/class-lp-admin-assets.php:566
msgid "Invalid date"
msgstr ""

#: inc/admin/class-lp-admin-dashboard.php:21
msgid "LearnPress order status"
msgstr ""

#: inc/admin/class-lp-admin-dashboard.php:74
#: inc/admin/views/addons/html-loop-theme.php:32
msgid " sales"
msgstr ""

#: inc/admin/class-lp-admin-dashboard.php:81
msgid "Created by: "
msgstr ""

#: inc/admin/class-lp-admin-menu.php:62
msgid "View Page Courses"
msgstr ""

#: inc/admin/class-lp-admin-menu.php:67
msgid "View Page Profile"
msgstr ""

#: inc/admin/class-lp-admin-menu.php:72
msgid "View Page Instructors"
msgstr ""

#: inc/admin/class-lp-admin-menu.php:105
msgid "Learning Management System"
msgstr ""

#: inc/admin/class-lp-admin.php:98
msgid "Available Courses"
msgstr ""

#: inc/admin/class-lp-admin.php:222
msgid "Shop Page"
msgstr ""

#: inc/admin/class-lp-admin.php:226
msgid "Cart Page"
msgstr ""

#: inc/admin/class-lp-admin.php:230
msgid "Checkout Page"
msgstr ""

#: inc/admin/class-lp-admin.php:234
msgid "My Account Page"
msgstr ""

#: inc/admin/class-lp-admin.php:238
msgid "Terms and Conditions Page"
msgstr ""

#: inc/admin/class-lp-admin.php:255
msgid "Account"
msgstr ""

#: inc/admin/class-lp-admin.php:256
msgid "Billing"
msgstr ""

#: inc/admin/class-lp-admin.php:258
#: inc/admin/class-lp-admin.php:324
msgid "Checkout"
msgstr ""

#: inc/admin/class-lp-admin.php:259
msgid "Confirmation"
msgstr ""

#: inc/admin/class-lp-admin.php:260
#: inc/admin/views/meta-boxes/order/content-tab-preview-exports-invoice.php:25
msgid "Invoice"
msgstr ""

#: inc/admin/class-lp-admin.php:261
#: inc/TemplateHooks/Course/FilterCourseTemplate.php:681
msgid "Levels"
msgstr ""

#: inc/admin/class-lp-admin.php:289
msgid "Members"
msgstr ""

#: inc/admin/class-lp-admin.php:290
msgid "Activity"
msgstr ""

#: inc/admin/class-lp-admin.php:291
#: templates/global/form-register.php:54
msgid "Register"
msgstr ""

#: inc/admin/class-lp-admin.php:292
#: inc/admin/views/addons.php:210
msgid "Activate"
msgstr ""

#: inc/admin/class-lp-admin.php:321
#: inc/admin/views/statistics/users.php:31
msgid "Instructors"
msgstr ""

#: inc/admin/class-lp-admin.php:322
msgid "Single Instructors"
msgstr ""

#: inc/admin/class-lp-admin.php:323
#: inc/admin/settings/class-lp-settings-profile.php:26
#: inc/Shortcodes/class-lp-shortcode-profile.php:85
msgid "Profile"
msgstr ""

#: inc/admin/class-lp-admin.php:325
#: inc/admin/settings/email-groups/class-lp-settings-become-teacher-emails.php:23
msgid "Become an Instructor"
msgstr ""

#: inc/admin/class-lp-admin.php:369
msgid "LearnPress Page"
msgstr ""

#: inc/admin/class-lp-admin.php:418
msgid "LearnPress Pages (%d)"
msgstr ""

#: inc/admin/class-lp-admin.php:474
msgctxt "pending-request"
msgid "Accept"
msgstr ""

#: inc/admin/class-lp-admin.php:479
msgctxt "pending-request"
msgid "Deny"
msgstr ""

#: inc/admin/class-lp-admin.php:580
msgid "Pending Request %s"
msgstr ""

#: inc/admin/class-lp-admin.php:648
msgid "A user has %s to become a teacher."
msgstr ""

#: inc/admin/class-lp-admin.php:685
msgid "If you like <strong>LearnPress</strong> please leave us a %1$s&#9733;&#9733;&#9733;&#9733;&#9733;%2$s rating. A huge thanks from the LearnPress team for your generosity."
msgstr ""

#: inc/admin/class-lp-admin.php:689
msgid "Thanks :)"
msgstr ""

#: inc/admin/class-lp-admin.php:910
msgid "Permalink is only available if the item is already assigned to a course."
msgstr ""

#: inc/admin/class-lp-install-sample-data.php:71
msgid "Are you sure you want to install the sample course data?"
msgstr ""

#: inc/admin/class-lp-install-sample-data.php:72
msgid "Are you sure you want to delete the sample course data?"
msgstr ""

#: inc/admin/class-lp-install-sample-data.php:154
msgid "The Course \"%s\" has been created"
msgstr ""

#: inc/admin/class-lp-install-sample-data.php:155
#: inc/admin/views/meta-boxes/course/assigned.php:23
#: inc/admin/views/meta-boxes/quiz/assigned.php:35
#: inc/admin/views/meta-boxes/quiz/assigned.php:44
#: inc/admin/views/user/courses.php:23
#: inc/custom-post-types/abstract.php:907
#: inc/custom-post-types/abstract.php:1025
#: inc/custom-post-types/abstract.php:1035
#: inc/custom-post-types/question.php:359
#: inc/order/class-lp-order.php:975
msgid "View"
msgstr ""

#: inc/admin/class-lp-install-sample-data.php:183
msgid "No data sample."
msgstr ""

#: inc/admin/class-lp-install-sample-data.php:204
msgid "The sample data was successfully deleted!"
msgstr ""

#: inc/admin/class-lp-install-sample-data.php:351
#: inc/admin/views/tools/course/html-install-sample-data.php:27
msgid "Sample course"
msgstr ""

#: inc/admin/class-lp-install-sample-data.php:693
msgctxt "install-sample-course"
msgid "[TRUE] - "
msgstr ""

#: inc/admin/class-lp-install-sample-data.php:701
msgctxt "install-sample-course"
msgid " [TRUE] - "
msgstr ""

#: inc/admin/class-lp-modal-search-items.php:60
#: inc/admin/class-lp-modal-search-users.php:50
#: inc/admin/views/course/modal-choose-items.php:92
#: inc/admin/views/quiz/modal-choose-items.php:83
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:550
msgid "Add"
msgstr ""

#: inc/admin/class-lp-modal-search-items.php:61
#: inc/admin/class-lp-modal-search-users.php:51
#: inc/admin/views/course/modal-choose-items.php:66
#: inc/admin/views/meta-boxes/order/details.php:306
#: inc/admin/views/quiz/modal-choose-items.php:58
#: inc/TemplateHooks/Course/FilterCourseTemplate.php:99
msgid "Close"
msgstr ""

#: inc/admin/class-lp-modal-search-items.php:62
#: inc/admin/views/modal-search-items.php:10
msgid "Search items"
msgstr ""

#: inc/admin/class-lp-modal-search-items.php:187
#: inc/admin/class-lp-modal-search-users.php:143
#: inc/lp-core-functions.php:639
msgid "<"
msgstr ""

#: inc/admin/class-lp-modal-search-items.php:188
#: inc/admin/class-lp-modal-search-users.php:144
#: inc/lp-core-functions.php:640
msgid ">"
msgstr ""

#: inc/admin/class-lp-modal-search-items.php:236
#: inc/admin/class-lp-modal-search-users.php:203
msgid "No item found"
msgstr ""

#: inc/admin/class-lp-modal-search-items.php:337
msgid "There are no available lessons for this course, please use "
msgstr ""

#: inc/admin/class-lp-modal-search-items.php:338
#: inc/admin/class-lp-modal-search-items.php:342
#: inc/admin/class-lp-modal-search-items.php:346
msgid "Add new item"
msgstr ""

#: inc/admin/class-lp-modal-search-items.php:341
msgid "There are no available quizzes for this course, please use "
msgstr ""

#: inc/admin/class-lp-modal-search-items.php:345
msgid "There are no available questions for this quiz, please use "
msgstr ""

#: inc/admin/class-lp-modal-search-users.php:52
#: inc/admin/views/modal-search-users.php:10
msgid "Search users"
msgstr ""

#: inc/admin/class-lp-reset-data.php:112
msgid "Item progress is deleted"
msgstr ""

#: inc/admin/class-lp-reset-data.php:114
msgid "No data found"
msgstr ""

#: inc/admin/class-lp-setup-wizard.php:71
msgctxt "static-page"
msgid "LP Courses"
msgstr ""

#: inc/admin/class-lp-setup-wizard.php:72
msgctxt "static-page"
msgid "LP Profile"
msgstr ""

#: inc/admin/class-lp-setup-wizard.php:73
msgctxt "static-page"
msgid "LP Checkout"
msgstr ""

#: inc/admin/class-lp-setup-wizard.php:74
msgctxt "static-page"
msgid "LP Become a Teacher"
msgstr ""

#: inc/admin/class-lp-setup-wizard.php:75
msgctxt "static-page"
msgid "LP Terms and Conditions"
msgstr ""

#: inc/admin/class-lp-setup-wizard.php:76
msgctxt "static-page"
msgid "Instructors"
msgstr ""

#: inc/admin/class-lp-setup-wizard.php:77
msgctxt "static-page"
msgid "Instructor"
msgstr ""

#: inc/admin/class-lp-setup-wizard.php:229
msgid "Welcome"
msgstr ""

#: inc/admin/class-lp-setup-wizard.php:231
msgid "Run Setup Wizard"
msgstr ""

#: inc/admin/class-lp-setup-wizard.php:234
msgid "Pages"
msgstr ""

#: inc/admin/class-lp-setup-wizard.php:244
#: inc/admin/meta-box/fields/payment-order.php:7
#: inc/admin/views/setup/steps/payment.php:16
#: templates/checkout/payment.php:25
msgid "Payment"
msgstr ""

#: inc/admin/class-lp-setup-wizard.php:252
#: inc/admin/views/setup/content.php:67
#: inc/admin/views/setup/steps/finish.php:12
#: inc/TemplateHooks/UserItem/UserCourseTemplate.php:98
msgid "Finish"
msgstr ""

#: inc/admin/class-lp-setup-wizard.php:369
#: inc/gateways/paypal/class-lp-gateway-paypal.php:87
#: inc/gateways/paypal/class-lp-gateway-paypal.php:91
msgid "PayPal"
msgstr ""

#: inc/admin/editor/class-lp-admin-editor-course.php:85
#: inc/curds/class-lp-course-curd.php:46
msgid "New Course"
msgstr ""

#: inc/admin/editor/class-lp-admin-editor-course.php:238
#: inc/admin/editor/class-lp-admin-editor-course.php:267
msgid "You can not delete this item!"
msgstr ""

#: inc/admin/editor/class-lp-admin-editor-course.php:242
#: inc/admin/editor/class-lp-admin-editor-course.php:271
msgid "Invalid params!"
msgstr ""

#: inc/admin/editor/class-lp-admin-editor-question.php:113
#: inc/curds/class-lp-question-curd.php:48
msgid "New Question"
msgstr ""

#: inc/admin/editor/class-lp-admin-editor-quiz.php:51
msgid "Invalid quiz"
msgstr ""

#: inc/admin/editor/class-lp-admin-editor-quiz.php:155
#: inc/curds/class-lp-quiz-curd.php:88
#: inc/custom-post-types/quiz.php:94
msgid "New Quiz"
msgstr ""

#: inc/admin/editor/class-lp-admin-editor-quiz.php:164
#: inc/admin/editor/class-lp-admin-editor-quiz.php:610
msgid "Quiz creation failed."
msgstr ""

#. translators: 1: Plugin name and version.
#: inc/admin/helpers/class-lp-plugins-helper.php:241
msgid "Install %s now"
msgstr ""

#. translators: 1: Plugin name and version
#: inc/admin/helpers/class-lp-plugins-helper.php:248
msgid "Update %s now"
msgstr ""

#: inc/admin/helpers/class-lp-plugins-helper.php:255
#: inc/admin/helpers/class-lp-plugins-helper.php:264
msgid "Disable %s now"
msgstr ""

#: inc/admin/helpers/class-lp-plugins-helper.php:255
#: inc/admin/helpers/class-lp-plugins-helper.php:264
msgid "Disable Now"
msgstr ""

#: inc/admin/helpers/class-lp-plugins-helper.php:257
#: inc/admin/helpers/class-lp-plugins-helper.php:266
msgid "Enable %s now"
msgstr ""

#: inc/admin/helpers/class-lp-plugins-helper.php:257
#: inc/admin/helpers/class-lp-plugins-helper.php:266
msgid "Enable Now"
msgstr ""

#: inc/admin/lp-admin-actions.php:219
#: inc/admin/sub-menus/class-lp-submenu-categories.php:11
#: inc/admin/sub-menus/class-lp-submenu-categories.php:12
#: inc/custom-post-types/course.php:375
#: inc/TemplateHooks/Course/FilterCourseTemplate.php:338
msgid "Categories"
msgstr ""

#: inc/admin/lp-admin-functions.php:32
msgctxt "copy course"
msgid "Duplicate"
msgstr ""

#: inc/admin/lp-admin-functions.php:79
msgctxt "copy quiz"
msgid "Duplicate"
msgstr ""

#: inc/admin/lp-admin-functions.php:88
msgctxt "copy question"
msgid "Duplicate"
msgstr ""

#: inc/admin/lp-admin-functions.php:97
msgctxt "copy lesson"
msgid "Duplicate"
msgstr ""

#: inc/admin/lp-admin-functions.php:136
msgid "[ Add a new page ]"
msgstr ""

#: inc/admin/lp-admin-functions.php:159
msgid "Select Page"
msgstr ""

#: inc/admin/lp-admin-functions.php:177
#: inc/admin/meta-box/fields/select-page.php:24
msgid "Select a page&hellip;"
msgstr ""

#: inc/admin/lp-admin-functions.php:205
msgctxt "dropdown pages"
msgid "or"
msgstr ""

#: inc/admin/lp-admin-functions.php:208
msgid "Create new"
msgstr ""

#: inc/admin/lp-admin-functions.php:213
msgid "New page title"
msgstr ""

#: inc/admin/lp-admin-functions.php:215
msgid "Ok [Enter]"
msgstr ""

#: inc/admin/lp-admin-functions.php:217
msgid "Cancel [ESC]"
msgstr ""

#: inc/admin/lp-admin-functions.php:222
msgid "Edit page"
msgstr ""

#: inc/admin/lp-admin-functions.php:225
msgid "View page"
msgstr ""

#: inc/admin/lp-admin-functions.php:348
msgid "Minutes"
msgstr ""

#: inc/admin/lp-admin-functions.php:439
msgid "Plain Text"
msgstr ""

#: inc/admin/lp-admin-functions.php:440
msgid "HTML"
msgstr ""

#: inc/admin/lp-admin-functions.php:1646
#: inc/TemplateHooks/Course/SingleCourseModernLayout.php:370
msgid "Copy"
msgstr ""

#: inc/admin/lp-admin-functions.php:1754
msgid "Question id %s does not exist."
msgstr ""

#: inc/admin/lp-admin-functions.php:1758
msgid "Quiz id %s does not exist."
msgstr ""

#: inc/admin/lp-admin-functions.php:2088
#: inc/admin/views/addons.php:260
#: inc/lp-deprecated.php:213
#: inc/TemplateHooks/Profile/ProfileQuizzesTemplate.php:239
#: inc/templates/class-lp-template-profile.php:116
#: inc/templates/class-lp-template-profile.php:158
#: inc/user/class-lp-profile.php:774
#: templates/widgets/course-info.php:35
msgid "All"
msgstr ""

#: inc/admin/lp-admin-functions.php:2093
#: inc/templates/class-lp-template-profile.php:117
msgid "Publish"
msgstr ""

#: inc/admin/lp-admin-functions.php:2098
#: inc/order/class-lp-order.php:283
#: inc/templates/class-lp-template-profile.php:118
msgid "Pending"
msgstr ""

#: inc/admin/lp-admin-functions.php:2103
#: inc/admin/views/addons.php:164
#: inc/admin/views/addons.php:262
#: inc/ExternalPlugin/Elementor/Widgets/Course/FilterCourseElementor.php:235
#: inc/TemplateHooks/Course/FilterCourseTemplate.php:251
msgid "Paid"
msgstr ""

#: inc/admin/lp-admin-functions.php:2108
#: inc/admin/views/addons.php:164
#: inc/admin/views/addons.php:263
#: inc/course/abstract-course.php:674
#: inc/course/abstract-course.php:713
#: inc/ExternalPlugin/Elementor/Widgets/Course/FilterCourseElementor.php:233
#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:880
#: inc/TemplateHooks/Course/FilterCourseTemplate.php:247
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:412
#: templates/order/order-details.php:46
msgid "Free"
msgstr ""

#: inc/admin/meta-box/fields/course-permalink.php:27
msgid "Courses base"
msgstr ""

#: inc/admin/meta-box/fields/course-permalink.php:32
msgid "Courses base with category"
msgstr ""

#: inc/admin/meta-box/fields/course-permalink.php:80
msgid "Custom Base"
msgstr ""

#: inc/admin/meta-box/fields/course-permalink.php:84
msgid "Enter a custom base to use. A base must be set or WordPress will use default values instead."
msgstr ""

#: inc/admin/meta-box/fields/custom-fields.php:42
msgid "Add new"
msgstr ""

#: inc/admin/meta-box/fields/email-content.php:95
msgid "This template has been overridden by your theme and can be found in: <code>%s</code>. <br />Please open the file in an editor program to edit"
msgstr ""

#: inc/admin/meta-box/fields/email-content.php:135
msgid "Click on any variables above to insert them into the email."
msgstr ""

#: inc/admin/meta-box/fields/image-advanced.php:25
#: inc/admin/meta-box/fields/image-advanced.php:38
#: inc/admin/meta-box/fields/image.php:28
#: inc/admin/views/course/section.php:48
#: inc/admin/views/meta-boxes/fields/materials.php:134
#: inc/admin/views/meta-boxes/fields/materials.php:156
#: inc/admin/views/question/fib-answer-editor.php:24
#: inc/admin/views/question/option.php:22
#: inc/admin/views/tools/database/html-clean-database.php:46
msgid "Delete"
msgstr ""

#: inc/admin/meta-box/fields/image-advanced.php:38
msgid "Add images"
msgstr ""

#: inc/admin/meta-box/fields/image-advanced.php:38
msgid "Add to gallery"
msgstr ""

#: inc/admin/meta-box/fields/image-advanced.php:38
msgid "Delete images"
msgstr ""

#: inc/admin/meta-box/fields/image.php:27
msgid "Select images"
msgstr ""

#: inc/admin/meta-box/fields/image.php:27
#: inc/admin/views/meta-boxes/fields/autocomplete.php:62
msgid "Select"
msgstr ""

#: inc/admin/meta-box/fields/image.php:27
#: inc/admin/views/meta-boxes/fields/materials.php:96
#: inc/class-lp-assets.php:142
#: templates/profile/tabs/settings/avatar.php:40
#: templates/profile/tabs/settings/cover-image.php:22
msgid "Upload"
msgstr ""

#: inc/admin/meta-box/fields/list-emails.php:7
#: inc/admin/views/meta-boxes/order/content-tab-preview-exports-invoice.php:37
#: inc/admin/views/meta-boxes/order/exports-invoice.php:77
#: templates/checkout/account-register.php:25
#: templates/global/become-teacher-form.php:39
#: templates/global/form-register.php:29
msgid "Email"
msgstr ""

#: inc/admin/meta-box/fields/list-emails.php:51
msgid "Manage"
msgstr ""

#: inc/admin/meta-box/fields/list-emails.php:58
msgid "Enable all"
msgstr ""

#: inc/admin/meta-box/fields/list-emails.php:59
msgid "Disable all"
msgstr ""

#: inc/admin/settings/class-lp-settings-advanced.php:18
msgid "Advanced"
msgstr ""

#: inc/admin/settings/class-lp-settings-emails.php:25
#: inc/admin/settings/class-lp-settings-emails.php:157
msgid "Emails"
msgstr ""

#: inc/admin/settings/class-lp-settings-emails.php:115
msgid "Email sender options"
msgstr ""

#: inc/admin/settings/class-lp-settings-emails.php:117
msgid "For all outgoing LearnPress notification emails."
msgstr ""

#: inc/admin/settings/class-lp-settings-emails.php:120
msgid "\"From\" name"
msgstr ""

#: inc/admin/settings/class-lp-settings-emails.php:130
msgid "Email template"
msgstr ""

#: inc/admin/settings/class-lp-settings-emails.php:132
msgid "This section lets you customize the LearnPress emails"
msgstr ""

#: inc/admin/settings/class-lp-settings-emails.php:145
msgid "Header image"
msgstr ""

#: inc/admin/settings/class-lp-settings-emails.php:151
msgid "Footer text"
msgstr ""

#: inc/admin/settings/class-lp-settings-payments.php:24
#: inc/admin/settings/class-lp-settings-payments.php:119
msgid "Payments"
msgstr ""

#: inc/admin/settings/class-lp-settings-payments.php:80
msgid "Guest checkout"
msgstr ""

#: inc/admin/settings/class-lp-settings-payments.php:84
msgid "Enable guest checkout."
msgstr ""

#: inc/admin/settings/class-lp-settings-payments.php:87
msgid "Account login"
msgstr ""

#: inc/admin/settings/class-lp-settings-payments.php:91
msgid "Enable the login form in the checkout."
msgstr ""

#: inc/admin/settings/class-lp-settings-payments.php:94
msgid "Account creation"
msgstr ""

#: inc/admin/settings/class-lp-settings-payments.php:98
msgid "Enable the register form in the checkout."
msgstr ""

#: inc/admin/settings/class-lp-settings-payments.php:107
msgid "Custom order slug"
msgstr ""

#: inc/admin/settings/class-lp-settings-permalink.php:19
msgid "Permalinks"
msgstr ""

#: inc/admin/settings/email-groups/class-lp-settings-cancelled-order-emails.php:24
msgid "Cancelled Order"
msgstr ""

#: inc/admin/settings/email-groups/class-lp-settings-completed-order-emails.php:23
msgid "Completed Order"
msgstr ""

#: inc/admin/settings/email-groups/class-lp-settings-course-review-emails.php:23
msgid "Review Course"
msgstr ""

#: inc/admin/settings/email-groups/class-lp-settings-enrolled-course-emails.php:28
#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:253
msgid "Enrolled Course"
msgstr ""

#: inc/admin/settings/email-groups/class-lp-settings-finished-course-emails.php:28
#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:263
msgid "Finished Course"
msgstr ""

#: inc/admin/settings/email-groups/class-lp-settings-new-order-emails.php:24
msgid "New Order"
msgstr ""

#: inc/admin/settings/email-groups/class-lp-settings-processing-order-emails.php:22
msgid "Processing Order"
msgstr ""

#: inc/admin/settings/email-groups/class-lp-settings-reset-password-emails.php:21
#: inc/emails/types/class-lp-email-reset-password.php:18
#: inc/emails/types/class-lp-email-reset-password.php:22
msgid "Reset Password"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-addons.php:14
#: learnpress.php:758
msgid "Add-ons"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-addons.php:15
msgid "LearnPress Add-ons"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-addons.php:23
msgid "LearnPress Addons"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-settings.php:19
msgid "LearnPress Settings"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-settings.php:60
msgid "Save settings"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-statistics.php:15
msgid "Statistics"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-statistics.php:16
msgid "LearnPress Statistics"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-statistics.php:20
#: inc/lp-template-functions.php:67
#: inc/TemplateHooks/Course/SingleCourseClassicTemplate.php:267
msgid "Overview"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-statistics.php:23
msgid "Users"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-themes.php:15
msgid "Themes"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-themes.php:16
msgid "LearnPress Themes"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-tools.php:12
msgid "Tools"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-tools.php:13
msgid "LearnPress Tools"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-tools.php:20
msgid "Course Data"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-tools.php:21
msgid "Assign/Unassigned Course"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-tools.php:22
msgid "Database"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-tools.php:23
msgid "Templates"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-tools.php:24
msgid "LearnPress Beta Version"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-tools.php:25
msgid "Cache"
msgstr ""

#: inc/admin/sub-menus/class-lp-submenu-tools.php:94
msgid "Clear all cache"
msgstr ""

#: inc/admin/views/addons.php:127
msgid "Latest version"
msgstr ""

#: inc/admin/views/addons.php:138
msgid "Extends Now"
msgstr ""

#: inc/admin/views/addons.php:143
msgid "Your update and support has expired!"
msgstr ""

#: inc/admin/views/addons.php:148
msgid "You have a license of for this item with %s days of update & support remaining. Please extend the update & support license to keep updating the latest versions & receive customer support from ThimPress before it expires."
msgstr ""

#: inc/admin/views/addons.php:165
msgid "WP.org"
msgstr ""

#: inc/admin/views/addons.php:165
msgid "Thimpress"
msgstr ""

#: inc/admin/views/addons.php:191
msgid "Change Purchase Code"
msgstr ""

#: inc/admin/views/addons.php:198
#: inc/admin/views/addons.php:201
#: inc/admin/views/tools/course/html-install-sample-data.php:59
#: inc/admin/views/tools/course/html-install-sample-data.php:62
msgid "Install"
msgstr ""

#: inc/admin/views/addons.php:206
msgid "Deactivate"
msgstr ""

#: inc/admin/views/addons.php:218
#: inc/admin/views/addons.php:237
msgid "Purchase Code"
msgstr ""

#: inc/admin/views/addons.php:220
#: inc/admin/views/addons.php:238
msgid "Enter Purchase Code"
msgstr ""

#: inc/admin/views/addons.php:243
#: inc/admin/views/addons.php:264
#: inc/admin/views/meta-boxes/order/actions.php:45
#: inc/admin/views/meta-boxes/order/exports-invoice.php:87
msgid "Update"
msgstr ""

#: inc/admin/views/addons.php:261
msgid "Installed"
msgstr ""

#: inc/admin/views/addons.php:265
msgid "License"
msgstr ""

#: inc/admin/views/addons.php:266
msgid "Not Installed"
msgstr ""

#: inc/admin/views/addons.php:291
msgid "Search name addon"
msgstr ""

#: inc/admin/views/addons/html-loop-theme.php:45
msgid "Get it now"
msgstr ""

#: inc/admin/views/addons/html-loop-theme.php:46
msgid "View Demo"
msgstr ""

#: inc/admin/views/addons/html-themes.php:18
msgid "No related themes."
msgstr ""

#: inc/admin/views/addons/html-themes.php:24
msgid "Education Support"
msgstr ""

#: inc/admin/views/admin-notices/addons-new-version.php:27
msgid "New version Addons."
msgstr ""

#: inc/admin/views/admin-notices/addons-purchased-extend.php:31
msgid "You have LearnPress Add-on licenses that need to be extended."
msgstr ""

#: inc/admin/views/admin-notices/addons-purchased-extend.php:35
msgid "Check now!"
msgstr ""

#: inc/admin/views/admin-notices/plugin-base.php:19
msgid "The LearnPress plugin base directory must be <strong>learnpress/learnpres.php</strong> (case-sensitive) to ensure all functions work properly and are fully operational (currently <strong>%s</strong>)"
msgstr ""

#: inc/admin/views/admin-notices/setup-wizard.php:18
msgid "LearnPress LMS is ready to use."
msgstr ""

#: inc/admin/views/admin-notices/setup-wizard.php:20
msgid "Quick Setup"
msgstr ""

#: inc/admin/views/admin-notices/upgrade-db.php:17
msgid "<strong>LearnPress update</strong> – We need to update your database to the latest version."
msgstr ""

#: inc/admin/views/admin-notices/upgrade-db.php:23
msgid "Go to Update"
msgstr ""

#: inc/admin/views/admin-notices/wp-remote.php:33
msgid "Check Site Health"
msgstr ""

#: inc/admin/views/backend-user-profile.php:26
msgid "LearnPress User Profile"
msgstr ""

#: inc/admin/views/course/curriculum.php:14
#: inc/admin/views/question/actions.php:12
#: inc/admin/views/quiz/editor.php:35
#: inc/admin/views/quiz/question-meta.php:12
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:117
msgid "Details"
msgstr ""

#: inc/admin/views/course/editor.php:41
msgid "Something went wrong! Please reload to continue editing the curriculum."
msgstr ""

#: inc/admin/views/course/modal-choose-items.php:71
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:529
msgid "Type here to search for an item"
msgstr ""

#: inc/admin/views/course/modal-choose-items.php:76
#: inc/admin/views/quiz/modal-choose-items.php:69
msgid "No item found."
msgstr ""

#: inc/admin/views/course/modal-choose-items.php:93
#: inc/admin/views/quiz/modal-choose-items.php:84
msgid "Adding"
msgstr ""

#: inc/admin/views/course/new-section.php:16
#: inc/admin/views/course/section.php:17
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:320
msgid "Create a new section"
msgstr ""

#: inc/admin/views/course/pagination.php:18
#: inc/admin/views/quiz/pagination.php:16
msgctxt "page-navigation"
msgid "Previous"
msgstr ""

#: inc/admin/views/course/pagination.php:20
#: inc/admin/views/quiz/pagination.php:18
msgctxt "page-navigation"
msgid "Next"
msgstr ""

#: inc/admin/views/course/section-item.php:24
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:357
msgid "Enable/Disable Preview"
msgstr ""

#: inc/admin/views/course/section-item.php:27
#: inc/admin/views/quiz/question-actions.php:31
msgid "Edit an item"
msgstr ""

#: inc/admin/views/course/section-item.php:34
msgid "Remove from the course"
msgstr ""

#: inc/admin/views/course/section-item.php:37
#: inc/admin/views/quiz/question-actions.php:41
msgid "Move to trash"
msgstr ""

#: inc/admin/views/course/section.php:31
msgid "Section description..."
msgstr ""

#: inc/admin/views/course/section.php:45
#: inc/admin/views/quiz/editor.php:79
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:435
msgid "Select items"
msgstr ""

#: inc/admin/views/course/section.php:49
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:211
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:368
msgid "Are you sure?"
msgstr ""

#: inc/admin/views/dashboard/html-orders.php:21
msgid "Total Raised"
msgstr ""

#: inc/admin/views/dashboard/html-orders.php:39
msgid "%d order"
msgstr ""

#: inc/admin/views/dashboard/plugin-status/html-no-data.php:4
msgid "No results found"
msgstr ""

#: inc/admin/views/dashboard/plugin-status/html-results.php:20
msgid "Downloaded"
msgstr ""

#: inc/admin/views/dashboard/plugin-status/html-results.php:23
msgid "Active Installation"
msgstr ""

#: inc/admin/views/dashboard/plugin-status/html-results.php:36
msgid "Published"
msgstr ""

#: inc/admin/views/dashboard/plugin-status/html-results.php:37
msgid "Updated"
msgstr ""

#: inc/admin/views/dashboard/plugin-status/html-results.php:38
msgid "Current Version"
msgstr ""

#: inc/admin/views/html-admin-notice-templates.php:35
msgid "There is a new update on LearnPress. You may need to update your theme <strong>(%s)</strong> to avoid outdated template files."
msgstr ""

#: inc/admin/views/html-admin-notice-templates.php:40
msgid "This is not a bug, don't worry. Read more about the outdated template files notice <a href=\\\"%s\\\" target=\\\"_blank\\\">here</a>."
msgstr ""

#: inc/admin/views/html-admin-notice-templates.php:54
msgid "View the list of outdated templates"
msgstr ""

#: inc/admin/views/html-admin-notice-templates.php:57
msgid "Dismiss"
msgstr ""

#: inc/admin/views/meta-boxes/course/assigned.php:29
#: inc/admin/views/meta-boxes/quiz/assigned.php:55
#: inc/custom-post-types/abstract.php:920
#: inc/custom-post-types/question.php:362
msgid "Not assigned yet"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:33
msgid "Course Settings"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:64
msgid "Offline Course"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:71
msgid "Pricing"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:78
msgid "Extra Information"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:85
msgid "Assessment"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:99
msgid "Downloadable Materials"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:119
msgid "Reset course progress: The course progress and results of student will be removed."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:126
msgid "Keep course progress: The course progress and results of student will remain."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:133
msgid "Open popup: The student can decide whether their course progress will be reset with the confirm popup."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:138
msgid "The maximum number of students that can join a course. Set 0 for unlimited."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:142
msgid "Not apply for case \"No enroll requirement\"."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:149
msgid "Fake students enrolled for the course."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:150
msgid "It only to display, not calculate"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:156
#: inc/admin/views/meta-boxes/lesson/settings.php:17
#: inc/admin/views/meta-boxes/quiz/settings.php:23
#: inc/custom-post-types/lesson.php:244
#: inc/custom-post-types/quiz.php:242
#: inc/Gutenberg/Blocks/SingleCourseElements/CourseDurationBlockType.php:79
#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1370
#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:530
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:636
#: inc/TemplateHooks/Course/SingleCourseModernLayout.php:214
#: inc/TemplateHooks/Course/SingleCourseOfflineTemplate.php:247
msgid "Duration"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:157
msgid "Set to 0 for the lifetime access."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:168
msgid "Block content"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:169
msgid "When the duration expires, the course is blocked."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:174
msgid "Block the course after the student finished this course."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:178
msgid "Allow Repurchase"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:179
msgid "Allow users to repurchase this course after it has been finished or blocked (Do not apply to free courses or Create Order manual)."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:183
msgid "Repurchase action"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:188
#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:481
msgid "Reset course progress"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:189
#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:482
msgid "Keep course progress"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:190
msgid "Open popup"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:201
msgid "Choose a difficulty level."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:208
msgid "Fake Students Enrolled"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:221
msgid "Max student"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:234
msgid "Re-take Course"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:235
msgid "The number of times a user can learn again from this course. To disable, set to 0."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:247
msgid "Finish button"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:248
msgid "Allow showing the finish button when the student has completed all items but has not passed the course assessment yet."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:252
msgid "Featured list"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:253
msgid "Add the course to the Featured List."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:257
msgid "Featured review"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:258
msgid "A good review to promote the course."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:261
msgid "External link"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:262
msgid "Normally used for offline classes. Ex: link to a contact page. Format: https://google.com"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:294
msgid "Enable offline course"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:295
msgid "When you enable the offline course feature, the system will disable certain online course functions, such as curriculum, finish button, re-take course, block content, repurchase. After checking the checkbox, make sure to click the \"Update\" button to apply the changes successfully."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:302
#: inc/admin/views/statistics/courses.php:43
#: inc/custom-post-types/lesson.php:174
#: inc/custom-post-types/lesson.php:175
#: inc/custom-post-types/lesson.php:178
#: inc/TemplateHooks/Course/SingleCourseOfflineTemplate.php:255
#: templates/widgets/course-info.php:25
msgid "Lessons"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:303
msgid "Total lessons of the course."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:318
msgid "Delivery Type"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:319
msgid "How your content is conveyed to students."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:331
msgid "You can enter the physical address of your class or specify the meeting method (e.g., Zoom, Google Meet, etc.)."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:367
msgid "Regular price"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:368
msgid "Set a regular price (<strong>%s</strong>). Leave it blank for <strong>Free</strong>."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:385
msgid "Sale price"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:386
msgid "Schedule"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:403
msgid "Sale start dates"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:408
msgctxt "placeholder"
msgid "From&hellip;"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:416
msgid "Sale end dates"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:421
msgctxt "placeholder"
msgid "To&hellip;"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:430
msgid "Price prefix"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:431
msgid "Show additional information placed before the price such as: Only, From, Up to..."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:435
msgid "Price Suffix"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:436
msgid "Show additional information placed after the price such as: Included Tax, Per Hour, (Per Week)..."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:440
msgid "There is no enrollment requirement"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:441
msgid "Students can see the content of all course items and take the quiz without logging in."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:531
msgid "The method of evaluating a student's performance in a course."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:534
msgid "Note: changing the evaluation type will affect the assessment results of student learning."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:538
msgid "<br /><strong>Note! </strong>There is no final quiz in the course. Please add a final quiz."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:560
#: inc/rest-api/v1/admin/class-lp-admin-rest-course-controller.php:81
msgid "Edit: %s"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:571
msgid "Evaluation"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:579
#: inc/admin/views/meta-boxes/quiz/settings.php:35
msgid "Passing Grade(%)"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:580
msgid "The conditions that must be achieved to finish the course."
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:606
msgid "Target Audience"
msgstr ""

#: inc/admin/views/meta-boxes/course/settings.php:611
msgid "Key Features"
msgstr ""

#: inc/admin/views/meta-boxes/fields/extra-faq.php:70
#: inc/admin/views/meta-boxes/fields/extra.php:57
#: inc/admin/views/meta-boxes/fields/repeater.php:50
msgid "+ Add more"
msgstr ""

#: inc/admin/views/meta-boxes/fields/file.php:85
#: inc/admin/views/meta-boxes/lp-meta-box-functions.php:374
msgid "+ Add media"
msgstr ""

#: inc/admin/views/meta-boxes/fields/materials.php:56
msgid "Downloadable Materials is not allowed!"
msgstr ""

#: inc/admin/views/meta-boxes/fields/materials.php:63
msgid "Maximum amount of files you can upload more: %d files (maximum file size is %s MB)"
msgstr ""

#: inc/admin/views/meta-boxes/fields/materials.php:72
msgid "And allow upload only these types: %s."
msgstr ""

#: inc/admin/views/meta-boxes/fields/materials.php:79
msgid "Add Course Materials"
msgstr ""

#: inc/admin/views/meta-boxes/fields/materials.php:82
#: inc/class-lp-assets.php:145
#: inc/TemplateHooks/Profile/ProfileTemplate.php:175
#: inc/TemplateHooks/Profile/ProfileTemplate.php:271
#: templates/profile/tabs/settings/avatar.php:27
#: templates/profile/tabs/settings/cover-image.php:23
msgid "Save"
msgstr ""

#: inc/admin/views/meta-boxes/fields/materials.php:88
#: inc/admin/views/meta-boxes/fields/materials.php:144
msgid "File Title"
msgstr ""

#: inc/admin/views/meta-boxes/fields/materials.php:90
msgid "Enter File Title"
msgstr ""

#: inc/admin/views/meta-boxes/fields/materials.php:93
#: inc/admin/views/meta-boxes/fields/materials.php:145
msgid "Method"
msgstr ""

#: inc/admin/views/meta-boxes/fields/materials.php:97
msgid "External"
msgstr ""

#: inc/admin/views/meta-boxes/fields/materials.php:101
#: inc/admin/views/meta-boxes/fields/materials.php:117
msgid "Choose File  "
msgstr ""

#: inc/admin/views/meta-boxes/fields/materials.php:108
msgid "Save field"
msgstr ""

#: inc/admin/views/meta-boxes/fields/materials.php:125
msgid "File URL"
msgstr ""

#: inc/admin/views/meta-boxes/fields/materials.php:127
msgid "Enter File URL"
msgstr ""

#: inc/admin/views/meta-boxes/fields/materials.php:132
msgid "Do you want to delete this file?"
msgstr ""

#: inc/admin/views/meta-boxes/fields/materials.php:146
msgid "Action"
msgstr ""

#: inc/admin/views/meta-boxes/fields/repeater.php:89
msgid "Repeater"
msgstr ""

#: inc/admin/views/meta-boxes/lesson/settings.php:9
msgid "Lesson Settings"
msgstr ""

#: inc/admin/views/meta-boxes/lesson/settings.php:29
#: inc/custom-post-types/abstract.php:1047
#: inc/custom-post-types/lesson.php:148
#: inc/custom-post-types/lesson.php:247
#: inc/lp-template-functions.php:1106
#: templates/loop/single-course/loop-section-item.php:37
#: templates/single-course/section/item-meta.php:32
msgid "Preview"
msgstr ""

#: inc/admin/views/meta-boxes/lesson/settings.php:30
msgid "Students can view this lesson content without taking the course."
msgstr ""

#: inc/admin/views/meta-boxes/lesson/settings.php:63
#: inc/lp-template-functions.php:101
#: inc/TemplateHooks/Course/SingleCourseClassicTemplate.php:270
msgid "Materials"
msgstr ""

#: inc/admin/views/meta-boxes/order/actions.php:19
msgid "Choose an action"
msgstr ""

#: inc/admin/views/meta-boxes/order/actions.php:21
msgid "Trigger action of the current order status"
msgstr ""

#: inc/admin/views/meta-boxes/order/actions.php:30
msgid "Delete Permanently"
msgstr ""

#: inc/admin/views/meta-boxes/order/actions.php:32
msgid "Move to Trash"
msgstr ""

#: inc/admin/views/meta-boxes/order/content-tab-preview-exports-invoice.php:32
#: inc/admin/views/meta-boxes/order/exports-invoice.php:71
msgid "Order Date"
msgstr ""

#: inc/admin/views/meta-boxes/order/content-tab-preview-exports-invoice.php:35
#: inc/admin/views/meta-boxes/order/exports-invoice.php:73
msgid "Invoice No."
msgstr ""

#: inc/admin/views/meta-boxes/order/content-tab-preview-exports-invoice.php:36
#: inc/admin/views/meta-boxes/order/exports-invoice.php:75
msgid "Customer"
msgstr ""

#: inc/admin/views/meta-boxes/order/content-tab-preview-exports-invoice.php:67
#: inc/admin/views/meta-boxes/order/details.php:210
msgid "Cost"
msgstr ""

#: inc/admin/views/meta-boxes/order/content-tab-preview-exports-invoice.php:68
#: inc/admin/views/meta-boxes/order/details.php:211
#: templates/emails/order-items-table.php:88
msgid "Quantity"
msgstr ""

#: inc/admin/views/meta-boxes/order/content-tab-preview-exports-invoice.php:69
msgid "Amount"
msgstr ""

#: inc/admin/views/meta-boxes/order/content-tab-preview-exports-invoice.php:96
msgid "Sub Total"
msgstr ""

#: inc/admin/views/meta-boxes/order/content-tab-preview-exports-invoice.php:102
#: inc/admin/views/meta-boxes/order/details.php:212
#: inc/custom-post-types/order.php:458
#: templates/checkout/order-received.php:117
#: templates/checkout/review-order.php:156
#: templates/emails/order-items-table.php:120
#: templates/emails/plain/order-items-table.php:88
#: templates/order/order-details.php:29
#: templates/order/order-details.php:115
#: templates/profile/tabs/orders/list.php:31
msgid "Total"
msgstr ""

#: inc/admin/views/meta-boxes/order/details.php:28
msgid "Order %s"
msgstr ""

#: inc/admin/views/meta-boxes/order/details.php:32
msgid "Created manually"
msgstr ""

#: inc/admin/views/meta-boxes/order/details.php:47
msgid "Date created:"
msgstr ""

#: inc/admin/views/meta-boxes/order/details.php:71
#: templates/order/confirm.php:70
msgid "Status:"
msgstr ""

#: inc/admin/views/meta-boxes/order/details.php:92
msgid "When the Status is changed to \"Pending\", \"Cancelled\", or \"Failed\" all courses, lessons, quizzes, and other progress are deleted!"
msgstr ""

#: inc/admin/views/meta-boxes/order/details.php:100
msgid "Customers:"
msgstr ""

#: inc/admin/views/meta-boxes/order/details.php:116
#: inc/admin/views/tools/course/html-assign-course.php:51
#: inc/admin/views/tools/course/html-unassign-course.php:52
#: inc/admin/views/tools/course/html-unassign-course.php:56
msgid "Choose User"
msgstr ""

#: inc/admin/views/meta-boxes/order/details.php:154
msgid "No customer"
msgstr ""

#: inc/admin/views/meta-boxes/order/details.php:181
msgid "In order to change the order user, please change the order status to \"Pending\"."
msgstr ""

#: inc/admin/views/meta-boxes/order/details.php:190
#: templates/order/order-details.php:122
msgid "Order key:"
msgstr ""

#: inc/admin/views/meta-boxes/order/details.php:199
msgid "Customer Note"
msgstr ""

#: inc/admin/views/meta-boxes/order/details.php:226
msgid "There are no order items"
msgstr ""

#: inc/admin/views/meta-boxes/order/details.php:237
msgid "Subtotal:"
msgstr ""

#: inc/admin/views/meta-boxes/order/details.php:248
#: templates/order/confirm.php:57
msgid "Total:"
msgstr ""

#: inc/admin/views/meta-boxes/order/details.php:265
msgid "Add item(s)"
msgstr ""

#: inc/admin/views/meta-boxes/order/details.php:270
msgid "In order to change the order item, please change the order status to 'Pending'."
msgstr ""

#: inc/admin/views/meta-boxes/order/details.php:297
msgid "Type here to search for the course"
msgstr ""

#: inc/admin/views/meta-boxes/order/details.php:301
msgid "No results"
msgstr ""

#: inc/admin/views/meta-boxes/order/exports-invoice.php:26
msgid "PDF Invoice"
msgstr ""

#: inc/admin/views/meta-boxes/order/exports-invoice.php:39
msgid "PDF Preview"
msgstr ""

#: inc/admin/views/meta-boxes/order/exports-invoice.php:40
msgid "Export Options"
msgstr ""

#: inc/admin/views/meta-boxes/order/exports-invoice.php:59
msgid "Export to pdf"
msgstr ""

#: inc/admin/views/meta-boxes/order/exports-invoice.php:69
msgid "Site Title"
msgstr ""

#: inc/admin/views/meta-boxes/order/exports-invoice.php:79
msgid "Payment Medthod"
msgstr ""

#: inc/admin/views/meta-boxes/question/settings.php:9
msgid "Question Settings"
msgstr ""

#: inc/admin/views/meta-boxes/question/settings.php:18
#: inc/admin/views/quiz/question-meta.php:33
msgid "Points for choosing the correct answer."
msgstr ""

#: inc/admin/views/meta-boxes/question/settings.php:31
#: inc/admin/views/quiz/question-meta.php:44
msgid "The instructions for the user to select the right answer. The text will be shown when users click the 'Hint' button."
msgstr ""

#: inc/admin/views/meta-boxes/question/settings.php:36
#: inc/admin/views/quiz/question-meta.php:55
msgid "The explanation will be displayed when students click the \"Check Answer\" button."
msgstr ""

#: inc/admin/views/meta-boxes/quiz/settings.php:24
msgid "Set to 0 for no limit, greater than 0 for a limit."
msgstr ""

#: inc/admin/views/meta-boxes/quiz/settings.php:36
msgid "The conditions that must be achieved in order to pass the quiz."
msgstr ""

#: inc/admin/views/meta-boxes/quiz/settings.php:49
msgid "Instant Check"
msgstr ""

#: inc/admin/views/meta-boxes/quiz/settings.php:50
msgid "Allow students to immediately check their answers while doing the quiz."
msgstr ""

#: inc/admin/views/meta-boxes/quiz/settings.php:54
msgid "Negative Marking"
msgstr ""

#: inc/admin/views/meta-boxes/quiz/settings.php:55
msgid "For each question that students answer wrongly, the total point is deducted exactly from the question's point."
msgstr ""

#: inc/admin/views/meta-boxes/quiz/settings.php:59
msgid "Minus for skip"
msgstr ""

#: inc/admin/views/meta-boxes/quiz/settings.php:60
msgid "For each question that students answer skip, the total point is deducted exactly from the question's point."
msgstr ""

#: inc/admin/views/meta-boxes/quiz/settings.php:65
msgid "How many times can the user re-take this quiz? Set 0 to disable. Set -1 to infinite."
msgstr ""

#: inc/admin/views/meta-boxes/quiz/settings.php:78
msgid "The number of displayed questions on each page."
msgstr ""

#: inc/admin/views/meta-boxes/quiz/settings.php:92
msgid "Allow students to review this quiz after they finish the quiz."
msgstr ""

#: inc/admin/views/meta-boxes/quiz/settings.php:96
msgid "Show the correct answer"
msgstr ""

#: inc/admin/views/meta-boxes/quiz/settings.php:97
msgid "Allow students to view the correct answer to the question in reviewing this quiz."
msgstr ""

#: inc/admin/views/question/answer.php:17
#: inc/admin/views/quiz/question-answer.php:18
msgid "Answers"
msgstr ""

#: inc/admin/views/question/answer.php:18
#: inc/admin/views/quiz/question-answer.php:19
msgid "Correction"
msgstr ""

#: inc/admin/views/question/answer.php:27
msgid "Add a new Answer"
msgstr ""

#: inc/admin/views/question/fib-answer-editor.php:5
msgid "Select a word in the passage above and click <strong>'Insert a new blank'</strong> to make that word a blank for filling."
msgstr ""

#: inc/admin/views/question/fib-answer-editor.php:8
msgid "Insert a new blank"
msgstr ""

#: inc/admin/views/question/fib-answer-editor.php:9
msgid "Remove all blanks"
msgstr ""

#: inc/admin/views/question/fib-answer-editor.php:10
msgid "Clear content"
msgstr ""

#: inc/admin/views/question/fib-answer-editor.php:23
#: inc/admin/views/tools/course/html-install-sample-data.php:22
msgid "Options"
msgstr ""

#: inc/admin/views/question/fib-answer-editor.php:34
msgid "Match case"
msgstr ""

#: inc/admin/views/question/fib-answer-editor.php:35
msgid "Match two words in case sensitive."
msgstr ""

#: inc/admin/views/question/fib-answer-editor.php:37
msgid "Comparison"
msgstr ""

#: inc/admin/views/question/fib-answer-editor.php:41
msgid "Equal"
msgstr ""

#: inc/admin/views/question/fib-answer-editor.php:42
msgid "Match two words are equality."
msgstr ""

#: inc/admin/views/question/fib-answer-editor.php:47
msgid "Range"
msgstr ""

#: inc/admin/views/question/fib-answer-editor.php:48
msgid "Match any number in a range. Use <code>100, 200</code> to match any value from 100 to 200."
msgstr ""

#: inc/admin/views/question/fib-answer-editor.php:53
msgid "Any"
msgstr ""

#: inc/admin/views/question/fib-answer-editor.php:54
msgid "Match any value in a set of words. Use <code>fill, blank, or question</code> to match any value in the set."
msgstr ""

#: inc/admin/views/quiz/editor.php:49
#: inc/admin/views/tools/course/html-user.php:31
#: templates/profile/tabs/orders/list.php:34
msgid "Actions"
msgstr ""

#: inc/admin/views/quiz/editor.php:64
msgid "Create a new question"
msgstr ""

#: inc/admin/views/quiz/editor.php:69
msgid "Add with type..."
msgstr ""

#: inc/admin/views/quiz/editor.php:93
msgid "Something went wrong! Please reload to continue editing quiz questions."
msgstr ""

#: inc/admin/views/quiz/modal-choose-items.php:64
msgid "Type here to search for the question"
msgstr ""

#: inc/admin/views/quiz/question-actions.php:28
msgid "Duplicate"
msgstr ""

#: inc/admin/views/quiz/question-actions.php:38
msgid "Removed from the quiz"
msgstr ""

#: inc/admin/views/quiz/question-answer.php:29
msgid "Add option"
msgstr ""

#: inc/admin/views/setup/content.php:61
#: inc/TemplateHooks/UserItem/UserCourseTemplate.php:65
#: templates/single-course/buttons/continue.php:19
msgid "Continue"
msgstr ""

#: inc/admin/views/setup/footer.php:17
msgid "LearnPress %s. Designed by @ThimPress."
msgstr ""

#: inc/admin/views/setup/footer.php:18
#: inc/admin/views/setup/steps/finish.php:32
msgid "Back to Dashboard"
msgstr ""

#: inc/admin/views/setup/header.php:17
msgid "LearnPress &rsaquo; Setup Wizard"
msgstr ""

#: inc/admin/views/setup/setup-paypal.php:17
msgid "Paypal Email"
msgstr ""

#: inc/admin/views/setup/setup-paypal.php:22
msgid "Your Paypal email is in live mode."
msgstr ""

#: inc/admin/views/setup/setup-stripe.php:15
msgid "Live secret key"
msgstr ""

#: inc/admin/views/setup/setup-stripe.php:19
msgid "Live publish key"
msgstr ""

#: inc/admin/views/setup/setup-stripe.php:24
msgid "Test mode"
msgstr ""

#: inc/admin/views/setup/setup-stripe.php:28
msgid "Test secret key"
msgstr ""

#: inc/admin/views/setup/setup-stripe.php:32
msgid "Test publish key"
msgstr ""

#: inc/admin/views/setup/steps/currency.php:57
msgid "Thousands Separator"
msgstr ""

#: inc/admin/views/setup/steps/emails.php:12
msgid "Emails system"
msgstr ""

#: inc/admin/views/setup/steps/emails.php:14
msgid "Emails are sent to users or teachers for each particular action."
msgstr ""

#: inc/admin/views/setup/steps/emails.php:15
msgid "You can enable/disable each email in your LearnPress settings later."
msgstr ""

#: inc/admin/views/setup/steps/emails.php:16
msgid "But right now, you can enable all emails to see how emails work."
msgstr ""

#: inc/admin/views/setup/steps/emails.php:17
msgid "You can skip to the next step if you don’t want to."
msgstr ""

#: inc/admin/views/setup/steps/emails.php:22
msgid "Enable emails"
msgstr ""

#: inc/admin/views/setup/steps/finish.php:14
msgid "LearnPress LMS is ready to go!"
msgstr ""

#: inc/admin/views/setup/steps/finish.php:20
msgid "Install a demo course"
msgstr ""

#: inc/admin/views/setup/steps/finish.php:24
msgid "View Documentation"
msgstr ""

#: inc/admin/views/setup/steps/finish.php:28
msgid "Create a new course"
msgstr ""

#: inc/admin/views/setup/steps/pages.php:14
msgid "Static Pages"
msgstr ""

#: inc/admin/views/setup/steps/pages.php:16
msgid "The pages will display the content of LP's necessary pages, such as Courses, Checkout, and Profile"
msgstr ""

#: inc/admin/views/setup/steps/pages.php:17
msgid "If you are not sure, click <a href=\"%s\" id=\"create-pages\">here</a> to create pages automatically."
msgstr ""

#: inc/admin/views/setup/steps/pages.php:22
msgid "Page: Show a list of courses"
msgstr ""

#: inc/admin/views/setup/steps/pages.php:30
msgid "Page: Show a list of instructors"
msgstr ""

#: inc/admin/views/setup/steps/pages.php:38
msgid "Page: single instructor"
msgstr ""

#: inc/admin/views/setup/steps/pages.php:46
msgid "Page: Profile"
msgstr ""

#: inc/admin/views/setup/steps/pages.php:54
msgid "Page: Checkout"
msgstr ""

#: inc/admin/views/setup/steps/pages.php:62
msgid "Page: Become a Teacher"
msgstr ""

#: inc/admin/views/setup/steps/pages.php:70
msgid "Page: Terms and Conditions"
msgstr ""

#: inc/admin/views/setup/steps/payment.php:18
msgid "LearnPress can accept both online and offline payments. Additional payment addons can be installed later."
msgstr ""

#: inc/admin/views/setup/steps/welcome.php:12
msgid "Welcome to LearnPress"
msgstr ""

#: inc/admin/views/setup/steps/welcome.php:14
msgid "Thanks for choosing LearnPress to sell your online courses!"
msgstr ""

#: inc/admin/views/setup/steps/welcome.php:16
msgid "The following wizard will help you configure your LMS site and get you started quickly."
msgstr ""

#: inc/admin/views/statistics/courses.php:9
#: inc/admin/views/statistics/orders.php:9
#: inc/admin/views/statistics/overview.php:8
#: inc/admin/views/statistics/users.php:9
msgid "Today"
msgstr ""

#: inc/admin/views/statistics/courses.php:10
#: inc/admin/views/statistics/orders.php:10
#: inc/admin/views/statistics/overview.php:9
#: inc/admin/views/statistics/users.php:10
msgid "Yesterday"
msgstr ""

#: inc/admin/views/statistics/courses.php:11
#: inc/admin/views/statistics/orders.php:11
#: inc/admin/views/statistics/overview.php:10
#: inc/admin/views/statistics/users.php:11
msgid "Last 7 days"
msgstr ""

#: inc/admin/views/statistics/courses.php:12
#: inc/admin/views/statistics/orders.php:12
#: inc/admin/views/statistics/overview.php:11
#: inc/admin/views/statistics/users.php:12
msgid "Last 30 days"
msgstr ""

#: inc/admin/views/statistics/courses.php:13
#: inc/admin/views/statistics/orders.php:13
#: inc/admin/views/statistics/overview.php:12
#: inc/admin/views/statistics/users.php:13
msgid "This month"
msgstr ""

#: inc/admin/views/statistics/courses.php:14
#: inc/admin/views/statistics/orders.php:14
#: inc/admin/views/statistics/overview.php:13
#: inc/admin/views/statistics/users.php:14
msgid "Last 12 months"
msgstr ""

#: inc/admin/views/statistics/courses.php:15
#: inc/admin/views/statistics/orders.php:15
#: inc/admin/views/statistics/overview.php:14
#: inc/admin/views/statistics/users.php:15
msgid "This year"
msgstr ""

#: inc/admin/views/statistics/courses.php:27
#: inc/admin/views/statistics/overview.php:34
#: templates/profile/tabs/courses/general-statistic.php:45
msgid "Total Courses"
msgstr ""

#: inc/admin/views/statistics/courses.php:31
#: inc/admin/views/statistics/courses.php:57
#: inc/rest-api/v1/admin/class-lp-admin-rest-statistics-controller.php:141
msgid "Published Courses"
msgstr ""

#: inc/admin/views/statistics/courses.php:35
msgid "Pending Courses"
msgstr ""

#: inc/admin/views/statistics/courses.php:39
msgid "Future Courses"
msgstr ""

#: inc/admin/views/statistics/courses.php:47
msgid "Quizes"
msgstr ""

#: inc/admin/views/statistics/courses.php:52
msgid "Assginments"
msgstr ""

#: inc/admin/views/statistics/orders.php:27
#: inc/admin/views/statistics/overview.php:30
msgid "Total Orders"
msgstr ""

#: inc/admin/views/statistics/orders.php:31
#: inc/admin/views/statistics/orders.php:51
msgid "Completed Orders"
msgstr ""

#: inc/admin/views/statistics/orders.php:35
msgid "Proccessing Orders"
msgstr ""

#: inc/admin/views/statistics/orders.php:39
msgid "Pending Orders"
msgstr ""

#: inc/admin/views/statistics/orders.php:43
msgid "Cancelled Orders"
msgstr ""

#: inc/admin/views/statistics/orders.php:47
msgid "Fail Orders"
msgstr ""

#: inc/admin/views/statistics/overview.php:26
msgid "Total Sales"
msgstr ""

#: inc/admin/views/statistics/overview.php:38
msgid "Total Instructors"
msgstr ""

#: inc/admin/views/statistics/overview.php:42
#: templates/profile/tabs/courses/general-statistic.php:49
msgid "Total Students"
msgstr ""

#: inc/admin/views/statistics/overview.php:46
msgid "Net Sales"
msgstr ""

#: inc/admin/views/statistics/overview.php:54
msgid "Top Courses Sold"
msgstr ""

#: inc/admin/views/statistics/overview.php:61
msgid "Top Categories Sold"
msgstr ""

#: inc/admin/views/statistics/users.php:27
msgid "Users activated"
msgstr ""

#: inc/admin/views/statistics/users.php:39
msgid "Inprogress"
msgstr ""

#: inc/admin/views/statistics/users.php:43
#: inc/lp-deprecated.php:215
#: inc/Models/UserItems/UserItemModel.php:522
#: inc/TemplateHooks/Profile/ProfileQuizzesTemplate.php:240
#: inc/templates/class-lp-template-profile.php:160
#: inc/user-item/class-lp-user-item.php:691
#: inc/user/class-lp-profile.php:775
msgid "Finished"
msgstr ""

#: inc/admin/views/statistics/users.php:47
#: inc/Models/UserItems/UserQuizModel.php:635
#: inc/user-item/class-lp-user-item-quiz.php:155
msgid "Not Started"
msgstr ""

#: inc/admin/views/statistics/users.php:51
msgid "Registed Users"
msgstr ""

#: inc/admin/views/statistics/users.php:59
msgid "Top Courses By Students"
msgstr ""

#: inc/admin/views/statistics/users.php:66
msgid "Top Instructors By Students Enrolled Times"
msgstr ""

#: inc/admin/views/tools/course/html-assign-course.php:21
#: inc/admin/views/tools/course/html-assign-course.php:25
#: inc/admin/views/tools/course/html-unassign-course.php:22
#: inc/admin/views/tools/course/html-unassign-course.php:26
#: inc/class-lp-widget.php:419
msgid "Choose Course"
msgstr ""

#: inc/admin/views/tools/course/html-assign-course.php:55
msgid "Chose User"
msgstr ""

#: inc/admin/views/tools/course/html-assign-course.php:69
msgid "Assign Course"
msgstr ""

#: inc/admin/views/tools/course/html-assign-course.php:71
msgid "User can enroll in a specific course by manually assign to them."
msgstr ""

#: inc/admin/views/tools/course/html-assign-course.php:73
msgid "Noted: when assign user to course, the progress old of user with course assign will eraser, so be careful before do this."
msgstr ""

#: inc/admin/views/tools/course/html-assign-course.php:92
msgid "Assign"
msgstr ""

#: inc/admin/views/tools/course/html-install-sample-data.php:19
msgid "Install Sample Data"
msgstr ""

#: inc/admin/views/tools/course/html-install-sample-data.php:20
msgid "Create a <strong>Sample course</strong> with lessons and quizzes. The content will be filled with <strong>Lorem</strong> text."
msgstr ""

#: inc/admin/views/tools/course/html-install-sample-data.php:26
msgid "Course name"
msgstr ""

#: inc/admin/views/tools/course/html-install-sample-data.php:30
msgid "Random number of sections in range"
msgstr ""

#: inc/admin/views/tools/course/html-install-sample-data.php:35
msgid "Random number of items in range (each section)"
msgstr ""

#: inc/admin/views/tools/course/html-install-sample-data.php:40
msgid "Random number of questions in range (each quiz)"
msgstr ""

#: inc/admin/views/tools/course/html-install-sample-data.php:45
msgid "Random number of answers in range (each question)"
msgstr ""

#: inc/admin/views/tools/course/html-install-sample-data.php:50
msgid "Course price"
msgstr ""

#: inc/admin/views/tools/course/html-install-sample-data.php:60
msgid "Installing..."
msgstr ""

#: inc/admin/views/tools/course/html-install-sample-data.php:64
msgid "Show options"
msgstr ""

#: inc/admin/views/tools/course/html-install-sample-data.php:66
#: inc/admin/views/tools/course/html-install-sample-data.php:69
msgid "Delete sample course"
msgstr ""

#: inc/admin/views/tools/course/html-install-sample-data.php:67
msgid "Deleting..."
msgstr ""

#: inc/admin/views/tools/course/html-unassign-course.php:71
msgid "Unassign Course"
msgstr ""

#: inc/admin/views/tools/course/html-unassign-course.php:73
msgid "Remove user from a course"
msgstr ""

#: inc/admin/views/tools/course/html-unassign-course.php:76
msgid "Noted: when remove user from course, the progress of user with course assign will eraser, so be careful before do this."
msgstr ""

#: inc/admin/views/tools/course/html-user-item.php:12
msgid "Reset Item Progress"
msgstr ""

#: inc/admin/views/tools/course/html-user-item.php:14
msgid "This action will reset progress of a specific lesson or quiz."
msgstr ""

#: inc/admin/views/tools/course/html-user-item.php:18
msgid "User ID or Email"
msgstr ""

#: inc/admin/views/tools/course/html-user-item.php:21
msgid "ID of quiz or lesson"
msgstr ""

#: inc/admin/views/tools/course/html-user-item.php:33
msgid "Are you sure to reset progress of this item?"
msgstr ""

#: inc/admin/views/tools/course/html-user.php:12
msgid "Reset User Progress"
msgstr ""

#: inc/admin/views/tools/course/html-user.php:14
msgid "This action will reset all course progresses of users."
msgstr ""

#: inc/admin/views/tools/course/html-user.php:15
msgid "Search results only show if users have course data."
msgstr ""

#: inc/admin/views/tools/course/html-user.php:19
msgid "Search user by login name or email"
msgstr ""

#: inc/admin/views/tools/course/html-user.php:56
msgid "Delete All"
msgstr ""

#: inc/admin/views/tools/course/html-user.php:63
msgid "Please enter at least 3 characters."
msgstr ""

#: inc/admin/views/tools/course/html-user.php:64
msgid "No user found."
msgstr ""

#: inc/admin/views/tools/course/html-user.php:65
msgid "Searching user..."
msgstr ""

#: inc/admin/views/tools/course/html-user.php:73
msgid "Are you sure to reset course progress of all users enrolled this course?"
msgstr ""

#: inc/admin/views/tools/database/html-clean-database.php:19
msgid "Clean Data System"
msgstr ""

#: inc/admin/views/tools/database/html-clean-database.php:20
msgid "Remove old data, not use or expire"
msgstr ""

#: inc/admin/views/tools/database/html-clean-database.php:37
msgid "rows expire"
msgstr ""

#: inc/admin/views/tools/database/html-clean-database.php:60
msgid "Table name"
msgstr ""

#: inc/admin/views/tools/database/html-clean-database.php:63
msgid "expire"
msgstr ""

#: inc/admin/views/tools/database/html-create-indexs-tables.php:16
msgid "Create Database Indexes"
msgstr ""

#: inc/admin/views/tools/database/html-create-indexs-tables.php:17
msgid "Re-create or create new indexes for tables."
msgstr ""

#: inc/admin/views/tools/database/html-create-indexs-tables.php:19
msgid "Create now"
msgstr ""

#: inc/admin/views/tools/database/html-reupgrade-db.php:16
msgid "Reupgrade Database"
msgstr ""

#: inc/admin/views/tools/database/html-reupgrade-db.php:17
msgid "1. Tool only one apply for case Update from LP3 to LP4 didn't success"
msgstr ""

#: inc/admin/views/tools/database/html-reupgrade-db.php:18
msgid "2. Please sure what you doing"
msgstr ""

#: inc/admin/views/tools/database/html-reupgrade-db.php:21
msgid "Run now"
msgstr ""

#: inc/admin/views/tools/database/html-upgrade-database.php:22
msgid "Upgrade Database"
msgstr ""

#: inc/admin/views/tools/database/html-upgrade-database.php:25
msgid "Upgrade now"
msgstr ""

#: inc/admin/views/tools/database/html-upgrade-database.php:84
msgid "I agree the new Terms of Service."
msgstr ""

#: inc/admin/views/tools/database/html-upgrade-database.php:87
msgid "Please agree to the terms before upgrade!"
msgstr ""

#: inc/admin/views/tools/database/html-upgrade-database.php:110
msgid "Please don't close this tab until the completed upgrade"
msgstr ""

#: inc/admin/views/tools/html-cache.php:12
msgid "LearnPress hard cache"
msgstr ""

#: inc/admin/views/tools/html-cache.php:13
msgid "Hard cache is build-in tool of LearnPress for caching of static content such as course, lesson, quiz."
msgstr ""

#: inc/admin/views/tools/html-cache.php:14
msgid "When caching is enabled, the content will be cached when course is accessed in the first time."
msgstr ""

#: inc/admin/views/tools/html-cache.php:15
msgid "And it will not change in all later accesses until the cache is cleared."
msgstr ""

#: inc/admin/views/tools/html-cache.php:16
msgid "If the content is not changed after updating course, click the button below to flush the cache and apply changes."
msgstr ""

#: inc/admin/views/tools/html-cache.php:21
msgid "Enable/Disable hard cache"
msgstr ""

#: inc/admin/views/tools/html-cache.php:25
#: inc/admin/views/tools/html-cache.php:28
msgid "Clear cache"
msgstr ""

#: inc/admin/views/tools/html-cache.php:26
msgid "Cleaning..."
msgstr ""

#: inc/admin/views/tools/html-template.php:30
msgid "Override Templates (%s)"
msgstr ""

#: inc/admin/views/tools/html-template.php:39
msgid "File"
msgstr ""

#: inc/admin/views/tools/html-template.php:42
msgid "All (%d)"
msgstr ""

#: inc/admin/views/tools/html-template.php:50
msgid "Outdated (%d)"
msgstr ""

#: inc/admin/views/tools/html-template.php:52
msgid "Unversioned (%d)"
msgstr ""

#: inc/admin/views/tools/html-template.php:56
msgid "Version"
msgstr ""

#: inc/admin/views/tools/html-template.php:58
msgid "Core version"
msgstr ""

#: inc/admin/views/tools/html-template.php:96
msgid "There is no template file has overwritten"
msgstr ""

#: inc/admin/views/tools/subscription-button.php:15
msgid "If you don't want to miss exclussive offers from us, join our newsletter."
msgstr ""

#: inc/admin/views/tools/subscription-button.php:19
msgid "Sure! I want to get the latest news."
msgstr ""

#: inc/admin/views/user/course-progress.php:24
#: inc/admin/views/user/course-progress.php:27
msgid "Items completed:"
msgstr ""

#: inc/admin/views/user/course-progress.php:30
msgid "%1$d of %2$d items"
msgstr ""

#: inc/admin/views/user/course-progress.php:35
#: templates/single-course/sidebar/user-progress.php:47
msgid "Course progress:"
msgstr ""

#: inc/admin/views/user/courses.php:22
msgid "The course list of enrolled users"
msgstr ""

#: inc/Ajax/EditCurriculumAjax.php:61
#: inc/Ajax/EditCurriculumAjax.php:107
#: inc/Ajax/EditCurriculumAjax.php:150
#: inc/Ajax/EditCurriculumAjax.php:191
#: inc/Ajax/EditCurriculumAjax.php:231
#: inc/Ajax/EditCurriculumAjax.php:280
#: inc/Ajax/EditCurriculumAjax.php:319
#: inc/Ajax/EditCurriculumAjax.php:376
#: inc/Ajax/EditCurriculumAjax.php:444
#: inc/Ajax/EditCurriculumAjax.php:497
#: inc/Ajax/EditCurriculumAjax.php:553
#: inc/Models/CourseSectionModel.php:210
#: inc/Models/CourseSectionModel.php:270
#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:475
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:78
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:597
msgid "Course not found"
msgstr ""

#: inc/Ajax/EditCurriculumAjax.php:66
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:280
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:321
msgid "Section title is required"
msgstr ""

#: inc/Ajax/EditCurriculumAjax.php:80
msgid "Section added successfully"
msgstr ""

#: inc/Ajax/EditCurriculumAjax.php:112
#: inc/Ajax/EditCurriculumAjax.php:155
#: inc/Ajax/EditCurriculumAjax.php:236
#: inc/Ajax/EditCurriculumAjax.php:285
#: inc/Ajax/EditCurriculumAjax.php:324
#: inc/Ajax/EditCurriculumAjax.php:502
msgid "Section not found"
msgstr ""

#: inc/Ajax/EditCurriculumAjax.php:124
#: inc/Ajax/EditCurriculumAjax.php:161
#: inc/Ajax/EditCurriculumAjax.php:200
msgid "Section updated successfully"
msgstr ""

#: inc/Ajax/EditCurriculumAjax.php:186
msgid "Invalid section position"
msgstr ""

#: inc/Ajax/EditCurriculumAjax.php:250
msgid "Item added to section successfully"
msgstr ""

#: inc/Ajax/EditCurriculumAjax.php:291
msgid "Items added to section successfully"
msgstr ""

#: inc/Ajax/EditCurriculumAjax.php:330
#: inc/Ajax/EditCurriculumAjax.php:387
#: inc/Ajax/EditCurriculumAjax.php:507
msgid "Item not found in section"
msgstr ""

#: inc/Ajax/EditCurriculumAjax.php:338
msgid "Item deleted from section successfully"
msgstr ""

#: inc/Ajax/EditCurriculumAjax.php:371
#: inc/Ajax/EditCurriculumAjax.php:448
msgid "Invalid item position"
msgstr ""

#: inc/Ajax/EditCurriculumAjax.php:413
#: inc/Ajax/EditCurriculumAjax.php:458
msgid "Item position updated successfully"
msgstr ""

#: inc/Ajax/EditCurriculumAjax.php:492
#: inc/Models/CourseSectionModel.php:220
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:393
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:458
msgid "Item title is required"
msgstr ""

#: inc/Ajax/EditCurriculumAjax.php:515
#: inc/Ajax/EditCurriculumAjax.php:565
msgid "Item not found"
msgstr ""

#: inc/Ajax/EditCurriculumAjax.php:525
#: inc/Ajax/EditCurriculumAjax.php:571
msgid "Item updated successfully"
msgstr ""

#: inc/Ajax/EditCurriculumAjax.php:557
msgid "Only lesson can be set preview"
msgstr ""

#: inc/Ajax/LessonAjax.php:39
#: inc/class-lp-checkout.php:477
msgid "Course is invalid!"
msgstr ""

#: inc/Ajax/LessonAjax.php:44
msgid "Lesson is invalid!"
msgstr ""

#: inc/Ajax/LessonAjax.php:56
#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:234
msgid "You have not started lesson"
msgstr ""

#: inc/Ajax/LessonAjax.php:70
#: inc/class-lp-ajax.php:251
msgid "Congrats! You have completed \"%s\"."
msgstr ""

#: inc/background-process/class-lp-background-query-items.php:128
#: inc/background-process/class-lp-background-query-items.php:182
msgid "No items found!"
msgstr ""

#: inc/background-process/class-lp-background-query-items.php:133
msgid "WP query plugins error!"
msgstr ""

#: inc/background-process/class-lp-background-query-items.php:136
msgid "WP query plugins empty!"
msgstr ""

#: inc/background-process/class-lp-background-query-items.php:232
msgid "No item found!"
msgstr ""

#: inc/cart/class-lp-cart.php:93
msgid "Course is not exists!"
msgstr ""

#: inc/class-lp-ajax.php:100
msgid "The order %s has been successfully recovered."
msgstr ""

#: inc/class-lp-ajax.php:120
msgid "Your email already exists. Do you want to continue with this email?"
msgstr ""

#: inc/class-lp-ajax.php:127
msgid "Create a new account with this email. The account information will be sent with this email."
msgstr ""

#: inc/class-lp-ajax.php:133
msgid "The system does not allow the creation of a new account, you must enter an existing account."
msgstr ""

#: inc/class-lp-ajax.php:166
msgid "Request is invalid!"
msgstr ""

#: inc/class-lp-ajax.php:171
msgid "You have not enrolled in this course."
msgstr ""

#: inc/class-lp-ajax.php:184
msgid "Course has been finished successfully."
msgstr ""

#: inc/class-lp-ajax.php:213
msgid "Error! Invalid lesson or failed security check."
msgstr ""

#: inc/class-lp-ajax.php:218
msgid "Error! Invalid lesson."
msgstr ""

#: inc/class-lp-ajax.php:223
msgid "Please login."
msgstr ""

#: inc/class-lp-ajax.php:228
msgid "Course is invalid!."
msgstr ""

#: inc/class-lp-assets.php:136
msgid "Redirecting"
msgstr ""

#: inc/class-lp-assets.php:137
msgid "Invalid field"
msgstr ""

#: inc/class-lp-assets.php:138
msgid "Unknown error"
msgstr ""

#: inc/class-lp-assets.php:139
#: templates/checkout/payment.php:70
msgid "Place order"
msgstr ""

#: inc/class-lp-assets.php:143
#: inc/TemplateHooks/Profile/ProfileTemplate.php:170
#: inc/TemplateHooks/Profile/ProfileTemplate.php:266
#: templates/profile/tabs/settings/avatar.php:25
msgid "Replace"
msgstr ""

#: inc/class-lp-breadcrumb.php:124
msgid "Error 404"
msgstr ""

#: inc/class-lp-breadcrumb.php:229
msgid "Courses tagged &ldquo;%s&rdquo;"
msgstr ""

#: inc/class-lp-breadcrumb.php:287
msgid "Posts tagged &ldquo;%s&rdquo;"
msgstr ""

#: inc/class-lp-breadcrumb.php:365
msgid "Author: %s"
msgstr ""

#: inc/class-lp-breadcrumb.php:398
msgid "Search results for &ldquo;%s&rdquo;"
msgstr ""

#: inc/class-lp-breadcrumb.php:407
msgid "Page %d"
msgstr ""

#: inc/class-lp-checkout.php:172
msgid "Your email is not valid!"
msgstr ""

#: inc/class-lp-checkout.php:250
msgid "New account email is existed"
msgstr ""

#: inc/class-lp-checkout.php:260
msgid "Create account failed"
msgstr ""

#: inc/class-lp-checkout.php:306
msgid "Error %d: Unable to add item to order. Please try again."
msgstr ""

#: inc/class-lp-checkout.php:399
msgid "Your session has expired."
msgstr ""

#: inc/class-lp-checkout.php:421
msgid "No payment method is selected"
msgstr ""

#: inc/class-lp-checkout.php:450
#: templates/checkout/empty-cart.php:16
msgid "Your cart is currently empty."
msgstr ""

#: inc/class-lp-checkout.php:457
msgid "Type item buy invalid!"
msgstr ""

#: inc/class-lp-datetime.php:119
#: inc/custom-post-types/order.php:528
#: inc/order/class-lp-order.php:151
msgid "%s ago"
msgstr ""

#: inc/class-lp-datetime.php:305
msgid "%s Second"
msgid_plural "%s Seconds"
msgstr[0] ""
msgstr[1] ""

#: inc/class-lp-datetime.php:311
msgid "%s Minute"
msgid_plural "%s Minutes"
msgstr[0] ""
msgstr[1] ""

#: inc/class-lp-datetime.php:317
msgid "%s Hour"
msgid_plural "%s Hours"
msgstr[0] ""
msgstr[1] ""

#: inc/class-lp-datetime.php:323
msgid "%s Day"
msgid_plural "%s Days"
msgstr[0] ""
msgstr[1] ""

#: inc/class-lp-datetime.php:329
msgid "%s Week"
msgid_plural "%s Weeks"
msgstr[0] ""
msgstr[1] ""

#: inc/class-lp-datetime.php:335
msgid "%s Month"
msgid_plural "%s Months"
msgstr[0] ""
msgstr[1] ""

#: inc/class-lp-datetime.php:341
msgid "%s Year"
msgid_plural "%s Years"
msgstr[0] ""
msgstr[1] ""

#: inc/class-lp-datetime.php:364
msgid "Timezone: UTC"
msgstr ""

#: inc/class-lp-datetime.php:366
msgid "Timezone:"
msgstr ""

#: inc/class-lp-forms-handler.php:31
#: inc/class-lp-forms-handler.php:254
msgid "Please enter a valid account username."
msgstr ""

#: inc/class-lp-forms-handler.php:38
#: inc/class-lp-forms-handler.php:244
msgid "Please provide a valid email address."
msgstr ""

#: inc/class-lp-forms-handler.php:45
msgid "Your email does not exist!"
msgstr ""

#: inc/class-lp-forms-handler.php:53
msgid "Thank you! Your message has been sent."
msgstr ""

#: inc/class-lp-forms-handler.php:81
msgid "Error:"
msgstr ""

#: inc/class-lp-forms-handler.php:81
msgid "A username is required"
msgstr ""

#: inc/class-lp-forms-handler.php:119
msgid "Login successfully!"
msgstr ""

#: inc/class-lp-forms-handler.php:182
msgid "I need to become an instructor"
msgstr ""

#: inc/class-lp-forms-handler.php:197
msgid " was successfully created!"
msgstr ""

#: inc/class-lp-forms-handler.php:200
msgid "Your request to become an instructor has been sent. We will get back to you soon!"
msgstr ""

#: inc/class-lp-forms-handler.php:240
msgid "System WordPress does not allow register."
msgstr ""

#: inc/class-lp-forms-handler.php:248
msgid "An account is already registered with your email address."
msgstr ""

#: inc/class-lp-forms-handler.php:259
msgid "An account is already registered with that username. Please choose another one."
msgstr ""

#: inc/class-lp-forms-handler.php:269
msgid "Please enter an account password."
msgstr ""

#: inc/class-lp-forms-handler.php:273
msgid "Password must contain at least six characters."
msgstr ""

#: inc/class-lp-forms-handler.php:277
msgid "Password can not contain spaces!"
msgstr ""

#: inc/class-lp-forms-handler.php:281
msgid "Please enter confirm password."
msgstr ""

#: inc/class-lp-forms-handler.php:285
msgid "Password and Confirm Password does not match!"
msgstr ""

#: inc/class-lp-forms-handler.php:292
#: inc/class-lp-forms-handler.php:408
msgid " is required field."
msgstr ""

#: inc/class-lp-forms-handler.php:381
msgid "Email is required"
msgstr ""

#: inc/class-lp-forms-handler.php:385
msgid "Display name is required"
msgstr ""

#: inc/class-lp-forms-handler.php:389
#: inc/class-lp-forms-handler.php:393
msgid "Due to privacy concerns, the display name cannot be changed to an email address."
msgstr ""

#: inc/class-lp-forms-handler.php:395
msgid "This email address is already registered."
msgstr ""

#: inc/class-lp-forms-handler.php:432
msgid "Enter a username or email address."
msgstr ""

#: inc/class-lp-forms-handler.php:452
#: inc/class-lp-forms-handler.php:456
msgid "Invalid username or email."
msgstr ""

#: inc/class-lp-forms-handler.php:467
msgid "Password reset is not allowed for this user."
msgstr ""

#: inc/class-lp-helper.php:235
msgid "Missing post title"
msgstr ""

#: inc/class-lp-helper.php:490
msgctxt "slug"
msgid "uncategorized"
msgstr ""

#: inc/class-lp-helper.php:575
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:533
msgid "Assignment"
msgid_plural "Assignments"
msgstr[0] ""
msgstr[1] ""

#: inc/class-lp-helper.php:581
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:536
msgid "H5P"
msgid_plural "H5Ps"
msgstr[0] ""
msgstr[1] ""

#: inc/class-lp-manager-addons.php:129
msgid "Install failed!"
msgstr ""

#: inc/class-lp-manager-addons.php:161
#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:385
msgid "Update failed!"
msgstr ""

#: inc/class-lp-page-controller.php:233
#: inc/lp-template-functions.php:320
msgid "Course Search Results"
msgstr ""

#: inc/class-lp-query-list-table.php:33
msgid "items"
msgstr ""

#: inc/class-lp-query-list-table.php:150
#: inc/TemplateHooks/Table/TableListTemplate.php:147
msgid "Displaying {{from}} to {{to}} of {{total}} {{item_name}}."
msgstr ""

#: inc/class-lp-query-list-table.php:152
msgid "Displaying {{from}} to {{to}} of {{total}}."
msgstr ""

#: inc/class-lp-shortcodes.php:236
msgid "Forgot password?"
msgstr ""

#: inc/class-lp-shortcodes.php:238
msgid "Create a new account"
msgstr ""

#: inc/class-lp-strings.php:23
msgid "You've already completed the quiz."
msgstr ""

#: inc/class-lp-strings.php:24
msgid "Do you want to redo the quiz \"%s\"?"
msgstr ""

#: inc/class-lp-strings.php:25
msgid "Do you want to complete the quiz \"%s\"?"
msgstr ""

#: inc/class-lp-strings.php:26
msgid "Do you want to complete the lesson \"%s\"?"
msgstr ""

#: inc/class-lp-strings.php:27
msgid "Do you want to finish the course \"%s\"?"
msgstr ""

#: inc/class-lp-strings.php:28
msgid "Do you want to retake the course \"%s\"?"
msgstr ""

#: inc/class-lp-widget.php:262
#: inc/Widgets/LPWidgetBase.php:41
msgid "There are no options for this widget."
msgstr ""

#: inc/course/abstract-course.php:793
#: inc/course/abstract-course.php:813
#: inc/lesson/class-lp-lesson.php:104
#: inc/quiz/class-lp-quiz.php:648
msgid "The function %s doesn't exist"
msgstr ""

#: inc/course/abstract-course.php:1344
msgid "This course will end within the next %s"
msgstr ""

#: inc/course/abstract-course.php:1372
msgctxt "External Link button text"
msgid "More Info"
msgstr ""

#: inc/course/abstract-course.php:1409
msgid "Tags: "
msgstr ""

#: inc/course/class-lp-course-item.php:277
msgctxt "course item status title"
msgid "Failed"
msgstr ""

#: inc/course/class-lp-course-item.php:279
msgctxt "course item status title"
msgid "Passed"
msgstr ""

#: inc/course/class-lp-course-item.php:281
msgctxt "course item status title"
msgid "Completed"
msgstr ""

#: inc/course/class-lp-course-item.php:284
msgctxt "course item status title"
msgid "Unread"
msgstr ""

#: inc/course/lp-course-functions.php:945
#: inc/lp-core-functions.php:2018
#: inc/lp-deprecated.php:214
#: inc/Models/UserItems/UserItemModel.php:531
#: inc/Models/UserItems/UserQuizModel.php:624
#: inc/Models/UserItems/UserQuizModel.php:625
#: inc/templates/class-lp-template-profile.php:159
#: inc/user-item/class-lp-user-item-quiz.php:144
#: inc/user-item/class-lp-user-item-quiz.php:145
msgid "In Progress"
msgstr ""

#: inc/course/lp-course-functions.php:1028
msgid "completed lessons per the total number of lessons."
msgstr ""

#: inc/course/lp-course-functions.php:1031
msgid "passed quizzes per the total number of quizzes."
msgstr ""

#: inc/course/lp-course-functions.php:1034
msgid "Final Quiz"
msgstr ""

#: inc/course/lp-course-functions.php:1037
msgid "correct answers per the total number of questions."
msgstr ""

#: inc/course/lp-course-functions.php:1040
msgid "score achieved per the total score of the questions."
msgstr ""

#: inc/course/lp-course-functions.php:1049
msgid "Require"
msgstr ""

#: inc/curds/class-lp-course-curd.php:27
msgid "The course does not exist."
msgstr ""

#: inc/curds/class-lp-lesson-curd.php:35
msgid "New Lesson"
msgstr ""

#: inc/curds/class-lp-lesson-curd.php:139
msgid "Invalid lesson with ID \"%d\"."
msgstr ""

#: inc/curds/class-lp-order-curd.php:478
msgid "Invalid order with ID \"%d\"."
msgstr ""

#: inc/curds/class-lp-order-curd.php:540
msgid "Invalid Order key."
msgstr ""

#: inc/curds/class-lp-order-curd.php:544
msgid "Order key is already assigned."
msgstr ""

#: inc/curds/class-lp-order-curd.php:550
msgid "Invalid User!"
msgstr ""

#: inc/curds/class-lp-order-curd.php:554
msgid "Invalid order key with Email!"
msgstr ""

#: inc/curds/class-lp-question-curd.php:28
msgid "The question does not exist."
msgstr ""

#: inc/curds/class-lp-question-curd.php:228
#: inc/curds/class-lp-question-curd.php:240
msgid "Failed to duplicate answer"
msgstr ""

#: inc/curds/class-lp-question-curd.php:283
msgid "Invalid question with ID \"%d\"."
msgstr ""

#: inc/curds/class-lp-quiz-curd.php:29
#: inc/curds/class-lp-user-item-curd.php:21
msgid "The quiz does not exist."
msgstr ""

#: inc/curds/class-lp-quiz-curd.php:43
msgid "Invalid quiz with ID \"%d\"."
msgstr ""

#: inc/curds/class-lp-user-item-curd.php:35
msgid "Invalid quiz."
msgstr ""

#: inc/custom-post-types/abstract.php:514
msgid "This item has already been assigned to the course. It will be removed from the course if it is not published."
msgstr ""

#: inc/custom-post-types/abstract.php:529
msgid "This question has already been assigned to the quiz. It will be removed from the quiz if it is not published."
msgstr ""

#: inc/custom-post-types/abstract.php:806
#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:203
msgid "Unknown"
msgstr ""

#: inc/custom-post-types/abstract.php:887
msgid "Course (%1$d %2$s)"
msgid_plural "Course (%1$d %2$s)"
msgstr[0] ""
msgstr[1] ""

#: inc/custom-post-types/abstract.php:914
msgid "Remove Filter"
msgstr ""

#: inc/custom-post-types/abstract.php:1005
#: inc/custom-post-types/abstract.php:1008
msgid "updated."
msgstr ""

#: inc/custom-post-types/abstract.php:1006
msgid "Custom field updated."
msgstr ""

#: inc/custom-post-types/abstract.php:1007
msgid "Custom field deleted."
msgstr ""

#. translators: %s: date and time of the revision
#: inc/custom-post-types/abstract.php:1010
msgid "The lesson has been restored to revision from %s"
msgstr ""

#: inc/custom-post-types/abstract.php:1011
msgid "published."
msgstr ""

#: inc/custom-post-types/abstract.php:1012
msgid "saved."
msgstr ""

#: inc/custom-post-types/abstract.php:1013
msgid "submitted."
msgstr ""

#: inc/custom-post-types/abstract.php:1015
msgid "scheduled for: <strong>%1$s</strong>."
msgstr ""

#. translators: Publish box date format, see http://php.net/date
#: inc/custom-post-types/abstract.php:1017
msgid "M j, Y @ G:i"
msgstr ""

#: inc/custom-post-types/abstract.php:1019
msgid "draft updated."
msgstr ""

#: inc/custom-post-types/course.php:51
msgctxt "Post Type General Name"
msgid "Courses"
msgstr ""

#: inc/custom-post-types/course.php:52
msgctxt "Post Type Singular Name"
msgid "Course"
msgstr ""

#: inc/custom-post-types/course.php:54
msgid "Parent Item:"
msgstr ""

#: inc/custom-post-types/course.php:56
msgid "View Course"
msgstr ""

#: inc/custom-post-types/course.php:57
msgid "Add a New Course"
msgstr ""

#: inc/custom-post-types/course.php:58
#: inc/custom-post-types/lesson.php:180
#: inc/custom-post-types/order.php:601
#: inc/custom-post-types/question.php:211
msgid "Add New"
msgstr ""

#: inc/custom-post-types/course.php:59
msgid "Edit Course"
msgstr ""

#: inc/custom-post-types/course.php:60
msgid "Update Course"
msgstr ""

#: inc/custom-post-types/course.php:61
msgid "Search Courses"
msgstr ""

#: inc/custom-post-types/course.php:63
msgid "You have not had any courses yet. Click <a href=\"%s\">Add new</a> to start"
msgstr ""

#: inc/custom-post-types/course.php:66
msgid "There was no course found in the trash"
msgstr ""

#: inc/custom-post-types/course.php:109
#: inc/custom-post-types/course.php:111
msgid "Course Categories"
msgstr ""

#: inc/custom-post-types/course.php:114
msgid "Add A New Course Category"
msgstr ""

#: inc/custom-post-types/course.php:115
msgid "All Categories"
msgstr ""

#: inc/custom-post-types/course.php:140
#: inc/custom-post-types/course.php:154
msgid "Course Tags"
msgstr ""

#: inc/custom-post-types/course.php:142
msgid "Search Course Tags"
msgstr ""

#: inc/custom-post-types/course.php:143
msgid "Popular Course Tags"
msgstr ""

#: inc/custom-post-types/course.php:144
msgid "All Course Tags"
msgstr ""

#: inc/custom-post-types/course.php:147
msgid "Edit Course Tag"
msgstr ""

#: inc/custom-post-types/course.php:148
msgid "Update Course Tag"
msgstr ""

#: inc/custom-post-types/course.php:149
msgid "Add A New Course Tag"
msgstr ""

#: inc/custom-post-types/course.php:150
msgid "New Course Tag Name"
msgstr ""

#: inc/custom-post-types/course.php:151
msgid "Separate tags with commas"
msgstr ""

#: inc/custom-post-types/course.php:152
msgid "Add or remove tags"
msgstr ""

#: inc/custom-post-types/course.php:153
msgid "Choose from the most used tags"
msgstr ""

#: inc/custom-post-types/course.php:361
#: inc/custom-post-types/course.php:364
msgid "Thumbnail"
msgstr ""

#: inc/custom-post-types/course.php:408
msgid "<strong>%d</strong> section"
msgid_plural "<strong>%d</strong> sections"
msgstr[0] ""
msgstr[1] ""

#: inc/custom-post-types/course.php:443
msgid "No content"
msgstr ""

#: inc/custom-post-types/lesson.php:137
#: inc/custom-post-types/question.php:94
#: inc/custom-post-types/quiz.php:71
msgid "Unassigned"
msgstr ""

#: inc/custom-post-types/lesson.php:159
msgid "No Preview"
msgstr ""

#: inc/custom-post-types/lesson.php:177
msgid "Add A New Lesson"
msgstr ""

#: inc/custom-post-types/lesson.php:179
msgid "View Lesson"
msgstr ""

#: inc/custom-post-types/lesson.php:181
msgid "Edit Lesson"
msgstr ""

#: inc/custom-post-types/lesson.php:182
msgid "Update Lesson"
msgstr ""

#: inc/custom-post-types/lesson.php:183
msgid "Search Lessons"
msgstr ""

#: inc/custom-post-types/lesson.php:184
msgid "No lesson found"
msgstr ""

#: inc/custom-post-types/lesson.php:185
msgid "There was no lesson found in the trash"
msgstr ""

#: inc/custom-post-types/lesson.php:243
msgid "Format"
msgstr ""

#: inc/custom-post-types/lesson.php:291
msgid "Standard"
msgstr ""

#: inc/custom-post-types/lesson.php:320
#: inc/custom-post-types/question.php:483
#: inc/custom-post-types/quiz.php:440
msgid "Assigned"
msgstr ""

#: inc/custom-post-types/order.php:199
msgid "Order number, course name, etc."
msgstr ""

#: inc/custom-post-types/order.php:454
#: inc/custom-post-types/order.php:596
#: templates/profile/tabs/orders/list.php:30
msgid "Order"
msgstr ""

#: inc/custom-post-types/order.php:455
#: inc/Gutenberg/Blocks/SingleCourseElements/CourseStudentBlockType.php:79
#: inc/TemplateHooks/Course/ListCoursesTemplate.php:777
#: inc/TemplateHooks/Course/SingleCourseModernLayout.php:206
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:463
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:489
#: inc/TemplateHooks/Instructor/SingleInstructorTemplate.php:105
msgid "Student"
msgid_plural "Students"
msgstr[0] ""
msgstr[1] ""

#: inc/custom-post-types/order.php:456
#: inc/user-item/class-lp-user-item.php:688
msgid "Purchased"
msgstr ""

#: inc/custom-post-types/order.php:457
#: templates/checkout/order-received.php:104
#: templates/profile/tabs/orders/list.php:33
#: templates/profile/tabs/quizzes.php:59
msgid "Date"
msgstr ""

#: inc/custom-post-types/order.php:459
#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1628
#: templates/checkout/order-received.php:61
#: templates/emails/order-items-table.php:74
#: templates/emails/plain/order-items-table.php:58
#: templates/profile/tabs/orders/list.php:32
msgid "Status"
msgstr ""

#: inc/custom-post-types/order.php:508
#: inc/order/class-lp-order.php:460
msgid "(Guest)"
msgstr ""

#: inc/custom-post-types/order.php:543
#: templates/checkout/order-received.php:81
msgid "The course does not exist"
msgstr ""

#: inc/custom-post-types/order.php:545
msgid "Deleted"
msgstr ""

#: inc/custom-post-types/order.php:561
#: templates/checkout/order-received.php:98
msgid "(No item)"
msgstr ""

#: inc/custom-post-types/order.php:571
msgid "Pay via <strong>%s</strong>"
msgstr ""

#: inc/custom-post-types/order.php:597
msgid "Add A New Order"
msgstr ""

#: inc/custom-post-types/order.php:598
#: inc/custom-post-types/order.php:729
#: templates/order/order-details.php:23
msgid "Order Details"
msgstr ""

#: inc/custom-post-types/order.php:600
msgid "View Order"
msgstr ""

#: inc/custom-post-types/order.php:602
msgid "Update Order"
msgstr ""

#: inc/custom-post-types/order.php:603
msgid "Search Orders"
msgstr ""

#: inc/custom-post-types/order.php:604
msgid "No order found"
msgstr ""

#: inc/custom-post-types/order.php:605
msgid "There was no order found in the trash"
msgstr ""

#: inc/custom-post-types/order.php:737
msgid "Order Actions"
msgstr ""

#: inc/custom-post-types/order.php:745
msgid "Order Exports"
msgstr ""

#: inc/custom-post-types/question.php:165
#: inc/custom-post-types/quiz.php:174
msgid "New Option"
msgstr ""

#: inc/custom-post-types/question.php:166
#: inc/custom-post-types/quiz.php:180
msgid "Are you sure to remove all the blanks?"
msgstr ""

#: inc/custom-post-types/question.php:183
msgid "Question Tag"
msgstr ""

#: inc/custom-post-types/question.php:186
msgid "Add A New Tag"
msgstr ""

#: inc/custom-post-types/question.php:187
msgid "All Tags"
msgstr ""

#: inc/custom-post-types/question.php:205
#: inc/custom-post-types/question.php:206
msgid "Question Bank"
msgstr ""

#: inc/custom-post-types/question.php:209
msgid "View Question"
msgstr ""

#: inc/custom-post-types/question.php:210
msgid "Add A New Question"
msgstr ""

#: inc/custom-post-types/question.php:212
msgid "Edit Question"
msgstr ""

#: inc/custom-post-types/question.php:213
msgid "Update Question"
msgstr ""

#: inc/custom-post-types/question.php:214
msgid "Search Questions"
msgstr ""

#: inc/custom-post-types/question.php:215
msgid "No questions found"
msgstr ""

#: inc/custom-post-types/question.php:216
msgid "There was no questions found in the trash"
msgstr ""

#: inc/custom-post-types/question.php:491
msgid "Answer Options"
msgstr ""

#: inc/custom-post-types/quiz.php:90
msgid "Add A New Quiz"
msgstr ""

#: inc/custom-post-types/quiz.php:91
msgid "Edit Quiz"
msgstr ""

#: inc/custom-post-types/quiz.php:93
msgid "View Quiz"
msgstr ""

#: inc/custom-post-types/quiz.php:95
msgid "Update Quiz"
msgstr ""

#: inc/custom-post-types/quiz.php:96
msgid "Search Quizzes"
msgstr ""

#: inc/custom-post-types/quiz.php:97
msgid "You haven't had any quizzes yet. Click <a href=\"%s\">Add new</a> to start"
msgstr ""

#: inc/custom-post-types/quiz.php:98
msgid "There was no quiz found in the trash"
msgstr ""

#: inc/custom-post-types/quiz.php:170
msgid "Option"
msgstr ""

#: inc/custom-post-types/quiz.php:175
msgid "Do you want to move the \"{{QUESTION_NAME}}\" question to the trash?"
msgstr ""

#: inc/custom-post-types/quiz.php:288
msgid "%d question"
msgid_plural "%d questions"
msgstr[0] ""
msgstr[1] ""

#: inc/custom-post-types/quiz.php:289
msgid "This quiz has no questions"
msgstr ""

#: inc/custom-post-types/quiz.php:302
#: inc/quiz/class-lp-quiz.php:518
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:461
msgid "Unlimited"
msgstr ""

#: inc/Databases/class-lp-course-json-db.php:144
msgid "Invalid ID!"
msgstr ""

#: inc/Databases/class-lp-order-db.php:111
#: inc/Databases/class-lp-order-db.php:145
#: inc/Databases/class-lp-user-items-db.php:224
#: inc/Databases/class-lp-user-items-db.php:544
msgid "Invalid user!"
msgstr ""

#: inc/Databases/class-lp-section-db.php:176
msgid "No course id"
msgstr ""

#: inc/Databases/class-lp-section-db.php:244
msgid "No section id"
msgstr ""

#: inc/Databases/class-lp-section-db.php:470
msgid "Invalid section_id!"
msgstr ""

#: inc/Databases/class-lp-section-items-db.php:203
msgid "Invalid section_item_id!"
msgstr ""

#: inc/Databases/class-lp-user-item-meta-db.php:108
msgid "Invalid meta id!"
msgstr ""

#: inc/Databases/class-lp-user-items-db.php:80
msgid "Invalid user item id!"
msgstr ""

#: inc/emails/admin/class-lp-email-become-an-instructor.php:24
msgid "Request"
msgstr ""

#: inc/emails/admin/class-lp-email-become-an-instructor.php:25
msgid "Become an instructor email."
msgstr ""

#: inc/emails/admin/class-lp-email-become-an-instructor.php:27
msgid "[{{site_title}}] Request to become an instructor"
msgstr ""

#: inc/emails/admin/class-lp-email-become-an-instructor.php:28
msgid "Become an instructor"
msgstr ""

#: inc/emails/admin/class-lp-email-cancelled-order-admin.php:25
#: inc/emails/admin/class-lp-email-completed-order-admin.php:26
#: inc/emails/admin/class-lp-email-enrolled-course-admin.php:28
#: inc/emails/admin/class-lp-email-finished-course-admin.php:26
#: inc/emails/admin/class-lp-email-new-order-admin.php:25
msgid "Admin"
msgstr ""

#: inc/emails/admin/class-lp-email-cancelled-order-admin.php:26
msgid "Send an email to admin when the order has been canceled."
msgstr ""

#: inc/emails/admin/class-lp-email-cancelled-order-admin.php:27
#: inc/emails/instructor/class-lp-email-cancelled-order-instructor.php:28
msgid "The order placed on {{order_date}} has been cancelled"
msgstr ""

#: inc/emails/admin/class-lp-email-cancelled-order-admin.php:28
#: inc/emails/instructor/class-lp-email-cancelled-order-instructor.php:29
msgid "The user order has been cancelled"
msgstr ""

#: inc/emails/admin/class-lp-email-completed-order-admin.php:27
msgid "Send an email to admin when an order has been completed."
msgstr ""

#: inc/emails/admin/class-lp-email-completed-order-admin.php:28
msgid "The order placed on {{order_date}} has been completed"
msgstr ""

#: inc/emails/admin/class-lp-email-completed-order-admin.php:29
msgid "The user order has been completed"
msgstr ""

#: inc/emails/admin/class-lp-email-enrolled-course-admin.php:29
msgid "Send an email to admin when the user has enrolled in the course."
msgstr ""

#: inc/emails/admin/class-lp-email-enrolled-course-admin.php:30
#: inc/emails/instructor/class-lp-email-enrolled-course-instructor.php:28
msgid "{{user_display_name}} has enrolled in the course"
msgstr ""

#: inc/emails/admin/class-lp-email-enrolled-course-admin.php:31
#: inc/emails/instructor/class-lp-email-enrolled-course-instructor.php:29
msgid "The user has enrolled in the course"
msgstr ""

#: inc/emails/admin/class-lp-email-finished-course-admin.php:27
msgid "Send an email to admin when the user has finished the course."
msgstr ""

#: inc/emails/admin/class-lp-email-finished-course-admin.php:28
#: inc/emails/instructor/class-lp-email-finished-course-instructor.php:28
msgid "{{user_display_name}} has completed the course."
msgstr ""

#: inc/emails/admin/class-lp-email-finished-course-admin.php:29
#: inc/emails/instructor/class-lp-email-finished-course-instructor.php:29
msgid "The user has completed the course."
msgstr ""

#: inc/emails/admin/class-lp-email-new-order-admin.php:26
msgid "Notify admin when a new order is placed."
msgstr ""

#: inc/emails/admin/class-lp-email-new-order-admin.php:28
#: inc/emails/instructor/class-lp-email-new-order-instructor.php:24
msgid "New order placed on {{order_date}}"
msgstr ""

#: inc/emails/admin/class-lp-email-new-order-admin.php:29
#: inc/emails/instructor/class-lp-email-new-order-instructor.php:25
msgid "New user order"
msgstr ""

#: inc/emails/class-lp-email.php:886
msgid "Recipient(s)"
msgstr ""

#: inc/emails/class-lp-email.php:890
msgid "Separate other recipients with commas."
msgstr ""

#: inc/emails/class-lp-email.php:895
msgid "Subject"
msgstr ""

#: inc/emails/class-lp-email.php:902
msgid "Email heading"
msgstr ""

#: inc/emails/guest/class-lp-email-cancelled-order-guest.php:26
msgid "Send an email to the guest when the order has been cancelled."
msgstr ""

#: inc/emails/guest/class-lp-email-cancelled-order-guest.php:28
#: inc/emails/student/class-lp-email-cancelled-order-user.php:28
msgid "Your order on {{order_date}} has been cancelled"
msgstr ""

#: inc/emails/guest/class-lp-email-cancelled-order-guest.php:29
#: inc/emails/student/class-lp-email-cancelled-order-user.php:29
msgid "Your order has been cancelled"
msgstr ""

#: inc/emails/guest/class-lp-email-completed-order-guest.php:30
#: inc/emails/guest/class-lp-email-new-order-guest.php:26
msgid "Send an email to the user who has bought the course as a guest."
msgstr ""

#: inc/emails/guest/class-lp-email-completed-order-guest.php:32
#: inc/emails/student/class-lp-email-completed-order-user.php:35
msgid "Your order on {{order_date}} has completed"
msgstr ""

#: inc/emails/guest/class-lp-email-completed-order-guest.php:33
#: inc/emails/student/class-lp-email-completed-order-user.php:36
msgid "Your order has completed"
msgstr ""

#: inc/emails/guest/class-lp-email-new-order-guest.php:28
#: inc/emails/guest/class-lp-email-processing-order-guest.php:25
#: inc/emails/student/class-lp-email-new-order-user.php:28
#: inc/emails/student/class-lp-email-processing-order-user.php:25
msgid "Your order placed on {{order_date}}"
msgstr ""

#: inc/emails/guest/class-lp-email-new-order-guest.php:29
#: inc/emails/guest/class-lp-email-processing-order-guest.php:26
#: inc/emails/student/class-lp-email-new-order-user.php:29
#: inc/emails/student/class-lp-email-processing-order-user.php:26
msgid "Thank you for your order"
msgstr ""

#: inc/emails/guest/class-lp-email-processing-order-guest.php:23
msgid "When the order is processed, send an email to the user who purchased the course as a guest."
msgstr ""

#: inc/emails/instructor/class-lp-email-cancelled-order-instructor.php:25
#: inc/emails/instructor/class-lp-email-enrolled-course-instructor.php:25
#: inc/emails/instructor/class-lp-email-finished-course-instructor.php:25
#: inc/emails/instructor/class-lp-email-new-order-instructor.php:21
#: inc/lp-template-functions.php:80
#: inc/TemplateHooks/Course/SingleCourseClassicTemplate.php:211
#: inc/TemplateHooks/Course/SingleCourseClassicTemplate.php:269
#: inc/TemplateHooks/Course/SingleCourseModernLayout.php:454
#: inc/TemplateHooks/Course/SingleCourseOfflineTemplate.php:119
#: templates/single-course/meta/instructor.php:31
msgid "Instructor"
msgstr ""

#: inc/emails/instructor/class-lp-email-cancelled-order-instructor.php:26
msgid "Send an email to the course instructor when the order has been cancelled."
msgstr ""

#: inc/emails/instructor/class-lp-email-enrolled-course-instructor.php:26
msgid "Send this email to the instructor when they have enrolled in the course."
msgstr ""

#: inc/emails/instructor/class-lp-email-finished-course-instructor.php:26
msgid "Send this email to the instructor when they have finished the course."
msgstr ""

#: inc/emails/instructor/class-lp-email-instructor-accepted.php:28
msgid "Accepted"
msgstr ""

#: inc/emails/instructor/class-lp-email-instructor-accepted.php:29
#: inc/emails/instructor/class-lp-email-instructor-accepted.php:32
msgid "Your email requesting to become an instructor has been accepted."
msgstr ""

#: inc/emails/instructor/class-lp-email-instructor-accepted.php:31
msgid "[{{site_title}}] Your request to become an instructor has been accepted"
msgstr ""

#: inc/emails/instructor/class-lp-email-instructor-denied.php:24
msgid "Denied"
msgstr ""

#: inc/emails/instructor/class-lp-email-instructor-denied.php:25
msgid "Your email requesting to become an instructor has been denied."
msgstr ""

#: inc/emails/instructor/class-lp-email-instructor-denied.php:27
msgid "[{{site_title}}] Your request to become an instructor has been denied"
msgstr ""

#: inc/emails/instructor/class-lp-email-instructor-denied.php:28
msgid "Your request to become an instructor has been denied"
msgstr ""

#: inc/emails/instructor/class-lp-email-new-order-instructor.php:22
msgid "Notify instructors when a user enrolls in one of their courses."
msgstr ""

#: inc/emails/student/class-lp-email-cancelled-order-user.php:25
#: inc/emails/student/class-lp-email-completed-order-user.php:29
#: inc/emails/student/class-lp-email-enrolled-course-user.php:27
#: inc/emails/student/class-lp-email-finished-course-user.php:26
#: inc/emails/student/class-lp-email-new-order-user.php:25
#: inc/emails/student/class-lp-email-processing-order-user.php:22
msgid "User"
msgstr ""

#: inc/emails/student/class-lp-email-cancelled-order-user.php:26
msgid "Send an email to the user when the order has been cancelled."
msgstr ""

#: inc/emails/student/class-lp-email-completed-order-user.php:30
msgid "Send an email to the user who has bought the course when the order is completed."
msgstr ""

#: inc/emails/student/class-lp-email-enrolled-course-user.php:28
msgid "Send this email to the user when they have enrolled in the course."
msgstr ""

#: inc/emails/student/class-lp-email-enrolled-course-user.php:30
msgid "[{{site_title}}] You have enrolled in the course"
msgstr ""

#: inc/emails/student/class-lp-email-enrolled-course-user.php:31
msgid "You have enrolled in the course"
msgstr ""

#: inc/emails/student/class-lp-email-finished-course-user.php:27
msgid "Send this email to the user when they have finished the course."
msgstr ""

#: inc/emails/student/class-lp-email-finished-course-user.php:29
msgid "[{{site_title}}] You have finished the course"
msgstr ""

#: inc/emails/student/class-lp-email-finished-course-user.php:30
msgid "You have finished the course"
msgstr ""

#: inc/emails/student/class-lp-email-new-order-user.php:26
msgid "Notify users when they successfully enroll in a course."
msgstr ""

#: inc/emails/student/class-lp-email-processing-order-user.php:23
msgid "Notify users when their course orders are being processed."
msgstr ""

#: inc/emails/types/class-lp-email-reset-password.php:19
msgid "Password Reset Email."
msgstr ""

#: inc/emails/types/class-lp-email-reset-password.php:21
msgid "[{{site_title}}] Reset Password"
msgstr ""

#: inc/ExternalPlugin/Elementor/LPElementor.php:60
msgid "LearnPress Instructor Sections"
msgstr ""

#: inc/ExternalPlugin/Elementor/LPElementor.php:64
msgid "LearnPress Course Sections"
msgstr ""

#: inc/ExternalPlugin/Elementor/LPElementorControls.php:393
msgid "Display"
msgstr ""

#: inc/ExternalPlugin/Elementor/LPElementorControls.php:449
msgid "Text Color"
msgstr ""

#: inc/ExternalPlugin/Elementor/LPElementorControls.php:454
msgid "Text Color Hover"
msgstr ""

#: inc/ExternalPlugin/Elementor/LPElementorControls.php:459
msgid "Background Color"
msgstr ""

#: inc/ExternalPlugin/Elementor/LPElementorControls.php:464
msgid "Background Color Hover"
msgstr ""

#: inc/ExternalPlugin/Elementor/LPElementorControls.php:536
msgid "Image"
msgstr ""

#: inc/ExternalPlugin/Elementor/Widgets/Course/CourseMaterialElementor.php:9
msgid "Course/Lesson Material"
msgstr ""

#: inc/ExternalPlugin/Elementor/Widgets/Course/FilterCourseElementor.php:22
msgid "Filter Course"
msgstr ""

#: inc/ExternalPlugin/Elementor/Widgets/Course/FilterCourseElementor.php:251
msgid "All Levels"
msgstr ""

#: inc/ExternalPlugin/Elementor/Widgets/Course/ListCoursesByPageElementor.php:19
msgid "List Courses by Page"
msgstr ""

#: inc/ExternalPlugin/Elementor/Widgets/Course/Sections/CoursePriceElementor.php:20
#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1411
msgid "Course Price"
msgstr ""

#: inc/ExternalPlugin/Elementor/Widgets/Course/Skins/SkinCoursesBase.php:222
#: inc/templates/class-lp-template-course.php:1016
msgid "View More"
msgstr ""

#: inc/ExternalPlugin/Elementor/Widgets/CourseListElementor.php:21
msgid "List Courses"
msgstr ""

#: inc/ExternalPlugin/Elementor/Widgets/Instructor/ListInstructorsElementor.php:20
msgid "List Instructors"
msgstr ""

#: inc/ExternalPlugin/Elementor/Widgets/Instructor/Sections/InstructorAvatarElementor.php:20
#: inc/ExternalPlugin/Elementor/Widgets/Instructor/Sections/InstructorAvatarElementor.php:44
msgid "Instructor Avatar"
msgstr ""

#: inc/ExternalPlugin/Elementor/Widgets/Instructor/Sections/InstructorButtonViewElementor.php:20
msgid "Instructor button view"
msgstr ""

#: inc/ExternalPlugin/Elementor/Widgets/Instructor/SingleInstructorBaseElementor.php:61
msgid "Instructor not found!"
msgstr ""

#: inc/ExternalPlugin/Elementor/Widgets/Instructor/SingleInstructorBaseElementor.php:66
msgid "User is not Instructor!"
msgstr ""

#: inc/ExternalPlugin/Elementor/Widgets/Instructor/SingleInstructorElementor.php:16
#: inc/Gutenberg/GutenbergHandleMain.php:397
msgid "Single Instructor"
msgstr ""

#: inc/ExternalPlugin/Elementor/Widgets/LoginUserFormElementor.php:16
msgid "Login Form"
msgstr ""

#: inc/ExternalPlugin/Elementor/Widgets/RegisterUserFormElementor.php:17
msgid "Register Form"
msgstr ""

#: inc/gateways/class-lp-gateway-abstract.php:227
msgid "%s has been deprecated. Please use % instead of."
msgstr ""

#: inc/gateways/offline-payment/class-lp-gateway-offline-payment.php:42
msgid "Offline Payment"
msgstr ""

#: inc/gateways/offline-payment/class-lp-gateway-offline-payment.php:43
msgid "Make a payment with cash."
msgstr ""

#: inc/gateways/offline-payment/class-lp-gateway-offline-payment.php:91
msgid "Payment can be made upon delivery."
msgstr ""

#: inc/gateways/paypal/class-lp-gateway-paypal.php:88
msgid "Make a payment via Paypal."
msgstr ""

#: inc/gateways/paypal/class-lp-gateway-paypal.php:92
msgid "Pay with PayPal"
msgstr ""

#: inc/gateways/paypal/class-lp-gateway-paypal.php:252
msgid "Paypal Client id is required."
msgstr ""

#: inc/gateways/paypal/class-lp-gateway-paypal.php:256
msgid "Paypal Client secret is required"
msgstr ""

#: inc/gateways/paypal/class-lp-gateway-paypal.php:346
#: inc/gateways/paypal/class-lp-gateway-paypal.php:408
msgid "Invalid Paypal access token"
msgstr ""

#: inc/gateways/paypal/class-lp-gateway-paypal.php:376
msgid "Invalid Paypal checkout"
msgstr ""

#: inc/gateways/paypal/class-lp-gateway-paypal.php:387
msgid "Invalid Paypal checkout url"
msgstr ""

#: inc/Gutenberg/Blocks/Courses/ListCoursesBlockType.php:136
#: inc/TemplateHooks/Course/ListCoursesTemplate.php:108
msgid "No courses found"
msgstr ""

#: inc/Gutenberg/Blocks/SingleCourseElements/CourseButtonReadMoreBlockType.php:96
msgid "Learn more about this course"
msgstr ""

#: inc/Gutenberg/Blocks/SingleCourseElements/CourseCapacityBlockType.php:80
#: inc/TemplateHooks/Course/SingleCourseOfflineTemplate.php:239
msgid "Capacity"
msgstr ""

#: inc/Gutenberg/Blocks/SingleCourseElements/CourseCategoriesBlockType.php:68
#: inc/TemplateHooks/Course/ListCoursesTemplate.php:280
#: inc/TemplateHooks/Course/SingleCourseModernLayout.php:98
msgid "in"
msgstr ""

#: inc/Gutenberg/Blocks/SingleCourseElements/CourseDeliveryBlockType.php:80
#: inc/TemplateHooks/Course/SingleCourseOfflineTemplate.php:235
msgid "Delivery type"
msgstr ""

#: inc/Gutenberg/Blocks/SingleCourseElements/CourseInstructorBlockType.php:61
#: inc/TemplateHooks/Course/ListCoursesTemplate.php:297
#: inc/TemplateHooks/Course/SingleCourseModernLayout.php:130
msgid "by"
msgstr ""

#: inc/Gutenberg/Blocks/SingleCourseItemElements/ItemCloseBlockType.php:62
#: templates/single-course/content-item/popup-header.php:57
msgid "Back to course"
msgstr ""

#: inc/Gutenberg/Blocks/SingleCourseItemElements/ItemSearchBlockType.php:66
#: inc/Gutenberg/Blocks/SingleCourseItemElements/ItemSearchBlockType.php:67
#: templates/single-course/content-item/popup-sidebar.php:22
msgctxt "search course input placeholder"
msgid "Search for course content"
msgstr ""

#: inc/Gutenberg/GutenbergHandleMain.php:304
msgid "LearnPress Legacy"
msgstr ""

#: inc/Gutenberg/GutenbergHandleMain.php:309
msgid "LearnPress Global"
msgstr ""

#: inc/Gutenberg/GutenbergHandleMain.php:314
msgid "LearnPress Course Elements"
msgstr ""

#: inc/Gutenberg/GutenbergHandleMain.php:376
msgid "Layout List Course"
msgstr ""

#: inc/Gutenberg/GutenbergHandleMain.php:377
msgid "List Course Learnpress"
msgstr ""

#: inc/Gutenberg/GutenbergHandleMain.php:386
msgid "Layout Grid Course"
msgstr ""

#: inc/Gutenberg/GutenbergHandleMain.php:387
msgid "List Course Learnpress - layout grid"
msgstr ""

#: inc/Gutenberg/GutenbergHandleMain.php:398
msgid "Single Instructor Learnpress"
msgstr ""

#: inc/handle-steps/class-lp-handle-steps.php:26
msgid "Invalid steps"
msgstr ""

#: inc/handle-steps/class-lp-handle-steps.php:31
msgid "Invalid step"
msgstr ""

#: inc/handle-steps/class-lp-handle-steps.php:78
msgid "Function not found"
msgstr ""

#: inc/Helpers/Template.php:139
msgid "Path file %s not exists"
msgstr ""

#: inc/jwt/includes/class-jwt-public.php:45
msgid "The username of the user."
msgstr ""

#: inc/jwt/includes/class-jwt-public.php:51
msgid "The password of the user."
msgstr ""

#: inc/jwt/includes/class-jwt-public.php:86
msgid "JSON Web Token"
msgstr ""

#: inc/jwt/includes/class-jwt-public.php:90
msgid "JSON Web Token."
msgstr ""

#: inc/jwt/includes/class-jwt-public.php:95
msgid "The ID of the user."
msgstr ""

#: inc/jwt/includes/class-jwt-public.php:100
msgid "The username of the user"
msgstr ""

#: inc/jwt/includes/class-jwt-public.php:105
msgid "The email address of the user."
msgstr ""

#: inc/jwt/includes/class-jwt-public.php:156
#: inc/jwt/includes/class-jwt-public.php:301
msgid "LearnPress JWT is not configurated properly, please contact the admin"
msgstr ""

#: inc/jwt/includes/class-jwt-public.php:272
msgid "Authorization header not found."
msgstr ""

#: inc/jwt/includes/class-jwt-public.php:288
msgid "Authentication token is missing."
msgstr ""

#: inc/jwt/includes/class-jwt-public.php:316
msgid "The iss do not match with this server"
msgstr ""

#: inc/jwt/includes/class-jwt-public.php:327
msgid "User ID not found in the token"
msgstr ""

#: inc/jwt/includes/class-jwt-public.php:337
msgid "The token must have an expiration date."
msgstr ""

#: inc/jwt/includes/class-jwt-public.php:347
msgid "The token has expired."
msgstr ""

#: inc/jwt/includes/class-jwt-public.php:362
msgid "Valid access token."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:45
#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:71
#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:96
#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:121
#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1290
#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:38
#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:64
#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:460
#: inc/jwt/rest-api/version1/class-lp-rest-questions-v1-controller.php:32
#: inc/jwt/rest-api/version1/class-lp-rest-questions-v1-controller.php:197
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:32
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:566
#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:213
#: inc/jwt/rest-api/version1/class-lp-rest-sections-v1-controller.php:116
#: inc/jwt/rest-api/version1/class-lp-rest-users-v1-controller.php:42
#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:86
#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:30
#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:74
#: inc/rest-api/v1/frontend/class-lp-rest-settings-controller.php:23
msgid "A unique identifier for the resource."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:154
msgid "Receipt data."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:265
msgid "Receipt data is empty."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:272
msgid "The secret key is empty."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:298
#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:324
msgid "Cannot verify the receipt"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:304
#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:315
#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:350
msgid "The course ID is invalid."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:366
#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:400
#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:527
msgid "Error: The course cannot be added to the cart."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:377
msgid "Verify Receipt Data successfully."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:556
#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:598
#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:818
#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:325
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:372
#: inc/TemplateHooks/Course/SingleCourseTemplate.php:617
#: templates/single-course/sidebar/user-time.php:42
msgid "Lifetime"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:627
msgid "Error: No Course ID available."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:634
msgid "Cannot finish this course."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:640
msgid "Error: Cannot finish this course."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:644
msgid "Congrats! You have completed the Course."
msgstr ""

#. translators: %s: post type
#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:679
#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:148
msgid "Cannot create existing %s."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1125
#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:160
msgid "%s days"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1126
#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:161
msgid "%s hours"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1127
#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:162
msgid "%s mins"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1128
#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:163
msgid "%s secs"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1296
#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:466
#: inc/jwt/rest-api/version1/class-lp-rest-questions-v1-controller.php:203
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:572
msgid "Course name."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1301
#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:471
#: inc/jwt/rest-api/version1/class-lp-rest-questions-v1-controller.php:208
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:577
msgid "Course slug."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1306
#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:476
#: inc/jwt/rest-api/version1/class-lp-rest-questions-v1-controller.php:213
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:582
msgid "Course URL."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1313
msgid "Course Image URL."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1319
#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:483
#: inc/jwt/rest-api/version1/class-lp-rest-questions-v1-controller.php:220
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:589
msgid "The date the Course was created, in the site's timezone."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1326
#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:489
#: inc/jwt/rest-api/version1/class-lp-rest-questions-v1-controller.php:226
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:595
msgid "The date the Course was created, as GMT."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1333
#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:495
#: inc/jwt/rest-api/version1/class-lp-rest-questions-v1-controller.php:232
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:601
msgid "The date the Course was last modified, in the site's timezone."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1340
#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:501
#: inc/jwt/rest-api/version1/class-lp-rest-questions-v1-controller.php:238
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:607
msgid "The date the Course was last modified, as GMT."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1347
msgid "Display courses if they are on sale."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1353
#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:507
#: inc/jwt/rest-api/version1/class-lp-rest-questions-v1-controller.php:244
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:613
msgid "Course status (post status)."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1360
#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:514
#: inc/jwt/rest-api/version1/class-lp-rest-questions-v1-controller.php:251
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:620
msgid "Course content."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1365
#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:519
#: inc/jwt/rest-api/version1/class-lp-rest-questions-v1-controller.php:256
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:625
msgid "Retrieves the course excerpt.."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1375
msgid "Count the number of enrolled students."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1381
#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:524
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:630
msgid "Can finish the course"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1387
msgid "Can retake the course"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1393
msgid "Total retakes"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1399
msgid "Retaken"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1405
msgid "Course Review add-on"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1417
msgid "Course Price Rendered"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1423
msgid "Course Origin Price"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1429
msgid "Course Origin Price Rendered"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1435
msgid "Course Sale Price"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1441
msgid "Course Sale Price Rendered"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1447
msgid "List of categories."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1454
msgid "Category ID."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1459
msgid "Category name."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1465
msgid "Category slug."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1474
msgid "List of tags."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1481
msgid "Tag ID."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1486
msgid "Tag name."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1492
#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1519
msgid "Tag slug."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1501
#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1534
msgid "Retrieves the course sections and items.."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1508
msgid "User ID."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1513
msgid "Display name."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1525
msgid "Social Infor."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1541
#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:14
msgid "Section ID."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1546
#: inc/jwt/rest-api/version1/class-lp-rest-sections-v1-controller.php:122
msgid "Section name."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1551
#: inc/jwt/rest-api/version1/class-lp-rest-sections-v1-controller.php:14
msgid "Course ID."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1556
#: inc/jwt/rest-api/version1/class-lp-rest-sections-v1-controller.php:127
msgid "Section description."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1561
msgid "Section items."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1566
#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1581
#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:540
#: inc/jwt/rest-api/version1/class-lp-rest-questions-v1-controller.php:266
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:646
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:682
msgid "Item ID."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1571
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:687
msgid "Item Type."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1576
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:692
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:721
msgid "Item title."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1586
msgid "Percent."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1591
msgid "Duration."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1596
msgid "Graduation."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1601
#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:572
msgid "Status."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1606
msgid "Locked."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1616
msgid "List of course user data."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1623
msgid "Graduation"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1634
msgid "Start time"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1640
#: templates/profile/tabs/courses/course-list.php:34
msgid "End time"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-courses-v1-controller.php:1646
#: templates/profile/tabs/courses/course-list.php:33
msgid "Expiration time"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:205
msgid "Error: No lesson available!."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:210
msgid "Error: Lesson is invalid!"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:215
msgid "Error: This lesson is not assigned in the Course."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:222
#: inc/user/class-lp-user.php:435
msgid "Error: No Course or User available."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:239
msgid "Congrats! You have completed the lesson successfully"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:535
#: inc/jwt/rest-api/version1/class-lp-rest-questions-v1-controller.php:261
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:641
msgid "Assigned."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:545
#: inc/jwt/rest-api/version1/class-lp-rest-questions-v1-controller.php:271
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:651
msgid "Title."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:550
#: inc/jwt/rest-api/version1/class-lp-rest-questions-v1-controller.php:276
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:656
#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:224
msgid "Item slug."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:555
#: inc/jwt/rest-api/version1/class-lp-rest-questions-v1-controller.php:281
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:661
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:697
msgid "Item Content."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:560
#: inc/jwt/rest-api/version1/class-lp-rest-questions-v1-controller.php:286
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:666
msgid "Item Author."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:567
msgid "Retrieves the Lesson result.."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:579
msgid "Video intro."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-lessons-v1-controller.php:602
#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:65
msgid "Invalid ID."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:14
#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:475
msgid "The method '%s' not implemented. It must be overridden in the subclass."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:19
msgid "Sorry, you cannot get lists"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:33
msgid "Sorry, you are not allowed to create resources."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:41
msgid "Sorry, You cannot view this item."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:47
msgid "Sorry, you cannot view this resource."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:506
#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:315
#: inc/jwt/rest-api/version1/class-lp-rest-sections-v1-controller.php:143
msgid "The current page of the collection."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:514
#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:323
#: inc/jwt/rest-api/version1/class-lp-rest-sections-v1-controller.php:151
msgid "The maximum number of items to be returned in the result set."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:523
#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:339
#: inc/jwt/rest-api/version1/class-lp-rest-sections-v1-controller.php:167
msgid "Limit results to those matching a string."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:529
msgid "Limit response to resources published after a given ISO8601 compliant date."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:535
msgid "Limit response to resources published before a given ISO8601 compliant date."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:541
#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:345
#: inc/jwt/rest-api/version1/class-lp-rest-sections-v1-controller.php:173
msgid "Ensure the result set excludes specific IDs."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:550
#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:354
#: inc/jwt/rest-api/version1/class-lp-rest-sections-v1-controller.php:182
msgid "Limit the result set to specific IDs."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:559
msgid "Offset the result set by a specific number of items."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:565
#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:332
#: inc/jwt/rest-api/version1/class-lp-rest-sections-v1-controller.php:160
msgid "Sorting attributes in ascending or descending order."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:572
msgid "Sort collections by the object attribute."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:586
#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:602
msgid "Limit the result set to posts assigned to specific authors."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:594
msgid "Ensure the result set excludes posts assigned to specific authors."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:610
msgid "Get item learned by user."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:618
msgid "Limit the result set to those of particular parent IDs."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-posts-controller.php:627
msgid "Limit the result set to all items except those of a particular parent ID."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:58
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:76
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:98
msgid "Quiz ID."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:80
msgid "Question ID."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:102
msgid "Answer all questions."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:231
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:257
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:285
msgid "There is no Quiz ID or Quiz assigned in the course."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:292
msgid "No Answer param."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:673
msgid "List all of the Quiz Questions."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:677
#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:716
msgid "Question items."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:702
msgid "Point."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:707
msgid "Question Hint."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:712
msgid "Question Options."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:726
msgid "Item value."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:731
msgid "Item id."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-quiz-v1-controller.php:742
msgid "Retrieves the quiz result.."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:31
msgid "Sorry, Invalid Section ID param."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:37
msgid "Please assign a section to the Course."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:44
#: inc/jwt/rest-api/version1/class-lp-rest-sections-v1-controller.php:40
#: inc/Models/CourseModel.php:962
#: inc/user/class-lp-user.php:291
msgid "The course is not public"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:219
msgid "Item name."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:229
msgid "Item URL."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:236
msgid "Item status (post status)."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:243
msgid "Item type."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:250
msgid "Item preview."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:256
msgid "Item duration."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:262
msgid "Item graduation."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:268
msgid "Item status."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:274
msgid "Item locked."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:280
msgid "Count questions."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-section-items-v1-controller.php:286
msgid "Item content."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-sections-v1-controller.php:31
msgid "Sorry, Invalid course ID param."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-users-v1-controller.php:77
msgid "User or Email login."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-users-v1-controller.php:94
msgid "Old Password"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-users-v1-controller.php:99
msgid "New Password"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-users-v1-controller.php:116
msgid "User ID"
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-users-v1-controller.php:299
msgid "A link to reset your password has been emailed to you."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-users-v1-controller.php:314
msgid "Invalid user ID."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-users-v1-controller.php:322
msgid "Invalid password."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-users-v1-controller.php:339
msgid "Your account has been deleted."
msgstr ""

#: inc/jwt/rest-api/version1/class-lp-rest-users-v1-controller.php:380
msgid "Your password has been updated. Please log in again to continue!"
msgstr ""

#: inc/lp-core-functions.php:723
msgid "Minute(s)"
msgstr ""

#: inc/lp-core-functions.php:724
msgid "Hour(s)"
msgstr ""

#: inc/lp-core-functions.php:725
msgid "Day(s)"
msgstr ""

#: inc/lp-core-functions.php:726
msgid "Week(s)"
msgstr ""

#: inc/lp-core-functions.php:866
msgid "Left with space"
msgstr ""

#: inc/lp-core-functions.php:867
msgid "Right with space"
msgstr ""

#: inc/lp-core-functions.php:1069
#: inc/lp-template-functions.php:1497
msgid "%s week"
msgid_plural "%s weeks"
msgstr[0] ""
msgstr[1] ""

#: inc/lp-core-functions.php:1073
#: inc/lp-template-functions.php:1493
msgid "%s day"
msgid_plural "%s days"
msgstr[0] ""
msgstr[1] ""

#: inc/lp-core-functions.php:1078
#: inc/lp-template-functions.php:1488
msgid "%s hour"
msgid_plural "%s hours"
msgstr[0] ""
msgstr[1] ""

#: inc/lp-core-functions.php:1082
#: inc/lp-template-functions.php:1482
msgid "%s minute"
msgid_plural "%s minutes"
msgstr[0] ""
msgstr[1] ""

#: inc/lp-core-functions.php:1812
msgid "Cart"
msgstr ""

#: inc/lp-core-functions.php:1816
msgid "Enable cart"
msgstr ""

#: inc/lp-core-functions.php:1817
msgid "Check this option to enable users to purchase multiple courses at one time."
msgstr ""

#: inc/lp-core-functions.php:1826
msgid "Add to cart redirect"
msgstr ""

#: inc/lp-core-functions.php:1827
msgid "Redirect to checkout immediately after adding the course to the cart."
msgstr ""

#: inc/lp-core-functions.php:1833
msgid "AJAX add to cart"
msgstr ""

#: inc/lp-core-functions.php:1834
msgid "Using AJAX to add the course to the cart."
msgstr ""

#: inc/lp-core-functions.php:1840
msgid "Cart page"
msgstr ""

#: inc/lp-core-functions.php:2600
msgid "Loading..."
msgstr ""

#: inc/lp-core-functions.php:2601
msgid "Get A Passing Grade"
msgstr ""

#: inc/lp-core-functions.php:2607
msgid "Evaluate by the number of completed lessons per the total number of lessons."
msgstr ""

#: inc/lp-core-functions.php:2608
msgid "E.g: If a course has 10 lessons and a user completes 5 lessons, then the result is 5/10 (50%)."
msgstr ""

#: inc/lp-core-functions.php:2610
msgid "Evaluate by the result of the final quiz in the course. You have to add a quiz at the end of the course."
msgstr ""

#: inc/lp-core-functions.php:2616
msgid "Evaluate by the number of passed quizzes per the total number of quizzes."
msgstr ""

#: inc/lp-core-functions.php:2617
msgid "E.g: If the course has 10 quizzes and the user passes 5 quizzes, then the result is 5/10 (50%)."
msgstr ""

#: inc/lp-core-functions.php:2624
msgid "Evaluate by the number of correct answers per the total number of questions."
msgstr ""

#: inc/lp-core-functions.php:2625
msgid "E.g: If the course has 10 questions and the user corrects 5 questions, then the result is 5/10 (50%)."
msgstr ""

#: inc/lp-core-functions.php:2630
msgid "Evaluate by the number of achieved scores per the total score of the questions."
msgstr ""

#: inc/lp-core-functions.php:2638
msgid "Evaluate via lessons"
msgstr ""

#: inc/lp-core-functions.php:2643
msgid "Evaluate via results of the final quiz"
msgstr ""

#: inc/lp-core-functions.php:2650
msgid "Evaluate via passed quizzes"
msgstr ""

#: inc/lp-core-functions.php:2655
msgid "Evaluate via questions"
msgstr ""

#: inc/lp-core-functions.php:2660
msgid "Evaluate via mark"
msgstr ""

#: inc/lp-core-functions.php:2806
msgid "Heads up! Please backup before upgrading!"
msgstr ""

#: inc/lp-core-functions.php:2809
msgid "The latest update includes some substantial changes across different areas of the plugin. We highly recommend you backup your site before upgrading, and make sure you first update in a staging environment"
msgstr ""

#: inc/lp-deprecated.php:859
#: inc/templates/class-lp-template-general.php:49
msgctxt "breadcrumb"
msgid "Home"
msgstr ""

#: inc/lp-template-functions.php:1097
#: inc/lp-template-functions.php:1182
#: inc/templates/class-lp-template-course.php:134
msgid "Final"
msgstr ""

#: inc/lp-template-functions.php:1371
msgid "Course Sidebar"
msgstr ""

#: inc/lp-template-functions.php:1373
msgid "Widgets in this area will be shown in a single course"
msgstr ""

#: inc/lp-template-functions.php:1382
msgid "All Courses"
msgstr ""

#: inc/lp-template-functions.php:1384
msgid "Widgets in this area will be shown on all course pages"
msgstr ""

#: inc/lp-template-functions.php:1521
#: inc/lp-template-functions.php:1530
msgid "All levels"
msgstr ""

#: inc/lp-template-functions.php:1531
msgid "Beginner"
msgstr ""

#: inc/lp-template-functions.php:1532
msgid "Intermediate"
msgstr ""

#: inc/lp-template-functions.php:1533
msgid "Expert"
msgstr ""

#: inc/lp-webhooks.php:85
msgid "LearnPress webhook %s process completed"
msgstr ""

#: inc/lp-webhooks.php:91
msgid "The LearnPress webhook process is complete"
msgstr ""

#: inc/Models/CourseModel.php:972
#: inc/user/class-lp-user.php:314
msgid "Course can retake."
msgstr ""

#: inc/Models/CourseModel.php:979
#: inc/user/class-lp-user.php:302
msgid "The course is full of students."
msgstr ""

#: inc/Models/CourseModel.php:986
msgid "This course is already enrolled!"
msgstr ""

#: inc/Models/CourseModel.php:989
msgid "The course is finished."
msgstr ""

#: inc/Models/CourseModel.php:997
#: inc/Models/CourseModel.php:1095
#: inc/user/class-lp-user.php:320
msgid "Enrollment in the course is not mandatory. You can access course for learning now."
msgstr ""

#: inc/Models/CourseModel.php:1007
#: inc/user/class-lp-user.php:309
msgid "The course is external"
msgstr ""

#: inc/Models/CourseModel.php:1013
#: inc/Models/CourseModel.php:1016
#: inc/user/class-lp-user.php:326
msgid "The course is not purchased."
msgstr ""

#: inc/Models/CourseModel.php:1088
msgid "The course is free."
msgstr ""

#: inc/Models/CourseModel.php:1104
#: inc/user/class-lp-user.php:390
msgid "Course is purchased"
msgstr ""

#: inc/Models/CourseModel.php:1115
#: inc/user/class-lp-user.php:403
msgid "Course is enrolled or finished"
msgstr ""

#: inc/Models/CourseSectionModel.php:215
msgid "Item type invalid"
msgstr ""

#: inc/Models/CourseSectionModel.php:290
msgid "Item already exists in this course"
msgstr ""

#: inc/Models/PostModel.php:230
msgid "You do not have permission to create item."
msgstr ""

#: inc/Models/PostModel.php:253
msgid "You do not have permission to edit this item."
msgstr ""

#: inc/Models/QuestionPostModel.php:90
#: inc/question/class-lp-question.php:356
msgid "True Or False"
msgstr ""

#: inc/Models/QuestionPostModel.php:91
#: inc/question/class-lp-question.php:357
msgid "Multi Choice"
msgstr ""

#: inc/Models/QuestionPostModel.php:92
#: inc/question/class-lp-question.php:358
msgid "Single Choice"
msgstr ""

#: inc/Models/QuestionPostModel.php:93
#: inc/question/class-lp-question.php:359
msgid "Fill In Blanks"
msgstr ""

#: inc/Models/UserItems/UserCourseModel.php:904
msgid "Course not exists!"
msgstr ""

#: inc/Models/UserItems/UserCourseModel.php:921
msgid "You must complete all items in course"
msgstr ""

#: inc/Models/UserItems/UserCourseModel.php:924
msgid "You must passed course"
msgstr ""

#: inc/Models/UserItems/UserCourseModel.php:970
msgid "You can not retake this course!"
msgstr ""

#: inc/Models/UserItems/UserCourseModel.php:1016
msgid "You have not enroll this course!"
msgstr ""

#: inc/Models/UserItems/UserCourseModel.php:1020
msgid "You have finished this course!"
msgstr ""

#: inc/Models/UserItems/UserCourseModel.php:1024
msgid "Course was blocked by expire!"
msgstr ""

#: inc/Models/UserItems/UserItemModel.php:519
#: inc/Models/UserItems/UserQuizModel.php:626
#: inc/order/class-lp-order.php:280
#: inc/user-item/class-lp-user-item-quiz.php:146
#: inc/user-item/class-lp-user-item.php:689
#: templates/content-lesson/button-complete.php:45
msgid "Completed"
msgstr ""

#: inc/Models/UserItems/UserItemModel.php:525
#: inc/user-item/class-lp-user-item.php:687
msgid "Enrolled"
msgstr ""

#: inc/Models/UserItems/UserLessonModel.php:79
msgid "Lesson is already completed."
msgstr ""

#: inc/Models/UserItems/UserLessonModel.php:84
msgid "You have not started course"
msgstr ""

#: inc/Models/UserItems/UserQuizModel.php:228
#: inc/Models/UserItems/UserQuizModel.php:300
msgid "User is invalid."
msgstr ""

#: inc/Models/UserItems/UserQuizModel.php:233
#: inc/Models/UserItems/UserQuizModel.php:305
msgid "Quiz is invalid."
msgstr ""

#: inc/Models/UserItems/UserQuizModel.php:238
#: inc/Models/UserItems/UserQuizModel.php:310
msgid "Course is invalid."
msgstr ""

#: inc/Models/UserItems/UserQuizModel.php:250
msgid "You have already started the quiz."
msgstr ""

#: inc/Models/UserItems/UserQuizModel.php:257
#: inc/Models/UserItems/UserQuizModel.php:319
#: inc/user/class-lp-user.php:522
msgid "Please enroll in the course before starting the quiz."
msgstr ""

#: inc/Models/UserItems/UserQuizModel.php:259
#: inc/Models/UserItems/UserQuizModel.php:324
msgid "You have already finished the course of this quiz."
msgstr ""

#: inc/Models/UserItems/UserQuizModel.php:330
msgid "You have not completed the quiz."
msgstr ""

#: inc/Models/UserItems/UserQuizModel.php:336
msgid "You have exceeded the number of retakes."
msgstr ""

#: inc/order/class-lp-order.php:189
#: inc/order/class-lp-order.php:199
#: templates/checkout/order-received.php:38
msgid "Thank you. Your order has been received."
msgstr ""

#: inc/order/class-lp-order.php:289
msgid "Cancelled"
msgstr ""

#: inc/order/class-lp-order.php:295
msgid "Trash"
msgstr ""

#: inc/order/class-lp-order.php:298
msgid "On hold"
msgstr ""

#: inc/order/class-lp-order.php:301
msgid "Refunded"
msgstr ""

#: inc/order/class-lp-order.php:458
msgid "%s (Guest)"
msgstr ""

#: inc/order/class-lp-order.php:1034
msgctxt "full name"
msgid "%1$s"
msgstr ""

#: inc/order/class-lp-order.php:1114
msgid "Order on"
msgstr ""

#: inc/order/lp-order-functions.php:285
msgctxt "Order status"
msgid "Completed"
msgstr ""

#: inc/order/lp-order-functions.php:290
msgid "Completed <span class=\"count\">(%s)</span>"
msgid_plural "Completed <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: inc/order/lp-order-functions.php:293
msgctxt "Order status"
msgid "Pending"
msgstr ""

#: inc/order/lp-order-functions.php:298
msgid "Pending Payment <span class=\"count\">(%s)</span>"
msgid_plural "Pending Payment <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: inc/order/lp-order-functions.php:301
msgctxt "Order status"
msgid "Processing"
msgstr ""

#: inc/order/lp-order-functions.php:306
msgid "Processing <span class=\"count\">(%s)</span>"
msgid_plural "Processing <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: inc/order/lp-order-functions.php:309
msgctxt "Order status"
msgid "Cancelled"
msgstr ""

#: inc/order/lp-order-functions.php:314
msgid "Cancelled <span class=\"count\">(%s)</span>"
msgid_plural "Cancelled <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: inc/order/lp-order-functions.php:317
msgctxt "Order status"
msgid "Failed"
msgstr ""

#: inc/order/lp-order-functions.php:322
msgid "Failed <span class=\"count\">(%s)</span>"
msgid_plural "Failed <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: inc/order/lp-order-functions.php:325
msgctxt "Order status"
msgid "Trash"
msgstr ""

#: inc/order/lp-order-functions.php:330
msgid "Trash <span class=\"count\">(%s)</span>"
msgid_plural "Trash <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: inc/order/lp-order-functions.php:338
msgid "Order received in case a user purchases a course but doesn't finalize the order."
msgstr ""

#: inc/order/lp-order-functions.php:339
msgid "Payment received and the order is awaiting fulfillment."
msgstr ""

#: inc/order/lp-order-functions.php:340
msgid "The order is fulfilled and completed."
msgstr ""

#: inc/order/lp-order-functions.php:341
msgid "The order is cancelled by an admin or the customer."
msgstr ""

#: inc/order/lp-order-functions.php:391
msgid "Order number <strong>%s</strong> not found"
msgstr ""

#: inc/order/lp-order-functions.php:396
msgid "You do not have permission to cancel this order."
msgstr ""

#: inc/order/lp-order-functions.php:401
msgid "The order is cancelled by the customer"
msgstr ""

#: inc/order/lp-order-functions.php:404
msgid "Order number <strong>%s</strong> has been cancelled"
msgstr ""

#: inc/order/lp-order-functions.php:407
msgid "The order number <strong>%s</strong> can not be cancelled."
msgstr ""

#: inc/question/class-lp-question-true-or-false.php:49
msgid "True"
msgstr ""

#: inc/question/class-lp-question-true-or-false.php:55
msgid "False"
msgstr ""

#: inc/question/class-lp-question.php:605
msgid "First option"
msgstr ""

#: inc/question/class-lp-question.php:611
msgid "Second option"
msgstr ""

#: inc/question/class-lp-question.php:617
msgid "Third option"
msgstr ""

#: inc/quiz/class-lp-quiz.php:586
msgid "Finish quiz"
msgstr ""

#: inc/quiz/class-lp-quiz.php:587
msgid "Are you sure you want to finish this quiz?"
msgstr ""

#: inc/quiz/class-lp-quiz.php:590
msgid "Retake quiz"
msgstr ""

#: inc/quiz/class-lp-quiz.php:591
msgid "Are you sure you want to retake this quiz?"
msgstr ""

#: inc/quiz/class-lp-quiz.php:594
msgid "Time's up!"
msgstr ""

#: inc/quiz/class-lp-quiz.php:595
msgid "The time is up! Your quiz will automatically come to an end"
msgstr ""

#: inc/quiz/class-lp-quiz.php:597
msgid "Congrats! You have finished this quiz"
msgstr ""

#: inc/quiz/class-lp-quiz.php:598
msgid "Congrats! You have re-taken this quiz. Please wait a moment and the page will reload"
msgstr ""

#: inc/rest-api/v1/admin/class-lp-admin-rest-course-controller.php:50
msgid "No Course ID available!"
msgstr ""

#: inc/rest-api/v1/admin/class-lp-admin-rest-course-controller.php:56
#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:418
msgid "No Course available!"
msgstr ""

#: inc/rest-api/v1/admin/class-lp-admin-rest-course-controller.php:88
msgid "No Quiz in this course!"
msgstr ""

#: inc/rest-api/v1/admin/class-lp-admin-rest-database-controller.php:118
msgid "The LP Database is Latest:"
msgstr ""

#: inc/rest-api/v1/admin/class-lp-admin-rest-reset-data-controller.php:91
msgid "No items available!"
msgstr ""

#: inc/rest-api/v1/admin/class-lp-admin-rest-reset-data-controller.php:105
msgid "No courses available!"
msgstr ""

#: inc/rest-api/v1/admin/class-lp-admin-rest-reset-data-controller.php:132
msgid "No course ID available"
msgstr ""

#: inc/rest-api/v1/admin/class-lp-admin-rest-statistics-controller.php:78
msgid "Net sales"
msgstr ""

#: inc/rest-api/v1/admin/class-lp-admin-rest-statistics-controller.php:116
msgid "Completed orders"
msgstr ""

#: inc/rest-api/v1/admin/class-lp-admin-rest-statistics-controller.php:188
msgid "User registed"
msgstr ""

#: inc/rest-api/v1/admin/class-lp-admin-rest-statistics-controller.php:220
#: inc/rest-api/v1/admin/class-lp-admin-rest-statistics-controller.php:245
msgid "Hour"
msgstr ""

#: inc/rest-api/v1/admin/class-lp-admin-rest-statistics-controller.php:223
#: inc/rest-api/v1/admin/class-lp-admin-rest-statistics-controller.php:226
#: inc/rest-api/v1/admin/class-lp-admin-rest-statistics-controller.php:248
msgid "Dates"
msgstr ""

#: inc/rest-api/v1/admin/class-lp-admin-rest-statistics-controller.php:229
#: inc/rest-api/v1/admin/class-lp-admin-rest-statistics-controller.php:232
#: inc/rest-api/v1/admin/class-lp-admin-rest-statistics-controller.php:252
#: inc/rest-api/v1/admin/class-lp-admin-rest-statistics-controller.php:258
msgid "Months"
msgstr ""

#: inc/rest-api/v1/admin/class-lp-admin-rest-statistics-controller.php:262
msgid "Quarters"
msgstr ""

#: inc/rest-api/v1/admin/class-lp-admin-rest-statistics-controller.php:265
msgid "Years"
msgstr ""

#: inc/rest-api/v1/admin/class-lp-admin-rest-tools-controller.php:264
msgid "Dismissed!"
msgstr ""

#: inc/rest-api/v1/admin/class-lp-admin-rest-tools-controller.php:592
msgid "Assign users to courses successfully."
msgstr ""

#: inc/rest-api/v1/admin/class-lp-admin-rest-tools-controller.php:647
msgid "Unassigned users from courses successfully."
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-addon-controller.php:121
msgid "Get addons successfully"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-addon-controller.php:147
msgid "Action is invalid!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-addon-controller.php:152
#: inc/rest-api/v1/frontend/class-lp-rest-lazy-load-controller.php:124
msgid "Params is invalid!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-addon-controller.php:203
msgid "successfully"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:290
#: inc/TemplateHooks/Course/ListCoursesTemplate.php:469
msgid "Showing only one result"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:293
msgid "Showing last course of %s results"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:308
#: inc/TemplateHooks/Course/ListCoursesTemplate.php:476
msgid "Showing %1$s of %2$s results"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:371
msgid "Invalid course!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:413
msgid "Congrats! You have enrolled in the course successfully. Redirecting..."
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:429
#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:543
msgid "Error: Please set up a page for checkout."
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:433
msgid "Redirecting..."
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:464
msgid "Error: Invalid Course ID."
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:469
msgid "Error: No Course available."
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:510
msgid "Repurchase Options"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:549
msgid "\"%s\" has been added to your cart. Redirecting..."
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:574
msgid "Invalid params"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:579
#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:692
#: inc/user/abstract-lp-user.php:766
msgid "Invalid course"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:584
msgid "Invalid user"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:589
#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:701
msgid "Invalid user course"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-courses-controller.php:601
msgid "Now you can begin this course"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-instructor-controller.php:80
msgid "No instructor found!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-lazy-load-controller.php:74
#: inc/rest-api/v1/frontend/class-lp-rest-lazy-load-controller.php:129
#: inc/rest-api/v1/frontend/class-lp-rest-lazy-load-controller.php:187
#: inc/rest-api/v1/frontend/class-lp-rest-lazy-load-controller.php:192
#: inc/rest-api/v1/frontend/class-lp-rest-users-controller.php:163
#: inc/Widgets/course-progress.php:65
msgid "The course is invalid!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-lazy-load-controller.php:78
msgid "The course is not required to enroll!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-lazy-load-controller.php:134
msgid "You are a Guest"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-lazy-load-controller.php:138
msgid "You are a not permission!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-lazy-load-controller.php:143
msgid "You are a not enroll course"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-lazy-load-controller.php:270
msgid "The section is invalid!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-lazy-load-controller.php:291
msgid "Item not assign to course"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:41
msgid "Data of material"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:47
msgid "File."
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:63
msgid "Material orders"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:116
msgid "Invalid course or lesson"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:120
msgid "Invalid materials"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:138
msgid "Material feature is not allowed to upload"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:141
msgid "Your uploaded files reach the maximum amount!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:156
msgid "File \"%s\" title is not empty!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:161
msgid "File %s method is invalid!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:168
msgid "File %s size is too large!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:178
msgid "File %s type is invalid!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:192
msgid "File %s: "
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:209
msgid "An error occurred while checking %1$s. %2$s"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:235
msgid "Cannot save file %s"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:239
msgid "Other files is upload successfully."
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:255
msgid "Files upload successfully."
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:282
msgid "Invalid item id!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:293
#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:502
msgid "Successfully"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:295
#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:504
msgid "Empty material!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:382
msgid "Updated."
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:408
msgid "Invalid file identifier"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:415
msgid "File is deleted."
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-material-controller.php:418
msgid "There is an error when delete this file."
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:131
msgid "User not found"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:135
msgid "File not found"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:147
#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:203
#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:556
msgid "The upload directory is not writable"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:169
#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:569
msgid "Cannot write the file"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:176
msgid "Avatar updated"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:191
#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:483
msgid "The user is invalid"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:215
msgid "The profile picture has been removed successfully"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:239
#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:308
#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:385
#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:391
msgid "No user ID found!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:244
#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:313
msgid "The user does not exist!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:252
#: templates/profile/tabs/courses/general-statistic.php:26
msgid "Total enrolled courses"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:257
msgid "Total course is in progress"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:258
msgid "Inprogress Course"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:262
msgid "Total courses finished"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:267
msgid "Total courses passed"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:268
msgid "Passed Course"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:272
msgid "Total courses failed"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:273
msgid "Failed Course"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:327
#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:328
msgid "Total Course"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:332
#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:333
msgid "Published Course"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:337
#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:338
msgid "Pending Course"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:342
#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:343
msgid "Total Student"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:347
#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:348
msgid "Student Completed"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:352
#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:353
msgid "Student In-progress"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:395
#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:398
msgid "Request invalid!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:429
msgid "No Course IDs available!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:435
msgid "No User available!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:518
#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:523
msgid "User is invalid!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:530
msgid "Cover image has been removed successfully"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:536
msgid "File is invalid!"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:546
msgid "File type is not allowed"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-profile-controller.php:579
msgid "Cover image is updated"
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-users-controller.php:96
msgid "The ID of the course item object."
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-users-controller.php:102
msgid "The ID of the course object."
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-users-controller.php:123
msgid "%s was not registered as a request argument."
msgstr ""

#: inc/rest-api/v1/frontend/class-lp-rest-users-controller.php:167
#: inc/rest-api/v1/frontend/class-lp-rest-users-controller.php:293
msgid "The quiz is invalid!"
msgstr ""

#: inc/settings/class-lp-settings-courses.php:32
msgctxt "slug"
msgid "courses"
msgstr ""

#: inc/settings/class-lp-settings-courses.php:46
msgctxt "default-slug"
msgid "courses"
msgstr ""

#: inc/Shortcodes/class-lp-shortcode-become-a-teacher.php:75
msgid "Please %s to send your request!"
msgstr ""

#: inc/Shortcodes/class-lp-shortcode-become-a-teacher.php:75
msgctxt "become-teacher-form"
msgid "login"
msgstr ""

#: inc/Shortcodes/class-lp-shortcode-become-a-teacher.php:78
msgid "You have already sent the request. Please wait for the approval."
msgstr ""

#: inc/Shortcodes/class-lp-shortcode-become-a-teacher.php:80
msgid "You are a teacher!"
msgstr ""

#: inc/Shortcodes/class-lp-shortcode-become-a-teacher.php:87
msgid "Become a Teacher"
msgstr ""

#: inc/Shortcodes/class-lp-shortcode-button-course.php:75
msgid "Learn now"
msgstr ""

#: inc/Shortcodes/class-lp-shortcode-course-curriculum.php:46
msgid "Invalid course."
msgstr ""

#: inc/Shortcodes/class-lp-shortcode-login-form.php:36
#: inc/Shortcodes/class-lp-shortcode-register-form.php:38
msgid "Your are logged in as %1$s. <a href=\"%2$s\">Log out</a>?"
msgstr ""

#: inc/Shortcodes/class-lp-shortcode-profile.php:59
msgid "You can't view the user profile"
msgstr ""

#: inc/Shortcodes/class-lp-shortcode-profile.php:83
msgid "This shortcode LP Profile only use on the page <a href=\"%s\">%s</a>"
msgstr ""

#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:123
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:136
#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:227
msgid "<span class=\"count\">%1$s</span> %2$s"
msgstr ""

#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:128
msgid "Sections"
msgstr ""

#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:201
msgid "Drag to reorder section"
msgstr ""

#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:207
msgid "Edit section title"
msgstr ""

#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:212
msgid "This section will be deleted. The items in this section will no longer be assigned to this course, but will not be permanently deleted."
msgstr ""

#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:213
msgid "Delete Section"
msgstr ""

#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:279
msgid "Update section title"
msgstr ""

#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:300
msgid "+ Add Description"
msgstr ""

#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:325
msgid "Add Sections"
msgstr ""

#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:362
msgid "Edit item detail"
msgstr ""

#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:369
msgid "This item will be removed from this section. This item will no longer be assigned to this course. It will not be permanently deleted from the system."
msgstr ""

#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:370
msgid "Remove item"
msgstr ""

#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:385
msgid "Drag to reorder item"
msgstr ""

#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:424
msgid "Create a new %s"
msgstr ""

#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:426
msgid "New %s"
msgstr ""

#: inc/TemplateHooks/Course/AdminEditCurriculumTemplate.php:638
msgid "No items found"
msgstr ""

#: inc/TemplateHooks/Course/CourseMaterialTemplate.php:57
msgid "Load more."
msgstr ""

#: inc/TemplateHooks/Course/FilterCourseTemplate.php:173
msgid "Search Course"
msgstr ""

#: inc/TemplateHooks/Course/FilterCourseTemplate.php:708
msgid "Online"
msgstr ""

#: inc/TemplateHooks/Course/FilterCourseTemplate.php:709
msgid "Offline"
msgstr ""

#: inc/TemplateHooks/Course/FilterCourseTemplate.php:771
msgid "Done"
msgstr ""

#: inc/TemplateHooks/Course/ListCoursesRelatedTemplate.php:140
msgid "You might be interested in"
msgstr ""

#: inc/TemplateHooks/Course/ListCoursesTemplate.php:537
msgid "Newly published"
msgstr ""

#: inc/TemplateHooks/Course/ListCoursesTemplate.php:540
msgid "Price high to low"
msgstr ""

#: inc/TemplateHooks/Course/ListCoursesTemplate.php:541
msgid "Price low to high"
msgstr ""

#: inc/TemplateHooks/Course/ListCoursesTemplate.php:562
#: templates/courses-top-bar.php:23
msgid "Search courses..."
msgstr ""

#: inc/TemplateHooks/Course/ListCoursesTemplate.php:589
#: templates/courses-top-bar.php:33
msgid "Switch to %s"
msgstr ""

#: inc/TemplateHooks/Course/ListCoursesTemplate.php:657
msgid "Course Found"
msgid_plural "Courses Found"
msgstr[0] ""
msgstr[1] ""

#: inc/TemplateHooks/Course/ListCoursesTemplate.php:662
msgid "View All"
msgstr ""

#: inc/TemplateHooks/Course/ListCoursesTemplate.php:736
msgid "Free Course"
msgid_plural "Free Courses"
msgstr[0] ""
msgstr[1] ""

#: inc/TemplateHooks/Course/SingleCourseClassicTemplate.php:284
#: templates/single-course/tabs/tabs.php:31
msgid "You finished this course. This course has been blocked"
msgstr ""

#: inc/TemplateHooks/Course/SingleCourseClassicTemplate.php:289
#: templates/single-course/tabs/tabs.php:36
msgid "This course has been blocked for expiration"
msgstr ""

#: inc/TemplateHooks/Course/SingleCourseModernLayout.php:110
msgid "Last updated"
msgstr ""

#: inc/TemplateHooks/Course/SingleCourseModernLayout.php:299
msgid "Facebook"
msgstr ""

#: inc/TemplateHooks/Course/SingleCourseModernLayout.php:307
msgid "Twitter"
msgstr ""

#: inc/TemplateHooks/Course/SingleCourseModernLayout.php:316
msgid "Pinterest"
msgstr ""

#: inc/TemplateHooks/Course/SingleCourseModernLayout.php:326
msgid "Linkedin"
msgstr ""

#: inc/TemplateHooks/Course/SingleCourseModernLayout.php:369
msgid "Copied!"
msgstr ""

#: inc/TemplateHooks/Course/SingleCourseModernLayout.php:371
msgid "Copy to Clipboard"
msgstr ""

#: inc/TemplateHooks/Course/SingleCourseModernLayout.php:501
msgid "End date"
msgstr ""

#: inc/TemplateHooks/Course/SingleCourseModernLayout.php:514
msgid "Start date"
msgstr ""

#: inc/TemplateHooks/Course/SingleCourseOfflineTemplate.php:68
msgid "By"
msgstr ""

#: inc/TemplateHooks/Course/SingleCourseOfflineTemplate.php:329
msgid "lessons"
msgstr ""

#: inc/TemplateHooks/Course/SingleCourseTemplate.php:295
msgctxt "no course thumbnail"
msgid "course thumbnail"
msgstr ""

#: inc/TemplateHooks/Course/SingleCourseTemplate.php:708
msgid "Contact To Request"
msgstr ""

#: inc/TemplateHooks/Course/SingleCourseTemplate.php:823
#: templates/single-course/buttons/enroll.php:28
msgid "Start Now"
msgstr ""

#: inc/TemplateHooks/Course/SingleCourseTemplate.php:1433
msgid "There are no items in the curriculum yet."
msgstr ""

#: inc/TemplateHooks/Course/SingleCourseTemplate.php:1460
msgid "%d Section"
msgid_plural "%d Sections"
msgstr[0] ""
msgstr[1] ""

#: inc/TemplateHooks/Course/SingleCourseTemplate.php:1467
msgid "%d Lesson"
msgid_plural "%d Lessons"
msgstr[0] ""
msgstr[1] ""

#: inc/TemplateHooks/Course/SingleCourseTemplate.php:1677
msgid "%d Question"
msgid_plural "%d Questions"
msgstr[0] ""
msgstr[1] ""

#: inc/TemplateHooks/Instructor/SingleInstructorTemplate.php:141
msgid "View Profile"
msgstr ""

#: inc/TemplateHooks/Instructor/SingleInstructorTemplate.php:343
msgid "%s does not have any courses"
msgstr ""

#: inc/TemplateHooks/Profile/ProfileQuizzesTemplate.php:65
msgid "Invalid User Profile"
msgstr ""

#: inc/TemplateHooks/Profile/ProfileQuizzesTemplate.php:70
msgid "User is not exist"
msgstr ""

#: inc/TemplateHooks/Profile/ProfileQuizzesTemplate.php:95
msgid "Invalid User"
msgstr ""

#: inc/TemplateHooks/Profile/ProfileQuizzesTemplate.php:135
msgid "Started Date"
msgstr ""

#: inc/TemplateHooks/Profile/ProfileQuizzesTemplate.php:206
msgid "No quizzes found"
msgstr ""

#: inc/TemplateHooks/Profile/ProfileTemplate.php:60
msgid "edit cover image"
msgstr ""

#: inc/TemplateHooks/Profile/ProfileTemplate.php:75
#: inc/TemplateHooks/Profile/ProfileTemplate.php:154
msgid "Cover image"
msgstr ""

#: inc/TemplateHooks/Profile/ProfileTemplate.php:120
msgid "Drag and drop or click here to choose image"
msgstr ""

#: inc/TemplateHooks/Profile/ProfileTemplate.php:125
msgid "Accepted file types: JPG, PNG %1$d x %2$d (px)"
msgstr ""

#: inc/TemplateHooks/Profile/ProfileTemplate.php:230
msgid "Avatar image"
msgstr ""

#: inc/TemplateHooks/Profile/ProfileTemplate.php:241
msgid "Upload Avatar"
msgstr ""

#: inc/TemplateHooks/UserItem/UserCourseTemplate.php:94
msgid "Do you want to finish the course?"
msgstr ""

#: inc/TemplateHooks/UserItem/UserCourseTemplate.php:140
#: templates/single-course/buttons/retry.php:40
msgid "Retake course"
msgstr ""

#: inc/TemplateHooks/UserItem/UserCourseTemplate.php:149
#: templates/single-course/buttons/retry.php:25
msgid "Do you want to retake the course"
msgstr ""

#: inc/TemplateHooks/UserItem/UserCourseTemplate.php:190
msgid "completed"
msgstr ""

#: inc/TemplateHooks/UserItem/UserCourseTemplate.php:217
msgid "Course passing progress:"
msgstr ""

#: inc/TemplateHooks/UserItem/UserCourseTemplate.php:257
msgid "This course is finished."
msgstr ""

#: inc/TemplateHooks/UserItem/UserCourseTemplate.php:259
msgid "This course is expired."
msgstr ""

#: inc/TemplateHooks/UserItem/UserItemBaseTemplate.php:125
msgid "Never"
msgstr ""

#: inc/TemplateHooks/UserTemplate.php:129
#: inc/TemplateHooks/UserTemplate.php:191
#: inc/user/class-lp-profile.php:972
msgid "User Avatar"
msgstr ""

#: inc/TemplateHooks/UserTemplate.php:203
msgid "Edit avatar"
msgstr ""

#: inc/TemplateHooks/UserTemplate.php:204
msgid "edit avatar"
msgstr ""

#: inc/templates/class-lp-template-course.php:108
msgid "%1$d question"
msgid_plural "%1$d questions"
msgstr[0] ""
msgstr[1] ""

#: inc/templates/class-lp-template-course.php:284
msgid "Course is finished"
msgstr ""

#: inc/templates/class-lp-template-course.php:354
#: inc/Widgets/course-extra.php:33
msgid "Key features"
msgstr ""

#: inc/templates/class-lp-template-course.php:873
msgid "%d lesson"
msgid_plural "%d lessons"
msgstr[0] ""
msgstr[1] ""

#: inc/templates/class-lp-template-course.php:877
msgid "%d quiz"
msgid_plural "%d quizzes"
msgstr[0] ""
msgstr[1] ""

#: inc/templates/class-lp-template-course.php:881
msgid "%d student"
msgid_plural "%d students"
msgstr[0] ""
msgstr[1] ""

#: inc/user-item/class-lp-user-item-quiz.php:743
msgid "The question is invalid!"
msgstr ""

#: inc/user-item/class-lp-user-item-quiz.php:748
msgid "Cannot check the answer to the question."
msgstr ""

#: inc/user-item/class-lp-user-item.php:690
msgid "Started"
msgstr ""

#: inc/user-item/class-lp-user-item.php:698
msgid "Not Enrolled"
msgstr ""

#: inc/user/abstract-lp-user.php:704
msgid "The role %s for the user doesn't exist"
msgstr ""

#: inc/user/abstract-lp-user.php:771
msgid "You must enroll course!"
msgstr ""

#: inc/user/abstract-lp-user.php:779
msgid "Invalid lesson"
msgstr ""

#: inc/user/abstract-lp-user.php:783
msgid "You have already completed this lesson."
msgstr ""

#: inc/user/class-lp-profile.php:94
msgid "Account information updated successfully."
msgstr ""

#: inc/user/class-lp-profile.php:95
msgid "Account avatar updated successfully."
msgstr ""

#: inc/user/class-lp-profile.php:96
msgid "Password updated successfully."
msgstr ""

#: inc/user/class-lp-profile.php:97
msgid "Account privacy updated successfully."
msgstr ""

#: inc/user/class-lp-profile.php:521
msgid "Public your profile courses attended."
msgstr ""

#: inc/user/class-lp-profile.php:528
msgid "Public your profile quizzes."
msgstr ""

#: inc/user/class-lp-user.php:21
#: inc/user/class-lp-user.php:73
msgid "This content is protected. Please enroll in the course to view this content!"
msgstr ""

#: inc/user/class-lp-user.php:44
msgid "This content is protected. Please log in or enroll in the course to view this content!"
msgstr ""

#: inc/user/class-lp-user.php:61
msgid "You finished this course. This content is protected. Please enroll in the course to view this content!"
msgstr ""

#: inc/user/class-lp-user.php:67
#: templates/global/block-content.php:16
msgid "The content of this item has been blocked because the course has exceeded its duration."
msgstr ""

#: inc/user/class-lp-user.php:219
#: inc/user/class-lp-user.php:445
msgid "The course is not enrolled."
msgstr ""

#: inc/user/class-lp-user.php:286
msgid "No Course or User available"
msgstr ""

#: inc/user/class-lp-user.php:296
msgid "This course is already enrolled."
msgstr ""

#: inc/user/class-lp-user.php:366
msgid "Course is free, so you can not purchase"
msgstr ""

#: inc/user/class-lp-user.php:374
msgid "Enrollment in the course is not mandatory. You can access materials for learning or to take quizzes now."
msgstr ""

#: inc/user/class-lp-user.php:385
msgid "Your order is waiting for processing"
msgstr ""

#: inc/user/class-lp-user.php:441
msgid "The course has finished."
msgstr ""

#: inc/user/class-lp-user.php:449
msgid "Error: The course is not in progress."
msgstr ""

#: inc/user/class-lp-user.php:474
msgid "Error: Filter the disabled finished courses."
msgstr ""

#: inc/user/class-lp-user.php:498
msgid "You cannot start a quiz in preview mode."
msgstr ""

#: inc/user/class-lp-user.php:505
msgid "The course does not exist or does not contain a quiz."
msgstr ""

#: inc/user/class-lp-user.php:514
msgid "You have already finished the course of this quiz"
msgstr ""

#: inc/user/class-lp-user.php:531
msgid "The user has started or completed the quiz."
msgstr ""

#: inc/user/class-lp-user.php:540
msgid "You have to log in to start the quiz."
msgstr ""

#: inc/user/class-lp-user.php:595
msgid "The user has already finished the course of this quiz."
msgstr ""

#: inc/user/class-lp-user.php:604
msgid "The user has completed the quiz"
msgstr ""

#: inc/user/class-lp-user.php:732
msgid "quiz"
msgstr ""

#: inc/user/class-lp-user.php:733
msgid "quizzes"
msgstr ""

#: inc/user/lp-user-functions.php:345
msgid "Want to become an instructor?"
msgstr ""

#: inc/user/lp-user-functions.php:886
msgid "Please log in to enroll in this course"
msgstr ""

#: inc/user/lp-user-functions.php:904
msgid "You have already finished the course"
msgstr ""

#: inc/user/lp-user-functions.php:907
msgid "You have already enrolled in this course"
msgstr ""

#: inc/user/lp-user-functions.php:1075
msgid "Enter the old password!"
msgstr ""

#: inc/user/lp-user-functions.php:1080
msgid "The old password is incorrect!"
msgstr ""

#: inc/user/lp-user-functions.php:1087
msgid "Incorrect confirmation password!"
msgstr ""

#: inc/user/lp-user-functions.php:1367
msgid "Invalid item id."
msgstr ""

#: inc/user/lp-user-functions.php:1402
msgid "Invalid item data."
msgstr ""

#: inc/user/lp-user-functions.php:1528
msgid "Course of Quiz not enroll"
msgstr ""

#: inc/user/lp-user-functions.php:1911
msgid "Facebook Profile"
msgstr ""

#: inc/user/lp-user-functions.php:1914
msgid "Twitter Profile"
msgstr ""

#: inc/user/lp-user-functions.php:1917
msgid "Google Profile"
msgstr ""

#: inc/user/lp-user-functions.php:1920
msgid "Youtube Channel"
msgstr ""

#: inc/user/lp-user-functions.php:1923
msgid "Linkedin Profile"
msgstr ""

#: inc/Widgets/course-extra.php:20
msgid "Display the Extra information in Course settings"
msgstr ""

#: inc/Widgets/course-extra.php:22
msgid "LearnPress - Course Extra"
msgstr ""

#: inc/Widgets/course-extra.php:27
msgid "Course Extra"
msgstr ""

#: inc/Widgets/course-extra.php:34
msgid "Target audience"
msgstr ""

#: inc/Widgets/course-extra.php:40
#: inc/Widgets/course-info.php:37
#: inc/Widgets/course-progress.php:34
msgid "Select Course"
msgstr ""

#: inc/Widgets/course-extra.php:46
#: inc/Widgets/course-info.php:43
#: inc/Widgets/course-progress.php:40
msgid "CSS Class"
msgstr ""

#: inc/Widgets/course-extra.php:60
#: inc/Widgets/course-info.php:69
msgid "Error: Please select a Course."
msgstr ""

#: inc/Widgets/course-extra.php:72
msgid "No Course found!"
msgstr ""

#: inc/Widgets/course-filter.php:21
msgid "Display the Course Filter"
msgstr ""

#: inc/Widgets/course-filter.php:23
#: inc/Widgets/Course/FilterCourseWidget.php:18
msgid "LearnPress - Course Filter"
msgstr ""

#: inc/Widgets/course-info.php:27
msgid "Display the Course Infomation"
msgstr ""

#: inc/Widgets/course-info.php:29
msgid "LearnPress - Course Info"
msgstr ""

#: inc/Widgets/course-info.php:34
msgid "Course Info"
msgstr ""

#: inc/Widgets/course-progress.php:24
msgid "Display the Course Progress"
msgstr ""

#: inc/Widgets/course-progress.php:26
msgid "LearnPress - Course Progress"
msgstr ""

#: inc/Widgets/course-progress.php:31
msgid "Course Progress"
msgstr ""

#: inc/Widgets/course-progress.php:56
msgid "You need to log in to view the Course Progress"
msgstr ""

#: inc/Widgets/course-progress.php:60
msgid "Please choose a course!"
msgstr ""

#: inc/Widgets/course-progress.php:72
#: inc/Widgets/course-progress.php:76
msgid "You haven't started %s"
msgstr ""

#: inc/Widgets/Course/FilterCourseWidget.php:19
msgid "Widget Course Filter"
msgstr ""

#: inc/Widgets/featured-courses.php:23
msgid "Display the Featured courses"
msgstr ""

#: inc/Widgets/featured-courses.php:25
msgid "LearnPress - Featured Courses"
msgstr ""

#: inc/Widgets/featured-courses.php:30
msgid "Featured Courses"
msgstr ""

#: inc/Widgets/featured-courses.php:33
#: inc/Widgets/popular-courses.php:29
#: inc/Widgets/recent-courses.php:34
msgid "Show instructor"
msgstr ""

#: inc/Widgets/featured-courses.php:38
#: inc/Widgets/popular-courses.php:34
#: inc/Widgets/recent-courses.php:39
msgid "Show thumbnail"
msgstr ""

#: inc/Widgets/featured-courses.php:49
#: inc/Widgets/popular-courses.php:45
#: inc/Widgets/recent-courses.php:50
msgid "Description length"
msgstr ""

#: inc/Widgets/featured-courses.php:55
#: inc/Widgets/popular-courses.php:52
#: inc/Widgets/recent-courses.php:56
msgid "Show price"
msgstr ""

#: inc/Widgets/featured-courses.php:60
#: inc/Widgets/popular-courses.php:57
#: inc/Widgets/recent-courses.php:61
msgid "CSS class"
msgstr ""

#: inc/Widgets/featured-courses.php:65
msgid "Go to courses"
msgstr ""

#: inc/Widgets/featured-courses.php:88
#: inc/Widgets/popular-courses.php:62
#: inc/Widgets/popular-courses.php:85
#: inc/Widgets/recent-courses.php:66
#: inc/Widgets/recent-courses.php:83
msgid "Go to Courses"
msgstr ""

#: inc/Widgets/popular-courses.php:19
msgid "Display the Popular courses"
msgstr ""

#: inc/Widgets/popular-courses.php:21
msgid "LearnPress - Popular Courses"
msgstr ""

#: inc/Widgets/popular-courses.php:26
msgid "Popular Courses"
msgstr ""

#: inc/Widgets/recent-courses.php:24
msgid "Display the Recent courses"
msgstr ""

#: inc/Widgets/recent-courses.php:26
msgid "LearnPress - Recent Courses"
msgstr ""

#: inc/Widgets/recent-courses.php:31
msgid "Recent Courses"
msgstr ""

#: learnpress.php:757
msgid "Documentation"
msgstr ""

#: templates/checkout/account-logged-in.php:25
msgid "Logged in as <a href=\"%1$s\">%2$s</a>."
msgstr ""

#: templates/checkout/account-logged-in.php:31
msgid "Log out of this account"
msgstr ""

#: templates/checkout/account-logged-in.php:32
msgid "Log out &raquo;"
msgstr ""

#: templates/checkout/account-login.php:27
#: templates/checkout/account-register.php:55
msgid "Sign in"
msgstr ""

#: templates/checkout/account-login.php:31
#: templates/global/form-login.php:27
msgid "Username or email"
msgstr ""

#: templates/checkout/account-login.php:32
#: templates/global/form-login.php:28
msgid "Email or username"
msgstr ""

#: templates/checkout/account-login.php:46
#: templates/global/form-login.php:40
msgid "Remember me"
msgstr ""

#: templates/checkout/account-login.php:50
msgid "Lost password?"
msgstr ""

#: templates/checkout/account-login.php:62
msgid "Don't have an account?"
msgstr ""

#: templates/checkout/account-login.php:64
#: templates/checkout/guest-checkout.php:43
msgctxt "checkout sign up link"
msgid "Sign up"
msgstr ""

#: templates/checkout/account-register.php:18
msgid "Sign up"
msgstr ""

#: templates/checkout/account-register.php:24
#: templates/global/form-register.php:28
#: templates/profile/tabs/settings/basic-information.php:51
msgid "Email address"
msgstr ""

#: templates/checkout/account-register.php:28
#: templates/checkout/account-register.php:29
#: templates/global/form-register.php:32
#: templates/global/form-register.php:33
msgid "Username"
msgstr ""

#: templates/checkout/account-register.php:36
#: templates/global/form-register.php:40
msgid "Confirm Password"
msgstr ""

#: templates/checkout/account-register.php:53
msgid "Already had an account?"
msgstr ""

#: templates/checkout/form.php:22
msgid "Please %s in to enroll in the course!"
msgstr ""

#: templates/checkout/form.php:26
#: templates/single-course/content-protected.php:29
msgid "login"
msgstr ""

#: templates/checkout/guest-checkout-link.php:16
msgid "Or quick checkout as"
msgstr ""

#: templates/checkout/guest-checkout-link.php:20
msgctxt "checkout guest link"
msgid "Guest"
msgstr ""

#: templates/checkout/guest-checkout.php:21
msgid "As Guest"
msgstr ""

#: templates/checkout/guest-checkout.php:24
msgid "Enter your email..."
msgstr ""

#: templates/checkout/guest-checkout.php:26
msgid "An order key to activate the course will be sent to your email after the payment has proceeded successfully."
msgstr ""

#: templates/checkout/guest-checkout.php:35
msgctxt "checkout sign in link"
msgid "Sign in"
msgstr ""

#: templates/checkout/guest-checkout.php:48
msgid "Or you can %1$s%2$s %3$s now."
msgstr ""

#: templates/checkout/order-comment.php:16
msgid "Additional Information"
msgstr ""

#: templates/checkout/order-comment.php:17
msgid "Note to administrator"
msgstr ""

#: templates/checkout/order-received.php:25
msgid "Invalid order."
msgstr ""

#: templates/checkout/order-received.php:48
msgid "Order Key"
msgstr ""

#: templates/checkout/order-received.php:55
#: templates/emails/order-items-table.php:62
#: templates/emails/plain/order-items-table.php:52
msgid "Order Number"
msgstr ""

#: templates/checkout/order-received.php:127
#: templates/emails/order-items-table.php:70
#: templates/emails/plain/order-items-table.php:56
msgid "Payment Method"
msgstr ""

#: templates/checkout/payment.php:28
msgctxt "payment method"
msgid "Secure Connection"
msgstr ""

#: templates/checkout/review-order.php:28
msgid "Your order"
msgstr ""

#: templates/checkout/review-order.php:142
#: templates/order/order-details.php:108
msgid "Subtotal"
msgstr ""

#: templates/checkout/term-conditions.php:22
msgid "Terms of Service"
msgstr ""

#: templates/checkout/term-conditions.php:30
msgid "By completing your purchase you agree to those <a href=\"%1$s\" target=\"_blank\">%2$s</a>."
msgstr ""

#: templates/content-lesson/button-complete.php:24
msgid "Do you want to complete the lesson"
msgstr ""

#: templates/content-lesson/button-complete.php:39
msgid "You have completed this lesson at "
msgstr ""

#: templates/content-lesson/button-complete.php:57
msgid "Complete lesson"
msgstr ""

#: templates/content-lesson/button-complete.php:69
msgid "Complete"
msgstr ""

#: templates/content-lesson/content.php:24
msgid "The lesson content is empty."
msgstr ""

#: templates/content-quiz/js.php:157
msgid "You haven't any question!"
msgstr ""

#: templates/emails/order-items-table.php:57
#: templates/emails/plain/order-items-table.php:50
msgid "Order summary"
msgstr ""

#: templates/emails/order-items-table.php:66
#: templates/emails/plain/order-items-table.php:54
msgid "Purchase Date"
msgstr ""

#: templates/emails/order-items-table.php:78
#: templates/emails/plain/order-items-table.php:60
msgid "User Email"
msgstr ""

#: templates/emails/plain/order-items-table.php:76
msgid "Quantity: %s"
msgstr ""

#: templates/emails/plain/order-items-table.php:78
msgid "Cost: %s"
msgstr ""

#: templates/global/become-teacher-form.php:36
msgid "Your name"
msgstr ""

#: templates/global/become-teacher-form.php:40
msgid "Your email address"
msgstr ""

#: templates/global/become-teacher-form.php:43
msgid "Phone"
msgstr ""

#: templates/global/become-teacher-form.php:44
msgid "Your phone number"
msgstr ""

#: templates/global/become-teacher-form.php:47
msgid "Message"
msgstr ""

#: templates/global/become-teacher-form.php:48
msgid "Your message"
msgstr ""

#: templates/global/become-teacher-form.php:57
msgid "Submitting"
msgstr ""

#: templates/global/before-main-content.php:25
msgid "The LearnPress <strong>Checkout</strong> page is not set up."
msgstr ""

#: templates/global/before-main-content.php:28
msgid "Please contact the administrator to set up this page."
msgstr ""

#: templates/global/before-main-content.php:30
msgid "Please <a href=\\\"%s\\\" target=\\\"_blank\\\">setup</a> it so users can purchase courses."
msgstr ""

#: templates/global/breadcrumb.php:31
msgid "Search results for: "
msgstr ""

#: templates/global/form-login.php:17
msgctxt "login-heading"
msgid "Login"
msgstr ""

#: templates/global/form-login.php:51
msgid "Login"
msgstr ""

#: templates/global/form-login.php:54
msgid "Lost your password?"
msgstr ""

#: templates/global/form-register.php:17
msgctxt "register-heading"
msgid "Register"
msgstr ""

#: templates/global/no-courses-found.php:15
msgid "No courses were found to match your selection."
msgstr ""

#: templates/loop/single-course/loop-section.php:28
#: templates/single-course/loop-section.php:47
msgctxt "template title empty"
msgid "Untitled"
msgstr ""

#: templates/loop/single-course/loop-section.php:56
msgid "Show more items"
msgstr ""

#: templates/order/confirm.php:23
msgid "Unfortunately your order cannot be processed as the originating bank/merchant has declined your transaction."
msgstr ""

#: templates/order/confirm.php:29
msgid "Please attempt your purchase again or go to your account page."
msgstr ""

#: templates/order/confirm.php:31
msgid "Please attempt your purchase again."
msgstr ""

#: templates/order/confirm.php:47
msgid "Order Number:"
msgstr ""

#: templates/order/confirm.php:52
msgid "Date:"
msgstr ""

#: templates/order/confirm.php:64
msgid "Payment Method:"
msgstr ""

#: templates/order/order-details.php:127
msgid "Order status:"
msgstr ""

#: templates/order/recover-form.php:18
msgid "Order key"
msgstr ""

#: templates/order/recover-form.php:21
msgid "Recover"
msgstr ""

#: templates/profile/not-logged-in.php:16
msgid "Please <a href=\"%s\">login</a> to see your profile content"
msgstr ""

#: templates/profile/tabs/courses/course-grid.php:64
#: templates/profile/tabs/courses/course-list.php:83
msgid "View more"
msgstr ""

#: templates/profile/tabs/courses/general-statistic.php:27
msgid "Enrolled Courses"
msgstr ""

#: templates/profile/tabs/courses/general-statistic.php:31
msgid "The total number of courses is being learned"
msgstr ""

#: templates/profile/tabs/courses/general-statistic.php:32
msgid "Active Courses"
msgstr ""

#: templates/profile/tabs/courses/general-statistic.php:35
msgid "Total courses have finished"
msgstr ""

#: templates/profile/tabs/courses/general-statistic.php:36
msgid "Completed Courses"
msgstr ""

#: templates/profile/tabs/courses/general-statistic.php:44
msgid "Total created courses"
msgstr ""

#: templates/profile/tabs/courses/general-statistic.php:48
msgid "Total attended students"
msgstr ""

#: templates/profile/tabs/orders/list.php:20
msgid "No orders!"
msgstr ""

#: templates/profile/tabs/orders/list.php:25
msgid "My Orders"
msgstr ""

#: templates/profile/tabs/orders/order-message.php:20
msgid "This order is paid for %s"
msgstr ""

#: templates/profile/tabs/orders/order-message.php:24
msgid "This order is paid by %s"
msgstr ""

#: templates/profile/tabs/orders/recover-order.php:16
msgid "If you have a valid order key, you can recover it here."
msgstr ""

#: templates/profile/tabs/orders/recover-order.php:17
msgid "When you checkout as a Guest, an order key will be sent to your email. You can use the order key to create an order."
msgstr ""

#: templates/profile/tabs/quizzes.php:120
msgid "No quizzes!"
msgstr ""

#: templates/profile/tabs/settings/basic-information.php:58
msgid "Biographical Info"
msgstr ""

#: templates/profile/tabs/settings/basic-information.php:68
msgid "Share a little biographical information to fill out your profile. This may be shown publicly."
msgstr ""

#: templates/profile/tabs/settings/basic-information.php:104
#: templates/profile/tabs/settings/change-password.php:59
#: templates/profile/tabs/settings/privacy.php:51
msgid "Save changes"
msgstr ""

#: templates/profile/tabs/settings/change-password.php:30
msgid "Current password"
msgstr ""

#: templates/profile/tabs/settings/change-password.php:36
msgid "New password"
msgstr ""

#: templates/profile/tabs/settings/change-password.php:42
msgid "Confirm new password"
msgstr ""

#: templates/profile/tabs/settings/change-password.php:45
msgid "The new password does not match!"
msgstr ""

#: templates/shortcode/list-courses.php:44
#: templates/widgets/featured-courses.php:22
#: templates/widgets/popular-courses.php:20
#: templates/widgets/recent-courses.php:20
msgid "No courses"
msgstr ""

#: templates/single-course/buttons/finish.php:15
#: templates/single-course/buttons/finish.php:17
msgid "Finish course"
msgstr ""

#: templates/single-course/content-item/nav.php:30
msgctxt "course-item-navigation"
msgid "Prev"
msgstr ""

#: templates/single-course/content-item/nav.php:39
msgctxt "course-item-navigation"
msgid "Next"
msgstr ""

#: templates/single-course/content-item/popup-header.php:39
msgid "<span class=\"items-completed\">%1$s</span> of %2$d items"
msgstr ""

#: templates/single-course/content-item/popup-sidebar.php:26
msgctxt "Search for course content"
msgid "Search for course content"
msgstr ""

#: templates/single-course/content-protected.php:22
msgid "This content is protected, please %1$s and %2$s in the course to view this content!"
msgstr ""

#: templates/single-course/content-protected.php:34
#: templates/single-course/content-protected.php:35
msgid "enroll"
msgstr ""

#: templates/single-course/graduation.php:13
msgctxt "course graduation"
msgid "un-graduated"
msgstr ""

#: templates/single-course/instructor.php:33
msgid "About the Instructor"
msgstr ""

#: templates/single-course/loop-section.php:67
msgid "Section progress %s%%"
msgstr ""

#: templates/single-course/loop-section.php:79
msgid "No items in this section"
msgstr ""

#: templates/single-course/meta/category.php:19
msgid "Uncategorized"
msgstr ""

#: templates/single-course/sidebar/user-progress.php:25
msgid "Lessons completed:"
msgstr ""

#: templates/single-course/sidebar/user-progress.php:37
msgid "Quizzes finished:"
msgstr ""

#: templates/single-course/sidebar/user-progress.php:39
msgid "Failed %1$d, Passed %2$d"
msgstr ""

#: templates/single-course/sidebar/user-progress.php:39
msgid "%1$d/%2$d"
msgstr ""

#: templates/single-course/sidebar/user-progress.php:63
msgid "Passing condition: %s%%"
msgstr ""

#: templates/single-course/sidebar/user-time.php:30
msgid "You started on:"
msgstr ""

#: templates/single-course/sidebar/user-time.php:36
msgid "Course will end:"
msgstr ""

#: templates/single-course/sidebar/user-time.php:47
msgid "You finished on:"
msgstr ""

#: templates/single-course/tabs/curriculum-v2.php:39
#: templates/single-course/tabs/curriculum.php:56
msgid "The curriculum is empty"
msgstr ""

#: templates/single-course/tabs/curriculum-v2.php:47
msgid "Show more Sections"
msgstr ""

#: assets/src/apps/js/blocks/archive-course-legacy/block.json
msgctxt "block title"
msgid "Archive Course (Legacy)"
msgstr ""

#: assets/src/apps/js/blocks/archive-course-legacy/block.json
msgctxt "block description"
msgid "Renders template Archive Course PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/archive-course-legacy/block.json
#: assets/src/apps/js/blocks/single-course-legacy/block.json
msgctxt "block keyword"
msgid "legacy"
msgstr ""

#: assets/src/apps/js/blocks/archive-course-legacy/block.json
#: assets/src/apps/js/blocks/breadcrumb/block.json
#: assets/src/apps/js/blocks/course-elements/course-address/block.json
#: assets/src/apps/js/blocks/course-elements/course-button-read-more/block.json
#: assets/src/apps/js/blocks/course-elements/course-button/block.json
#: assets/src/apps/js/blocks/course-elements/course-capacity/block.json
#: assets/src/apps/js/blocks/course-elements/course-categories/block.json
#: assets/src/apps/js/blocks/course-elements/course-curriculum/block.json
#: assets/src/apps/js/blocks/course-elements/course-delivery/block.json
#: assets/src/apps/js/blocks/course-elements/course-description/block.json
#: assets/src/apps/js/blocks/course-elements/course-duration/block.json
#: assets/src/apps/js/blocks/course-elements/course-faqs/block.json
#: assets/src/apps/js/blocks/course-elements/course-feature-review/block.json
#: assets/src/apps/js/blocks/course-elements/course-featured/block.json
#: assets/src/apps/js/blocks/course-elements/course-features/block.json
#: assets/src/apps/js/blocks/course-elements/course-image/block.json
#: assets/src/apps/js/blocks/course-elements/course-instructor-info/block.json
#: assets/src/apps/js/blocks/course-elements/course-instructor/block.json
#: assets/src/apps/js/blocks/course-elements/course-item-curriculum/block.json
#: assets/src/apps/js/blocks/course-elements/course-lesson/block.json
#: assets/src/apps/js/blocks/course-elements/course-level/block.json
#: assets/src/apps/js/blocks/course-elements/course-material/block.json
#: assets/src/apps/js/blocks/course-elements/course-offline-lesson/block.json
#: assets/src/apps/js/blocks/course-elements/course-price/block.json
#: assets/src/apps/js/blocks/course-elements/course-progress/block.json
#: assets/src/apps/js/blocks/course-elements/course-quiz/block.json
#: assets/src/apps/js/blocks/course-elements/course-requirements/block.json
#: assets/src/apps/js/blocks/course-elements/course-share/block.json
#: assets/src/apps/js/blocks/course-elements/course-student/block.json
#: assets/src/apps/js/blocks/course-elements/course-target-audiences/block.json
#: assets/src/apps/js/blocks/course-elements/course-title/block.json
#: assets/src/apps/js/blocks/course-filter-elements/button-reset-filter/block.json
#: assets/src/apps/js/blocks/course-filter-elements/button-submit-filter/block.json
#: assets/src/apps/js/blocks/course-filter-elements/course-author-filter/block.json
#: assets/src/apps/js/blocks/course-filter-elements/course-categories-filter/block.json
#: assets/src/apps/js/blocks/course-filter-elements/course-level-filter/block.json
#: assets/src/apps/js/blocks/course-filter-elements/course-price-filter/block.json
#: assets/src/apps/js/blocks/course-filter-elements/course-search-filter/block.json
#: assets/src/apps/js/blocks/course-filter-elements/course-tag-filter/block.json
#: assets/src/apps/js/blocks/course-filter/block.json
#: assets/src/apps/js/blocks/courses/course-item-template/block.json
#: assets/src/apps/js/blocks/courses/course-order-by/block.json
#: assets/src/apps/js/blocks/courses/course-search/block.json
#: assets/src/apps/js/blocks/courses/list-courses/block.json
#: assets/src/apps/js/blocks/instructor-elements/instructor-avatar/block.json
#: assets/src/apps/js/blocks/instructor-elements/instructor-background/block.json
#: assets/src/apps/js/blocks/instructor-elements/instructor-course/block.json
#: assets/src/apps/js/blocks/instructor-elements/instructor-description/block.json
#: assets/src/apps/js/blocks/instructor-elements/instructor-name/block.json
#: assets/src/apps/js/blocks/instructor-elements/instructor-social/block.json
#: assets/src/apps/js/blocks/instructor-elements/instructor-student/block.json
#: assets/src/apps/js/blocks/single-course-item/item-close/block.json
#: assets/src/apps/js/blocks/single-course-item/item-comment/block.json
#: assets/src/apps/js/blocks/single-course-item/item-content/block.json
#: assets/src/apps/js/blocks/single-course-item/item-curriculum/block.json
#: assets/src/apps/js/blocks/single-course-item/item-hidden-sidebar/block.json
#: assets/src/apps/js/blocks/single-course-item/item-navigation/block.json
#: assets/src/apps/js/blocks/single-course-item/item-progress/block.json
#: assets/src/apps/js/blocks/single-course-item/item-search/block.json
#: assets/src/apps/js/blocks/single-course-legacy/block.json
#: assets/src/apps/js/blocks/single-course/block.json
#: assets/src/apps/js/blocks/single-instructor/block.json
msgctxt "block keyword"
msgid "learnpress"
msgstr ""

#: assets/src/apps/js/blocks/breadcrumb/block.json
msgctxt "block title"
msgid "Learnpress Breadcrumb"
msgstr ""

#: assets/src/apps/js/blocks/breadcrumb/block.json
msgctxt "block description"
msgid "Renders Learnpress Breadcrumb PHP Template."
msgstr ""

#: assets/src/apps/js/blocks/breadcrumb/block.json
msgctxt "block keyword"
msgid "learnpress breadcrumb"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-address/block.json
msgctxt "block title"
msgid "Course Address"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-address/block.json
msgctxt "block description"
msgid "Renders template Address Course PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-address/block.json
msgctxt "block keyword"
msgid "address"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-address/block.json
msgctxt "block keyword"
msgid "single"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-address/block.json
#: assets/src/apps/js/blocks/course-elements/course-offline-lesson/block.json
msgctxt "block keyword"
msgid "offline"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-address/block.json
msgctxt "block keyword"
msgid "course"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-button-read-more/block.json
msgctxt "block title"
msgid "Course Button Read More"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-button-read-more/block.json
msgctxt "block description"
msgid "Renders template Button Course Read More PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-button-read-more/block.json
msgctxt "block keyword"
msgid "button read more single course"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-button/block.json
msgctxt "block title"
msgid "Course Button"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-button/block.json
msgctxt "block description"
msgid "Renders template Button Course PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-button/block.json
msgctxt "block keyword"
msgid "button single course"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-capacity/block.json
msgctxt "block title"
msgid "Course Capacity"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-capacity/block.json
msgctxt "block description"
msgid "Show number capacity of course."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-capacity/block.json
msgctxt "block keyword"
msgid "capacity"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-capacity/block.json
#: assets/src/apps/js/blocks/course-elements/course-lesson/block.json
#: assets/src/apps/js/blocks/course-elements/course-offline-lesson/block.json
#: assets/src/apps/js/blocks/course-elements/course-student/block.json
msgctxt "block keyword"
msgid "count"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-categories/block.json
msgctxt "block title"
msgid "Course Category"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-categories/block.json
msgctxt "block description"
msgid "Renders template List Category Course PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-categories/block.json
msgctxt "block keyword"
msgid "category Category single course"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-curriculum/block.json
msgctxt "block title"
msgid "Course Curriculum"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-curriculum/block.json
msgctxt "block description"
msgid "Renders template Curriculum Course PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-curriculum/block.json
msgctxt "block keyword"
msgid "curriculum single course"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-delivery/block.json
msgctxt "block title"
msgid "Course Delivery Type"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-delivery/block.json
msgctxt "block description"
msgid "Show type delivery of course."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-delivery/block.json
msgctxt "block keyword"
msgid "delivery"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-delivery/block.json
msgctxt "block keyword"
msgid "type"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-description/block.json
msgctxt "block title"
msgid "Course Description"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-description/block.json
msgctxt "block description"
msgid "Renders template Course Description PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-description/block.json
msgctxt "block keyword"
msgid "description single course"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-duration/block.json
msgctxt "block title"
msgid "Course Duration"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-duration/block.json
msgctxt "block description"
msgid "Show Duration of Course."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-duration/block.json
msgctxt "block keyword"
msgid "duration"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-faqs/block.json
msgctxt "block title"
msgid "Course Faqs"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-faqs/block.json
msgctxt "block description"
msgid "Renders template Box Extra Faqs Course PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-faqs/block.json
msgctxt "block keyword"
msgid "box extra faqs single course"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-feature-review/block.json
msgctxt "block title"
msgid "Course Feature Review"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-feature-review/block.json
msgctxt "block description"
msgid "Renders template Feature Review Course PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-feature-review/block.json
msgctxt "block keyword"
msgid "feature review single course"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-featured/block.json
msgctxt "block title"
msgid "Course Featured"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-featured/block.json
msgctxt "block description"
msgid "Course featured."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-featured/block.json
msgctxt "block keyword"
msgid "course featured"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-features/block.json
msgctxt "block title"
msgid "Course Features"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-features/block.json
msgctxt "block description"
msgid "Renders template Box Extra Features Course PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-features/block.json
msgctxt "block keyword"
msgid "box extra features single course"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-image/block.json
msgctxt "block title"
msgid "Course Image"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-image/block.json
msgctxt "block description"
msgid "Renders template Image Course PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-image/block.json
msgctxt "block keyword"
msgid "image single course"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-instructor-info/block.json
msgctxt "block title"
msgid "Course Instructor Info"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-instructor-info/block.json
msgctxt "block description"
msgid "Renders template Instructor Section PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-instructor-info/block.json
msgctxt "block keyword"
msgid "instructor info single course"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-instructor/block.json
msgctxt "block title"
msgid "Course Instructor"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-instructor/block.json
msgctxt "block description"
msgid "Renders template Instructor Course PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-instructor/block.json
msgctxt "block keyword"
msgid "instructor single course"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-item-curriculum/block.json
msgctxt "block title"
msgid "Course Item Curriculum (Legacy)"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-item-curriculum/block.json
msgctxt "block description"
msgid "Renders Course Item Curriculum Block PHP Template."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-item-curriculum/block.json
msgctxt "block keyword"
msgid "item curriculum course single"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-lesson/block.json
msgctxt "block title"
msgid "Course Lesson Count"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-lesson/block.json
msgctxt "block description"
msgid "Show number lesson of course."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-lesson/block.json
#: assets/src/apps/js/blocks/course-elements/course-offline-lesson/block.json
msgctxt "block keyword"
msgid "lesson"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-level/block.json
msgctxt "block title"
msgid "Course Level"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-level/block.json
msgctxt "block description"
msgid "Show level course."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-level/block.json
msgctxt "block keyword"
msgid "level"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-material/block.json
msgctxt "block title"
msgid "Course Material"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-material/block.json
msgctxt "block description"
msgid "Renders template Material Course PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-material/block.json
msgctxt "block keyword"
msgid "material single course"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-offline-lesson/block.json
msgctxt "block title"
msgid "Course Offline Lesson"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-offline-lesson/block.json
msgctxt "block description"
msgid "Show number lesson of course offline."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-price/block.json
msgctxt "block title"
msgid "Course Price"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-price/block.json
msgctxt "block description"
msgid "Renders template Price Course PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-price/block.json
msgctxt "block keyword"
msgid "price single course"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-progress/block.json
msgctxt "block title"
msgid "Course Progress"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-progress/block.json
msgctxt "block description"
msgid "Renders template Progress Course PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-progress/block.json
msgctxt "block keyword"
msgid "progress single course"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-quiz/block.json
msgctxt "block title"
msgid "Course Quiz Count"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-quiz/block.json
msgctxt "block description"
msgid "Show number quiz of course."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-quiz/block.json
msgctxt "block keyword"
msgid "quiz"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-requirements/block.json
msgctxt "block title"
msgid "Course Requirements"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-requirements/block.json
msgctxt "block description"
msgid "Renders template Box Extra Requirements Course PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-requirements/block.json
msgctxt "block keyword"
msgid "box extra requirements single course"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-share/block.json
msgctxt "block title"
msgid "Course Share"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-share/block.json
msgctxt "block description"
msgid "Renders Share Course Block PHP Template."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-share/block.json
msgctxt "block keyword"
msgid "share course single"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-student/block.json
msgctxt "block title"
msgid "Course Student Count"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-student/block.json
msgctxt "block description"
msgid "Show number students attempt course."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-student/block.json
msgctxt "block keyword"
msgid "student"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-target-audiences/block.json
msgctxt "block title"
msgid "Course Target audiences"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-target-audiences/block.json
msgctxt "block description"
msgid "Renders template Box Extra Target audiences Course PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-target-audiences/block.json
msgctxt "block keyword"
msgid "box extra target audiences single course"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-title/block.json
msgctxt "block title"
msgid "Course Title"
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-title/block.json
msgctxt "block description"
msgid "Course title."
msgstr ""

#: assets/src/apps/js/blocks/course-elements/course-title/block.json
msgctxt "block keyword"
msgid "course title"
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/button-reset-filter/block.json
msgctxt "block title"
msgid "Button Reset Filter"
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/button-reset-filter/block.json
msgctxt "block description"
msgid "Renders template Button Reset Filter PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/button-reset-filter/block.json
msgctxt "block keyword"
msgid "category button reset filter course"
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/button-submit-filter/block.json
msgctxt "block title"
msgid "Button Submit Filter"
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/button-submit-filter/block.json
msgctxt "block description"
msgid "Renders template Button Submit Filter PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/button-submit-filter/block.json
msgctxt "block keyword"
msgid "category button submit filter course"
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/course-author-filter/block.json
msgctxt "block title"
msgid "Author Filter"
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/course-author-filter/block.json
msgctxt "block description"
msgid "Renders template Author Filter PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/course-author-filter/block.json
#: assets/src/apps/js/blocks/course-filter/block.json
msgctxt "block keyword"
msgid "filter"
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/course-categories-filter/block.json
msgctxt "block title"
msgid "Categories Filter"
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/course-categories-filter/block.json
msgctxt "block description"
msgid "Renders template Categories Filter PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/course-categories-filter/block.json
msgctxt "block keyword"
msgid "categories filter archive course"
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/course-level-filter/block.json
msgctxt "block title"
msgid "Level Filter"
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/course-level-filter/block.json
msgctxt "block description"
msgid "Renders template Level Filter PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/course-level-filter/block.json
msgctxt "block keyword"
msgid "level filter archive course"
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/course-price-filter/block.json
msgctxt "block title"
msgid "Price Filter"
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/course-price-filter/block.json
msgctxt "block description"
msgid "Renders template Price Filter PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/course-price-filter/block.json
msgctxt "block keyword"
msgid "price filter archive course"
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/course-search-filter/block.json
msgctxt "block title"
msgid "Search Filter"
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/course-search-filter/block.json
msgctxt "block description"
msgid "Renders template Search Filter PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/course-search-filter/block.json
msgctxt "block keyword"
msgid "search filter archive course"
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/course-tag-filter/block.json
msgctxt "block title"
msgid "Tag Filter"
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/course-tag-filter/block.json
msgctxt "block description"
msgid "Renders template Tag Filter PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/course-filter-elements/course-tag-filter/block.json
msgctxt "block keyword"
msgid "tag filter archive course"
msgstr ""

#: assets/src/apps/js/blocks/course-filter/block.json
msgctxt "block title"
msgid "Course Filter"
msgstr ""

#: assets/src/apps/js/blocks/course-filter/block.json
msgctxt "block description"
msgid "Show filter fields for courses."
msgstr ""

#: assets/src/apps/js/blocks/courses/course-item-template/block.json
msgctxt "block title"
msgid "Course Item Template"
msgstr ""

#: assets/src/apps/js/blocks/courses/course-item-template/block.json
msgctxt "block description"
msgid "Course Item Template"
msgstr ""

#: assets/src/apps/js/blocks/courses/course-item-template/block.json
msgctxt "block keyword"
msgid "course item"
msgstr ""

#: assets/src/apps/js/blocks/courses/course-order-by/block.json
msgctxt "block title"
msgid "Course Sorting"
msgstr ""

#: assets/src/apps/js/blocks/courses/course-order-by/block.json
msgctxt "block description"
msgid "Show sorting select box for courses."
msgstr ""

#: assets/src/apps/js/blocks/courses/course-order-by/block.json
msgctxt "block keyword"
msgid "sort"
msgstr ""

#: assets/src/apps/js/blocks/courses/course-search/block.json
msgctxt "block title"
msgid "Course Search"
msgstr ""

#: assets/src/apps/js/blocks/courses/course-search/block.json
msgctxt "block description"
msgid "Show Search Course."
msgstr ""

#: assets/src/apps/js/blocks/courses/course-search/block.json
msgctxt "block keyword"
msgid "search"
msgstr ""

#: assets/src/apps/js/blocks/courses/list-courses/block.json
msgctxt "block title"
msgid "Course Listing"
msgstr ""

#: assets/src/apps/js/blocks/courses/list-courses/block.json
msgctxt "block description"
msgid "Course Listing block"
msgstr ""

#: assets/src/apps/js/blocks/courses/list-courses/block.json
msgctxt "block keyword"
msgid "listing course"
msgstr ""

#: assets/src/apps/js/blocks/courses/list-courses/block.json
msgctxt "block keyword"
msgid "query loop"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-avatar/block.json
msgctxt "block title"
msgid "Instructor Avatar"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-avatar/block.json
msgctxt "block description"
msgid "Renders template Instructor Avatar PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-avatar/block.json
msgctxt "block keyword"
msgid "instructor avatar single"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-background/block.json
msgctxt "block title"
msgid "Instructor Background"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-background/block.json
msgctxt "block description"
msgid "Renders template Instructor Background PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-background/block.json
msgctxt "block keyword"
msgid "instructor background single"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-course/block.json
msgctxt "block title"
msgid "Instructor Count Course"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-course/block.json
msgctxt "block description"
msgid "Renders template instructor count course PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-course/block.json
msgctxt "block keyword"
msgid "instructor course single"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-description/block.json
msgctxt "block title"
msgid "Instructor Description"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-description/block.json
msgctxt "block description"
msgid "Renders template Instructor Description PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-description/block.json
msgctxt "block keyword"
msgid "instructor description single"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-name/block.json
msgctxt "block title"
msgid "Instructor Name"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-name/block.json
msgctxt "block description"
msgid "Renders template Instructor Name PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-name/block.json
msgctxt "block keyword"
msgid "instructor name single"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-social/block.json
msgctxt "block title"
msgid "Instructor Social"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-social/block.json
msgctxt "block description"
msgid "Renders template Instructor Social PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-social/block.json
msgctxt "block keyword"
msgid "instructor social single"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-student/block.json
msgctxt "block title"
msgid "Instructor Count Student"
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-student/block.json
msgctxt "block description"
msgid "Renders template instructor count student PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/instructor-elements/instructor-student/block.json
msgctxt "block keyword"
msgid "instructor student single"
msgstr ""

#: assets/src/apps/js/blocks/single-course-item/item-close/block.json
msgctxt "block title"
msgid "Item Close"
msgstr ""

#: assets/src/apps/js/blocks/single-course-item/item-close/block.json
#: assets/src/apps/js/blocks/single-course-item/item-comment/block.json
#: assets/src/apps/js/blocks/single-course-item/item-content/block.json
#: assets/src/apps/js/blocks/single-course-item/item-curriculum/block.json
#: assets/src/apps/js/blocks/single-course-item/item-hidden-sidebar/block.json
#: assets/src/apps/js/blocks/single-course-item/item-navigation/block.json
#: assets/src/apps/js/blocks/single-course-item/item-progress/block.json
#: assets/src/apps/js/blocks/single-course-item/item-search/block.json
#: assets/src/apps/js/blocks/single-course-legacy/block.json
msgctxt "block description"
msgid "Renders template Single Course Legacy PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/single-course-item/item-close/block.json
msgctxt "block keyword"
msgid "item close course"
msgstr ""

#: assets/src/apps/js/blocks/single-course-item/item-comment/block.json
msgctxt "block title"
msgid "Item Comment"
msgstr ""

#: assets/src/apps/js/blocks/single-course-item/item-comment/block.json
msgctxt "block keyword"
msgid "item comment"
msgstr ""

#: assets/src/apps/js/blocks/single-course-item/item-content/block.json
msgctxt "block title"
msgid "Item Content"
msgstr ""

#: assets/src/apps/js/blocks/single-course-item/item-content/block.json
msgctxt "block keyword"
msgid "item content course"
msgstr ""

#: assets/src/apps/js/blocks/single-course-item/item-curriculum/block.json
msgctxt "block title"
msgid "Item Curriculum"
msgstr ""

#: assets/src/apps/js/blocks/single-course-item/item-curriculum/block.json
msgctxt "block keyword"
msgid "item curriculum course"
msgstr ""

#: assets/src/apps/js/blocks/single-course-item/item-hidden-sidebar/block.json
msgctxt "block title"
msgid "Item Hidden Sidebar"
msgstr ""

#: assets/src/apps/js/blocks/single-course-item/item-hidden-sidebar/block.json
msgctxt "block keyword"
msgid "item hidden sidebar course"
msgstr ""

#: assets/src/apps/js/blocks/single-course-item/item-navigation/block.json
msgctxt "block title"
msgid "Item Nagivation"
msgstr ""

#: assets/src/apps/js/blocks/single-course-item/item-navigation/block.json
msgctxt "block keyword"
msgid "item nagivation course"
msgstr ""

#: assets/src/apps/js/blocks/single-course-item/item-progress/block.json
msgctxt "block title"
msgid "Item Progress"
msgstr ""

#: assets/src/apps/js/blocks/single-course-item/item-progress/block.json
msgctxt "block keyword"
msgid "item progress course"
msgstr ""

#: assets/src/apps/js/blocks/single-course-item/item-search/block.json
msgctxt "block title"
msgid "Item Search"
msgstr ""

#: assets/src/apps/js/blocks/single-course-item/item-search/block.json
msgctxt "block keyword"
msgid "item search course"
msgstr ""

#: assets/src/apps/js/blocks/single-course-legacy/block.json
msgctxt "block title"
msgid "Single Course (Legacy)"
msgstr ""

#: assets/src/apps/js/blocks/single-course/block.json
msgctxt "block title"
msgid "Single Course"
msgstr ""

#: assets/src/apps/js/blocks/single-course/block.json
msgctxt "block description"
msgid "Renders Template Single Course PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/single-course/block.json
msgctxt "block keyword"
msgid "learnpress single course"
msgstr ""

#: assets/src/apps/js/blocks/single-instructor/block.json
msgctxt "block title"
msgid "Single Instructor"
msgstr ""

#: assets/src/apps/js/blocks/single-instructor/block.json
msgctxt "block description"
msgid "Renders Template Single Instructor PHP templates."
msgstr ""

#: assets/src/apps/js/blocks/single-instructor/block.json
msgctxt "block keyword"
msgid "learnpress single instructor"
msgstr ""
