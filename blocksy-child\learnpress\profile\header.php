<?php
/**
 * Custom Template for displaying profile header.
 *
 * This template overrides the default LearnPress profile header
 * to implement a custom design with user avatar, name, and navigation.
 *
 * <AUTHOR> Theme
 * @package Blocksy-Child/LearnPress
 * @version 1.0.0
 */

use LearnPress\Helpers\Template;

defined( 'ABSPATH' ) || exit;

$profile = LP_Profile::instance();
$user    = $profile->get_user();

if ( ! isset( $user ) ) {
	return;
}

$bio = $user->get_description();
$user_name = $user->get_display_name();
$user_email = $user->get_email();
$avatar_url = get_avatar_url( $user->get_id(), array( 'size' => 120 ) );
$cover_image_url = get_user_meta( $user->get_id(), '_lp_cover_image', true );

// Get user statistics
$enrolled_courses = learn_press_get_enrolled_courses( $user->get_id() );
$completed_courses = 0;
$total_progress = 0;

if ( ! empty( $enrolled_courses ) ) {
	foreach ( $enrolled_courses as $course_id ) {
		$progress = learn_press_get_user_course_progress( $user->get_id(), $course_id );
		$total_progress += $progress;
		if ( $progress >= 100 ) {
			$completed_courses++;
		}
	}
}

$average_progress = ! empty( $enrolled_courses ) ? round( $total_progress / count( $enrolled_courses ), 0 ) : 0;
$followers = get_user_meta( $user->get_id(), '_lp_followers', true ) ?: 6;
$following = get_user_meta( $user->get_id(), '_lp_following', true ) ?: 0;
?>

<div class="custom-profile-header-wrapper">
	
	<!-- Cover Image Section -->
	<?php if ( $cover_image_url ) : ?>
	<div class="profile-cover-image" style="background-image: url('<?php echo esc_url( $cover_image_url ); ?>');">
		<div class="cover-overlay"></div>
	</div>
	<?php endif; ?>

	<!-- Main Header Content -->
	<div class="profile-header-main">
		<div class="profile-header-container">
			
			<!-- Left Section: Avatar and Basic Info -->
			<div class="profile-header-left">
				<div class="profile-avatar-wrapper">
					<img src="<?php echo esc_url( $avatar_url ); ?>" alt="<?php echo esc_attr( $user_name ); ?>" class="profile-avatar" />
					<?php if ( $user->get_id() === get_current_user_id() ) : ?>
						<a href="<?php echo esc_url( $profile->get_tab_link( 'settings', 'avatar' ) ); ?>" class="edit-avatar-btn">
							<i class="lp-icon-camera"></i>
						</a>
					<?php endif; ?>
				</div>
				
				<div class="profile-basic-info">
					<h1 class="profile-user-name"><?php echo esc_html( $user_name ); ?></h1>
					<p class="profile-user-email"><?php echo esc_html( $user_email ); ?></p>
					
					<?php if ( $bio ) : ?>
						<div class="profile-user-bio">
							<p><?php echo wp_kses_post( $bio ); ?></p>
						</div>
					<?php endif; ?>
				</div>
			</div>

			<!-- Right Section: Statistics -->
			<div class="profile-header-right">
				<div class="profile-stats-grid">
					<div class="stat-item">
						<div class="stat-number"><?php echo count( $enrolled_courses ); ?></div>
						<div class="stat-label"><?php _e( 'Courses', 'learnpress' ); ?></div>
					</div>
					<div class="stat-item">
						<div class="stat-number"><?php echo $completed_courses; ?></div>
						<div class="stat-label"><?php _e( 'Completed', 'learnpress' ); ?></div>
					</div>
					<div class="stat-item">
						<div class="stat-number"><?php echo $average_progress; ?>%</div>
						<div class="stat-label"><?php _e( 'Progress', 'learnpress' ); ?></div>
					</div>
					<div class="stat-item">
						<div class="stat-number"><?php echo $followers; ?></div>
						<div class="stat-label"><?php _e( 'Followers', 'learnpress' ); ?></div>
					</div>
				</div>
			</div>

		</div>
	</div>

	<!-- Navigation Tabs -->
	<div class="profile-header-navigation">
		<div class="profile-nav-container">
			<?php
			$tabs = $profile->get_tabs();
			$current_tab = $profile->get_current_tab();
			
			if ( $tabs && $tabs->tabs() ) :
			?>
				<ul class="profile-nav-tabs">
					<?php
					foreach ( $tabs->tabs() as $tab_key => $profile_tab ) {
						if ( ! is_object( $profile_tab ) || ! $profile_tab || $profile_tab->is_hidden() || ! $profile->current_user_can( 'view-tab-' . $tab_key ) ) {
							continue;
						}

						// Admin view another user profile - hide certain tabs
						if ( $profile->get_user()->get_id() !== $profile->get_user_current()->get_id() && current_user_can( 'administrator' ) ) {
							$tab_key_hidden_admin_view_user = [ 'settings', 'logout', 'orders', 'gradebook' ];
							if ( in_array( $tab_key, $tab_key_hidden_admin_view_user ) ) {
								continue;
							}
						}

						$tab_classes = array( 'profile-nav-tab' );
						if ( $profile->is_current_tab( $tab_key ) ) {
							$tab_classes[] = 'active';
						}

						$link = $profile->get_tab_link( $tab_key );
						?>
						<li class="<?php echo implode( ' ', $tab_classes ); ?>">
							<a href="<?php echo esc_url_raw( $link ); ?>" data-slug="<?php echo esc_attr( $tab_key ); ?>">
								<?php
								if ( ! empty( $profile_tab->get( 'icon' ) ) ) {
									echo wp_kses_post( str_replace( array( 'fas fa-', 'fa fa-' ), 'lp-icon-', $profile_tab->get( 'icon' ) ) );
								}
								?>
								<span class="tab-title"><?php echo apply_filters( 'learn_press_profile_' . $tab_key . '_tab_title', $profile_tab->get( 'title' ), $tab_key ); ?></span>
							</a>
						</li>
						<?php
					}
					?>
				</ul>
			<?php endif; ?>
		</div>
	</div>

</div>

<style>
/* Custom Profile Header Styles */
.custom-profile-header-wrapper {
	background: var(--primary-color, #003087);
	color: white;
	position: relative;
	margin-bottom: 30px;
	border-radius: 8px;
	overflow: hidden;
}

.profile-cover-image {
	height: 200px;
	background-size: cover;
	background-position: center;
	position: relative;
}

.cover-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 48, 135, 0.7);
}

.profile-header-main {
	padding: 30px 20px;
	position: relative;
}

.profile-header-container {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	max-width: 1200px;
	margin: 0 auto;
	gap: 30px;
}

.profile-header-left {
	display: flex;
	align-items: center;
	gap: 20px;
	flex: 1;
}

.profile-avatar-wrapper {
	position: relative;
}

.profile-avatar {
	width: 120px;
	height: 120px;
	border-radius: 50%;
	border: 4px solid white;
	object-fit: cover;
}

.edit-avatar-btn {
	position: absolute;
	bottom: 5px;
	right: 5px;
	background: var(--secondary-color, #007BFF);
	color: white;
	width: 30px;
	height: 30px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	text-decoration: none;
	font-size: 14px;
}

.profile-user-name {
	margin: 0 0 5px 0;
	font-size: 28px;
	font-weight: 600;
}

.profile-user-email {
	margin: 0 0 10px 0;
	opacity: 0.9;
	font-size: 16px;
}

.profile-user-bio {
	margin-top: 10px;
	opacity: 0.9;
	line-height: 1.5;
}

.profile-header-right {
	flex-shrink: 0;
}

.profile-stats-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20px;
	min-width: 200px;
}

.stat-item {
	text-align: center;
}

.stat-number {
	font-size: 24px;
	font-weight: 700;
	margin-bottom: 5px;
}

.stat-label {
	font-size: 12px;
	opacity: 0.8;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.profile-header-navigation {
	background: rgba(255, 255, 255, 0.1);
	border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.profile-nav-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
}

.profile-nav-tabs {
	display: flex;
	list-style: none;
	margin: 0;
	padding: 0;
	gap: 0;
}

.profile-nav-tab {
	margin: 0;
}

.profile-nav-tab a {
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 15px 20px;
	color: rgba(255, 255, 255, 0.8);
	text-decoration: none;
	border-bottom: 3px solid transparent;
	transition: all 0.3s ease;
}

.profile-nav-tab.active a,
.profile-nav-tab a:hover {
	color: white;
	border-bottom-color: var(--secondary-color, #007BFF);
	background: rgba(255, 255, 255, 0.1);
}

.tab-title {
	font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
	.profile-header-container {
		flex-direction: column;
		gap: 20px;
	}
	
	.profile-header-left {
		flex-direction: column;
		text-align: center;
	}
	
	.profile-stats-grid {
		grid-template-columns: repeat(4, 1fr);
		min-width: auto;
		width: 100%;
	}
	
	.profile-nav-tabs {
		flex-wrap: wrap;
	}
	
	.profile-nav-tab a {
		padding: 12px 15px;
		font-size: 14px;
	}
}

/* RTL Support */
[dir="rtl"] .profile-header-left {
	flex-direction: row-reverse;
}

[dir="rtl"] .profile-nav-tab a {
	flex-direction: row-reverse;
}
</style>
