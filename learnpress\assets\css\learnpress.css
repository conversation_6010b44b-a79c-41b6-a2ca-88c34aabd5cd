@charset "UTF-8";
:root {
  --lp-cotainer-max-with: var(--lp-container-max-width);
}

.wp-block-group {
  --lp-container-max-width: var(--wp--style--global--wide-size);
}

/**
 * Mixin
 */
@-webkit-keyframes rotating4 {
  from {
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes rotating4 {
  from {
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes animation4 {
  from {
    left: -40%;
    width: 40%;
  }
  to {
    left: 100%;
    width: 10%;
  }
}
@keyframes animation4 {
  from {
    left: -40%;
    width: 40%;
  }
  to {
    left: 100%;
    width: 10%;
  }
}
.learn-press-message {
  position: relative;
  margin: 24px auto;
  padding: 10px 20px;
  border-radius: var(--lp-border-radius, 5px);
  background-color: #E5F7FF;
  color: #007AFF;
  width: 100%;
}
.learn-press-message.error {
  background-color: #FEE5E5;
  color: #FF3B30;
}
.learn-press-message.warning {
  background-color: #FEF7E6;
  color: #FF9500;
}
.learn-press-message.success {
  background-color: #EBF8E5;
  color: #3AB500;
}
.learn-press-message.info {
  background-color: rgba(0, 122, 255, 0.1019607843);
  color: #007AFF;
}
.learn-press-message a {
  text-decoration: underline;
}

.lp-toast.toastify {
  background: #EBF8E5;
  color: #3AB500;
  border-radius: var(--lp-border-radius, 5px);
  box-shadow: 0 0 0;
  display: flex;
  align-items: center;
}
.lp-toast.toastify .toast-close {
  background: transparent !important;
  font-size: 0;
  padding-left: 12px;
}
.lp-toast.toastify .toast-close:before {
  content: "\f00d";
  font-family: "lp-icon";
  font-size: 16px;
  color: #000;
  line-height: 17px;
}
.lp-toast.toastify .toast-close:hover {
  opacity: 1;
}
.lp-toast.toastify.error {
  background-color: #FEE5E5;
  color: #FF3B30;
  padding: 12px 20px;
  border: none;
  margin: 0 auto;
}

@keyframes lp-rotating {
  from {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes lp-rotating {
  from {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.lp-loading-change {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.38);
  top: 0;
}

.lp-load-ajax-element {
  position: relative;
}

/**
* Styles for all page of LP
*
* @since 4.2.3
* @version 1.0.0
*/
/**
 * Mixin
 */
@-webkit-keyframes rotating4 {
  from {
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes rotating4 {
  from {
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes animation4 {
  from {
    left: -40%;
    width: 40%;
  }
  to {
    left: 100%;
    width: 10%;
  }
}
@keyframes animation4 {
  from {
    left: -40%;
    width: 40%;
  }
  to {
    left: 100%;
    width: 10%;
  }
}
:root {
  --lp-cotainer-max-with: var(--lp-container-max-width);
}

.wp-block-group {
  --lp-container-max-width: var(--wp--style--global--wide-size);
}

*, :after, :before {
  box-sizing: border-box;
}

/*  start reset css */
body {
  background: #fff;
}

button {
  cursor: pointer;
}

.learnpress-page input[type=text],
.learnpress-page input[type=email],
.learnpress-page input[type=number],
.learnpress-page input[type=password], .learnpress-page textarea {
  border-color: var(--lp-border-color, #E2E0DB);
  -webkit-border-radius: var(--lp-border-radius, 5px);
  -moz-border-radius: var(--lp-border-radius, 5px);
  border-radius: var(--lp-border-radius, 5px);
}
.learnpress-page input[type=text]:focus,
.learnpress-page input[type=email]:focus,
.learnpress-page input[type=number]:focus,
.learnpress-page input[type=password]:focus, .learnpress-page textarea:focus {
  outline: none;
  border-color: var(--lp-primary-color, #ffb606);
}
.learnpress-page .lp-button, .learnpress-page #lp-button {
  padding: 12px 24px;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  color: var(--lp-color-base, #333);
  background: transparent;
  box-shadow: unset;
  font-family: inherit;
  font-weight: 400;
  text-align: center;
  text-transform: capitalize;
  -webkit-border-radius: var(--lp-border-radius, 5px);
  -moz-border-radius: var(--lp-border-radius, 5px);
  border-radius: var(--lp-border-radius, 5px);
  -webkit-transition: all 0.25s;
  -moz-transition: all 0.25s;
  -ms-transition: all 0.25s;
  -o-transition: all 0.25s;
  transition: all 0.25s;
}
.learnpress-page .lp-button.large, .learnpress-page #lp-button.large {
  height: 52px;
  padding: 18px 30px;
  font-size: 1.1em;
}
.learnpress-page .lp-button:hover, .learnpress-page #lp-button:hover {
  border-color: var(--lp-primary-color);
  color: #fff;
  background: var(--lp-primary-color);
}
.learnpress-page .lp-button.btn-ajax-off .icon, .learnpress-page #lp-button.btn-ajax-off .icon {
  display: none;
}
.learnpress-page .lp-button.btn-ajax-on .icon, .learnpress-page #lp-button.btn-ajax-on .icon {
  display: inline-block;
  margin-right: 5px;
  -webkit-animation: lp-rotating 1s linear infinite;
  -moz-animation: lp-rotating 1s linear infinite;
  animation: lp-rotating 1s linear infinite;
}
.learnpress-page .lp-button:focus, .learnpress-page #lp-button:focus {
  outline: 0;
}
.learnpress-page .rwmb-field .description {
  margin-top: 8px;
  color: #999;
  font-size: smaller;
  font-style: italic;
}

input, button, select, textarea {
  outline: none;
}

/*html {
	overflow-x: hidden;
}*/
a {
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  text-decoration: none;
}

p {
  margin-bottom: 1rem;
}
p:last-child {
  margin: 0;
}

.lp-content-area {
  max-width: var(--lp-container-max-width) !important;
  margin: 0 auto;
  padding-right: var(--lp-cotainer-padding);
  padding-left: var(--lp-cotainer-padding);
}
.lp-content-area.learn-press-message {
  margin-bottom: 24px;
  padding-left: 15px;
  padding-right: 15px;
}

.lp-ico svg {
  width: 20px;
  height: 20px;
}

.lp-button {
  border: 1px solid var(--lp-border-color, #E2E0DB);
  border-radius: var(--lp-border-radius, 5px);
  padding: 8px 16px;
}
.lp-button.loading {
  pointer-events: none;
  opacity: 0.8;
}
.lp-button.loading:before {
  display: inline-block;
  font-family: "lp-icon";
  content: "\f110";
  animation: lp-rotating 1s linear infinite;
  margin-right: 5px;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  vertical-align: middle;
  margin-top: -2px;
}

.lp-hidden {
  display: none !important;
}

.course-price .origin-price {
  text-decoration: line-through;
  margin-right: 4px;
  font-size: calc(var(--lp-font-size-base, 1em) * 0.925);
  opacity: 0.6;
}

.learn-press-tabs {
  margin-bottom: 32px;
  position: relative;
  border-bottom: 1px solid var(--lp-border-color, #E2E0DB);
}
.learn-press-tabs .learn-press-tabs__checker {
  display: none;
}
.learn-press-tabs__nav {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  column-gap: 40px;
  row-gap: 12px;
  list-style: none;
  margin: 0 0 -1px 0 !important;
}
.learn-press-tabs__item {
  position: relative;
  margin: 0;
  list-style: none;
}
.learn-press-tabs__item > a {
  display: block;
  margin: 0;
  padding: 0 0 16px 0;
  font-size: 1.125em;
  line-height: 1;
  font-weight: 600;
  text-align: center;
  text-transform: capitalize;
  cursor: pointer;
  color: #333;
  text-decoration: none;
}
.learn-press-tabs__item > a:focus {
  outline: 0;
  text-decoration: none;
}
.learn-press-tabs__item::after {
  position: absolute;
  bottom: 0;
  top: auto;
  left: 0;
  width: 100%;
  height: 2px;
  background: transparent;
  content: "";
}
.learn-press-tabs__item.active a, .learn-press-tabs__item:hover a {
  color: var(--lp-primary-color, #ffb606);
}
.learn-press-tabs__item.active::after, .learn-press-tabs__item:hover::after {
  background: var(--lp-primary-color, #ffb606);
}
.learn-press-tabs.stretch .learn-press-tabs__tab {
  flex: 1;
}
.learn-press-tabs.stretch .learn-press-tabs__tab > label {
  padding: 18px 10px;
}
@media (max-width: 990px) {
  .learn-press-tabs__nav, .learn-press-tabs .learn-press-filters {
    overflow-x: auto;
    white-space: nowrap;
    flex-wrap: nowrap;
    scroll-behavior: smooth;
  }
  .learn-press-tabs__nav::-webkit-scrollbar, .learn-press-tabs .learn-press-filters::-webkit-scrollbar {
    display: none;
  }
}

.learn-press-filters {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  column-gap: 40px;
  row-gap: 12px;
  list-style: none;
  margin: 0 0 -1px 0 !important;
}
.learn-press-filters li {
  position: relative;
  margin: 0;
  list-style: none;
}
.learn-press-filters li a, .learn-press-filters li span {
  display: block;
  margin: 0;
  padding: 0 0 16px 0;
  font-size: 1.125em;
  line-height: 1;
  font-weight: 600;
  text-align: center;
  text-transform: capitalize;
  cursor: pointer;
  color: #333;
  text-decoration: none;
}
.learn-press-filters li a:focus, .learn-press-filters li span:focus {
  outline: 0;
  text-decoration: none;
}
.learn-press-filters li a::after, .learn-press-filters li span::after {
  position: absolute;
  bottom: 0;
  top: auto;
  left: 0;
  width: 100%;
  height: 2px;
  background: transparent;
  content: "";
}
.learn-press-filters li a.active, .learn-press-filters li a:hover, .learn-press-filters li span.active, .learn-press-filters li span:hover {
  color: var(--lp-primary-color, #ffb606);
}
.learn-press-filters li a.active::after, .learn-press-filters li a:hover::after, .learn-press-filters li span.active::after, .learn-press-filters li span:hover::after {
  background: var(--lp-primary-color, #ffb606);
}
.learn-press-filters li.active a, .learn-press-filters li.active span {
  color: var(--lp-primary-color, #ffb606);
}
.learn-press-filters li.active a::after, .learn-press-filters li.active span::after {
  background: var(--lp-primary-color, #ffb606);
}
.learn-press-filters li:after {
  display: none !important;
}

.wrapper-course-nav-tabs {
  position: relative;
  border-bottom: 1px solid var(--lp-border-color, #E2E0DB);
}

#learn-press-course-tabs.show-all .course-tab-panel {
  margin-bottom: 40px;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(1):checked ~ .course-tab-panels .course-tab-panel:nth-child(1) {
  display: block;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(1):checked ~ .learn-press-nav-tabs .course-nav:nth-child(1) {
  border-bottom: 0;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(1):checked ~ .learn-press-nav-tabs .course-nav:nth-child(1) label {
  color: var(--lp-primary-color);
  background: #fff;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(2):checked ~ .course-tab-panels .course-tab-panel:nth-child(2) {
  display: block;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(2):checked ~ .learn-press-nav-tabs .course-nav:nth-child(2) {
  border-bottom: 0;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(2):checked ~ .learn-press-nav-tabs .course-nav:nth-child(2) label {
  color: var(--lp-primary-color);
  background: #fff;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(3):checked ~ .course-tab-panels .course-tab-panel:nth-child(3) {
  display: block;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(3):checked ~ .learn-press-nav-tabs .course-nav:nth-child(3) {
  border-bottom: 0;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(3):checked ~ .learn-press-nav-tabs .course-nav:nth-child(3) label {
  color: var(--lp-primary-color);
  background: #fff;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(4):checked ~ .course-tab-panels .course-tab-panel:nth-child(4) {
  display: block;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(4):checked ~ .learn-press-nav-tabs .course-nav:nth-child(4) {
  border-bottom: 0;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(4):checked ~ .learn-press-nav-tabs .course-nav:nth-child(4) label {
  color: var(--lp-primary-color);
  background: #fff;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(5):checked ~ .course-tab-panels .course-tab-panel:nth-child(5) {
  display: block;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(5):checked ~ .learn-press-nav-tabs .course-nav:nth-child(5) {
  border-bottom: 0;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(5):checked ~ .learn-press-nav-tabs .course-nav:nth-child(5) label {
  color: var(--lp-primary-color);
  background: #fff;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(6):checked ~ .course-tab-panels .course-tab-panel:nth-child(6) {
  display: block;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(6):checked ~ .learn-press-nav-tabs .course-nav:nth-child(6) {
  border-bottom: 0;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(6):checked ~ .learn-press-nav-tabs .course-nav:nth-child(6) label {
  color: var(--lp-primary-color);
  background: #fff;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(7):checked ~ .course-tab-panels .course-tab-panel:nth-child(7) {
  display: block;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(7):checked ~ .learn-press-nav-tabs .course-nav:nth-child(7) {
  border-bottom: 0;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(7):checked ~ .learn-press-nav-tabs .course-nav:nth-child(7) label {
  color: var(--lp-primary-color);
  background: #fff;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(8):checked ~ .course-tab-panels .course-tab-panel:nth-child(8) {
  display: block;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(8):checked ~ .learn-press-nav-tabs .course-nav:nth-child(8) {
  border-bottom: 0;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(8):checked ~ .learn-press-nav-tabs .course-nav:nth-child(8) label {
  color: var(--lp-primary-color);
  background: #fff;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(9):checked ~ .course-tab-panels .course-tab-panel:nth-child(9) {
  display: block;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(9):checked ~ .learn-press-nav-tabs .course-nav:nth-child(9) {
  border-bottom: 0;
}
#learn-press-course-tabs input[name=learn-press-course-tab-radio]:nth-child(9):checked ~ .learn-press-nav-tabs .course-nav:nth-child(9) label {
  color: var(--lp-primary-color);
  background: #fff;
}

.course-tab-panels .course-tab-panel {
  padding-top: 30px;
}

.course-tab-panel {
  display: none;
}
.course-tab-panel.active {
  display: block;
}
.course-tab-panel .course-description h4 {
  margin-top: 0;
  margin-bottom: 1.125em;
}
.course-tab-panel .course-description img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}
.course-tab-panel .lp-course-author {
  display: flex;
  align-items: start;
}
@media (max-width: 767px) {
  .course-tab-panel .lp-course-author {
    flex-direction: column;
    align-items: center;
  }
}
.course-tab-panel .lp-course-author .course-author__pull-left {
  margin-right: 30px;
  text-align: center;
  align-items: center;
}
@media (max-width: 767px) {
  .course-tab-panel .lp-course-author .course-author__pull-left {
    margin-right: 0;
    margin-bottom: 24px;
  }
}
.course-tab-panel .lp-course-author img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  object-position: center;
  display: block;
  margin: 0 auto;
}
.course-tab-panel .lp-course-author .course-author__pull-right {
  flex: 1;
}
@media (max-width: 767px) {
  .course-tab-panel .lp-course-author .course-author__pull-right {
    width: 100%;
    text-align: center;
  }
}
.course-tab-panel .lp-course-author .author-title {
  margin-bottom: 4px;
}
.course-tab-panel .lp-course-author .author-title a {
  text-decoration: none;
  color: #333;
  box-shadow: none;
  font-size: 1.125em;
  font-weight: 500;
  text-transform: capitalize;
}
.course-tab-panel .lp-course-author .author-title a:hover {
  color: var(--lp-primary-color);
}
.course-tab-panel .lp-course-author .author-description {
  color: #666;
  font-style: italic;
}
.course-tab-panel .lp-course-author .instructor-social {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}
.course-tab-panel .lp-course-author .author-socials {
  display: grid;
  grid-template-columns: repeat(4, 33px);
  gap: 10px;
  justify-content: center;
  margin-top: 16px;
}
.course-tab-panel .lp-course-author .author-socials > a {
  display: inline-block;
  width: 33px;
  height: 33px;
  margin: 0;
  border: 1px solid #ededed;
  border-radius: 50%;
  color: #878787;
  box-shadow: none;
  font-size: 0.8em;
  line-height: 2em;
  text-align: center;
  vertical-align: middle;
  transition: all 0.3s;
}
.course-tab-panel .lp-course-author .author-socials > a:hover {
  border-color: var(--lp-primary-color);
  color: #fff;
  background: var(--lp-primary-color);
}
.course-tab-panel .lp-course-author .author-socials .fa-googleplus::before {
  content: "\f0d5";
}
.course-tab-panel .lp-course-curriculum__title {
  display: none;
}

.learn-press-nav-tabs {
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  column-gap: 40px;
  row-gap: 12px;
  list-style: none;
  margin: 0 0 -1px 0 !important;
}
.learn-press-nav-tabs li {
  position: relative;
  margin: 0;
  list-style: none;
}
.learn-press-nav-tabs li label {
  display: block;
  margin: 0;
  padding: 0 0 16px 0;
  font-size: 1.125em;
  line-height: 1;
  font-weight: 600;
  text-align: center;
  text-transform: capitalize;
  cursor: pointer;
  color: #333;
  text-decoration: none;
}
.learn-press-nav-tabs li label:focus {
  outline: 0;
  text-decoration: none;
}
.learn-press-nav-tabs li::after {
  position: absolute;
  bottom: 0;
  top: auto;
  left: 0;
  width: 100%;
  height: 2px;
  background: transparent;
  content: "";
}
.learn-press-nav-tabs li.active label, .learn-press-nav-tabs li:hover label {
  color: var(--lp-primary-color, #ffb606);
}
.learn-press-nav-tabs li.active::after, .learn-press-nav-tabs li:hover::after {
  background: var(--lp-primary-color, #ffb606);
}

.TabsDragScroll {
  position: relative;
}
.TabsDragScroll:hover {
  cursor: all-scroll;
}
.TabsDragScroll ul {
  max-width: 100%;
  white-space: nowrap;
  scroll-behavior: smooth;
  user-select: none;
  overflow-x: auto;
  flex-wrap: nowrap;
}
.TabsDragScroll ul::-webkit-scrollbar {
  display: none;
}
.TabsDragScroll ul.dragging {
  scroll-behavior: unset;
  cursor: all-scroll;
}

.lp-checkout-form__before,
.lp-checkout-form__after {
  width: 45%;
  width: -webkit-calc(50% - 30px);
  width: -moz-calc(50% - 30px);
  width: calc(50% - 30px);
  margin: 0 15px 40px 15px;
}
.lp-checkout-form__before .lp-checkout-block h4,
.lp-checkout-form__after .lp-checkout-block h4 {
  margin-top: 0;
  margin-bottom: 18px;
  color: #333;
  font-size: 1.5em;
  font-weight: 600;
  line-height: 1;
}
@media (max-width: 815px) {
  .lp-checkout-form__before,
  .lp-checkout-form__after {
    width: 100%;
    margin: 0 0 40px 0;
  }
}

.lp-checkout-form__before {
  float: right;
}
.lp-checkout-form__before #checkout-order {
  width: 100%;
}

.lp-checkout-form .lp-checkout-form__after {
  float: left;
}
.lp-checkout-form .lp-checkout-remember {
  letter-spacing: 0;
  margin-top: 0;
}
.lp-checkout-form .lp-checkout-remember label {
  width: auto;
  color: #666;
}
.lp-checkout-form .lp-checkout-remember label input[type=checkbox] {
  position: relative;
  top: 3px;
  width: 19px;
  height: 19px;
  margin: 0 4px 0 0;
  border: 1px solid #ccc;
  cursor: pointer;
  -webkit-appearance: none;
}
.lp-checkout-form .lp-checkout-remember label input[type=checkbox]:focus {
  outline: 0;
}
.lp-checkout-form .lp-checkout-remember label input[type=checkbox]:checked::after {
  position: absolute;
  top: 2px;
  left: 2px;
  color: #41abec;
  font-family: "lp-icon";
  font-size: 14px;
  line-height: 1;
  content: "\f00c";
  border: none;
  transform: none;
}
.lp-checkout-form .lp-checkout-remember a {
  color: #666;
  font-weight: 300;
  text-decoration: underline;
  text-decoration-color: #ccc;
}
.lp-checkout-form .lp-checkout-remember a:hover {
  color: var(--lp-primary-color);
  text-decoration-color: var(--lp-primary-color);
}
.lp-checkout-form .lp-form-fields {
  padding: 0 0 14px 0;
}
.lp-checkout-form .lp-form-fields label {
  display: block;
  margin-bottom: 10px;
}
.lp-checkout-form .lp-form-fields input:not([type=checkbox]) {
  line-height: normal;
  width: 100%;
  padding: 10px 15px;
  border: 1px solid #ccc;
}
.lp-checkout-form .lp-form-fields input:not([type=checkbox]):focus {
  border-color: var(--lp-primary-color);
  outline: 0;
}

.lp-checkout-form {
  margin: 0 -15px;
  margin-top: 50px;
}
@media (max-width: 815px) {
  .lp-checkout-form {
    display: flex;
    flex-direction: column-reverse;
    margin: 0;
  }
}
.lp-checkout-form #btn-checkout-account-switch-to-guest {
  margin-bottom: 60px;
}
.lp-checkout-form .description {
  float: left;
}
.lp-checkout-form a {
  color: #41abec;
}
.lp-checkout-form a label {
  display: inline-block;
  color: inherit;
  font-weight: normal;
  cursor: pointer;
}
.lp-checkout-form a:hover {
  color: #ffb606;
}
.lp-checkout-form #checkout-account-guest {
  width: 100%;
  margin-bottom: 35px;
  padding-bottom: 19px;
  border-bottom: 1px solid var(--lp-border-color, #E2E0DB);
}
.lp-checkout-form #checkout-account-guest .lp-form-fields,
.lp-checkout-form #checkout-account-guest .form-field {
  margin-bottom: 0;
  padding-bottom: 0;
}
.lp-checkout-form div.lp-guest-checkout-output {
  margin-top: 10px;
  padding: 8px 20px;
  border: 2px solid #41abec;
  border-radius: 4px;
  transition: all 0.2s ease;
}
.lp-checkout-form label.lp-guest-checkout-output {
  margin-top: 10px;
  cursor: pointer;
  user-select: none;
}
.lp-checkout-form .lp-guest-checkout-notice {
  margin: 30px 0 0 0;
}
.lp-checkout-form .lp-guest-switch-login {
  margin-top: 1em;
  color: #666;
  font-weight: 400;
}
.lp-checkout-form .lp-guest-switch-login a {
  display: inline-block;
}
.lp-checkout-form input[name=checkout-account-switch-form] {
  display: none;
}
.lp-checkout-form input[name=checkout-account-switch-form]:not(:checked) + .lp-checkout-block {
  display: none;
  overflow: hidden;
  height: 0;
  margin: 0;
  padding: 0;
  border: 0;
  opacity: 0;
}
.lp-checkout-form input[name=checkout-account-switch-form]:not(:checked) + .lp-checkout-block > * {
  overflow: hidden;
  height: 0;
}
.lp-checkout-form input[name=checkout-account-switch-form]:checked + .lp-checkout-block {
  display: block;
  overflow: auto;
  height: auto;
}
.lp-checkout-form input[name=checkout-account-switch-form][value=guest]:checked ~ #btn-checkout-account-switch-to-guest {
  display: none;
}
.lp-checkout-form::after {
  display: block;
  clear: both;
  content: "";
}
.lp-checkout-form:focus {
  outline: 0;
}

#checkout-order .lp-checkout-order__inner {
  padding: 0 20px;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  border-radius: 4px;
}
#checkout-order .cart-item:first-child td {
  border-top: 0;
}
#checkout-order td, #checkout-order th {
  border-right: 0;
  border-left: 0;
  background: transparent;
}
#checkout-order td:last-child, #checkout-order th:last-child {
  padding-right: 0;
}
#checkout-order table {
  width: 100%;
  margin: 0;
  border: 0;
}
#checkout-order .course-name {
  font-weight: 600;
}
#checkout-order .course-name a {
  display: -webkit-box;
  overflow: hidden;
  color: #000;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
#checkout-order .course-name a:hover {
  color: var(--lp-primary-color);
}
#checkout-order .col-number {
  min-width: 50px;
  color: #666;
  font-weight: 600;
  text-align: right;
}
#checkout-order tfoot {
  font-size: 1.2em;
}
#checkout-order tfoot th {
  font-weight: 600;
  padding-left: 0;
}
#checkout-order tfoot .order-total {
  font-size: 1.4em;
}
#checkout-order tfoot tr:last-child {
  border-width: 0;
}
#checkout-order tfoot tr:last-child td, #checkout-order tfoot tr:last-child th {
  border-width: 0;
}
#checkout-order .course-thumbnail {
  width: 80px;
  padding: 20px 13px 18px 0;
}
#checkout-order .course-thumbnail img {
  width: 100px;
  max-width: 100% !important;
  height: auto !important;
}

.order-comments {
  width: 100%;
  padding: 15px;
  min-height: 150px;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  resize: none;
}

#checkout-account-register,
#checkout-account-login {
  width: 100%;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--lp-border-color, #E2E0DB);
}
#checkout-account-register .form-field .rwmb-label,
#checkout-account-register .form-field .rwmb-input .description,
#checkout-account-login .form-field .rwmb-label,
#checkout-account-login .form-field .rwmb-input .description {
  display: none;
}
#checkout-account-register .lp-checkout-remember label,
#checkout-account-login .lp-checkout-remember label {
  display: inline-block;
  /*font-weight: normal;
           color: inherit;*/
}
#checkout-account-register .lp-checkout-remember a,
#checkout-account-login .lp-checkout-remember a {
  float: right;
}
#checkout-account-register .lp-checkout-sign-in-link,
#checkout-account-register .lp-checkout-sign-up-link,
#checkout-account-login .lp-checkout-sign-in-link,
#checkout-account-login .lp-checkout-sign-up-link {
  display: flex;
  margin: 0;
  color: #666;
  font-weight: 400;
}
#checkout-account-register .lp-checkout-sign-in-link a,
#checkout-account-register .lp-checkout-sign-up-link a,
#checkout-account-login .lp-checkout-sign-in-link a,
#checkout-account-login .lp-checkout-sign-up-link a {
  margin-left: 5px;
  color: #41abec;
  cursor: pointer;
}
#checkout-account-register .lp-checkout-sign-in-link a:hover,
#checkout-account-register .lp-checkout-sign-up-link a:hover,
#checkout-account-login .lp-checkout-sign-in-link a:hover,
#checkout-account-login .lp-checkout-sign-up-link a:hover {
  color: var(--lp-primary-color);
}
#checkout-account-register .lp-checkout-sign-in-link a label,
#checkout-account-register .lp-checkout-sign-up-link a label,
#checkout-account-login .lp-checkout-sign-in-link a label,
#checkout-account-login .lp-checkout-sign-up-link a label {
  display: inline-block;
  float: right;
  color: inherit;
  font-weight: normal;
  cursor: pointer;
}

#checkout-payment {
  width: 100%;
  margin-bottom: 25px;
}
#checkout-payment h4 {
  margin-bottom: 24px;
}
#checkout-payment .secure-connection {
  margin-top: 5px;
  opacity: 0.5;
  font-size: 0.75em;
  font-weight: normal;
  margin-left: 10px;
}
#checkout-payment .secure-connection i {
  margin-right: 5px;
  font-size: 1.125em;
}
@media (max-width: 767px) {
  #checkout-payment .secure-connection {
    margin-top: 0;
  }
}
#checkout-payment .payment-methods {
  margin: 0 0 24px 0;
  padding: 0;
  list-style: none;
}
#checkout-payment .lp-payment-method {
  position: relative;
  margin: 0;
}
#checkout-payment .lp-payment-method > label {
  display: flex;
  align-items: center;
}
#checkout-payment .lp-payment-method .gateway-input,
#checkout-payment .lp-payment-method .gateway-icon {
  vertical-align: middle;
}
#checkout-payment .lp-payment-method .gateway-icon {
  max-height: 32px;
}
#checkout-payment .lp-payment-method .gateway-input {
  position: relative;
  width: 20px;
  height: 20px;
  margin: 0 8px 0 0;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  background: #fff;
  -webkit-appearance: none;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
}
#checkout-payment .lp-payment-method .gateway-input::before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6px;
  height: 6px;
  background: #41abec;
  transform: translate(-50%, -50%);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
}
#checkout-payment .lp-payment-method .gateway-input:checked::before {
  content: "";
}
#checkout-payment .lp-payment-method .gateway-input:focus {
  outline: 0;
}
#checkout-payment #checkout-order-action button {
  width: 100%;
  border-color: var(--lp-primary-color);
  color: #fff;
  background: var(--lp-primary-color);
  font-weight: 500;
}
#checkout-payment #checkout-order-action button:hover {
  border-color: var(--lp-primary-color);
  opacity: 0.5;
  background: var(--lp-primary-color);
}

.lp-terms-and-conditions {
  color: #666;
}
.lp-terms-and-conditions a {
  color: #41abec;
}
.lp-terms-and-conditions a:hover {
  color: var(--lp-primary-color);
}

.learn-press-checkout.guest-checkout {
  display: none;
}

.button-continue-guest-checkout {
  clear: both;
}

#learn-press-checkout .payment-methods {
  margin: 0;
  padding: 0;
  list-style: none;
}
#learn-press-checkout .payment-methods .lp-payment-method {
  margin-bottom: 20px;
}
#learn-press-checkout .payment-methods .lp-payment-method > label {
  display: flex;
  flex-flow: row nowrap;
  padding: 10px 20px;
  background: #f5f5f5;
  line-height: 2.5rem;
  cursor: pointer;
}
#learn-press-checkout .payment-methods .lp-payment-method > label img {
  vertical-align: middle;
}
#learn-press-checkout .payment-methods .lp-payment-method.selected > label {
  background: #d4d4d4;
}
#learn-press-checkout .payment-methods .payment-method-form {
  display: none;
  padding: 15px 20px;
  border-top: 1px solid var(--lp-border-color, #E2E0DB);
  background: #f9f9f9;
}

#learn-press-checkout-login,
#learn-press-checkout-register {
  margin-bottom: 1.5em;
  padding: 20px 20px 0 20px;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  background: #fff;
}

#learn-press-order-review,
.learn-press-checkout-comment {
  margin-bottom: 20px;
}

#checkout-form-login,
#checkout-form-register {
  _display: none;
}
#checkout-form-login .learn-press-form-register,
#checkout-form-login .learn-press-form-login,
#checkout-form-register .learn-press-form-register,
#checkout-form-register .learn-press-form-login {
  display: none;
}

#checkout-guest-email {
  margin: 0 0 20px 0;
}
#checkout-guest-email .form-heading {
  margin: 0;
}
#checkout-guest-email #checkout-guest-options {
  margin: 0;
  list-style: none;
}
#checkout-guest-email #checkout-existing-account,
#checkout-guest-email #checkout-new-account {
  display: none;
  margin: 0;
}
#checkout-guest-email.email-exists #checkout-existing-account {
  display: block;
}
#checkout-guest-email.email-exists #checkout-new-account {
  display: none;
}

.learn-press-checkout-comment h4 {
  display: none;
}

.lp-content-area .order_details {
  width: 100%;
  border-collapse: collapse;
}
.lp-content-area .order_details th a, .lp-content-area .order_details td a {
  color: var(--lp-primary-color);
}

.learnpress-checkout .learnpress > .learn-press-message {
  max-width: var(--lp-container-max-width);
  margin: 0 auto;
}

input[type=text],
input[type=email],
input[type=number],
input[type=password] {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  box-shadow: unset;
}

.learnpress table {
  width: 100%;
  margin: 0 auto 1em auto;
  border-spacing: 0;
  border-collapse: collapse;
}
.learnpress table th, .learnpress table td {
  padding: 0.7em 1em;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  background: #fff;
  text-align: left;
}
.learnpress table thead {
  box-sizing: border-box;
  border: 1px solid var(--lp-border-color, #E2E0DB);
}
.learnpress table thead th {
  border-bottom: none;
  background: var(--lp-white-grey, #F7F7FB);
  font-size: 1.1em;
  font-weight: 600;
}
.learnpress table tbody, .learnpress table tfoot {
  box-sizing: border-box;
}
.learnpress table tbody td, .learnpress table tbody th, .learnpress table tfoot td, .learnpress table tfoot th {
  border: 1px solid var(--lp-border-color, #E2E0DB);
  line-height: 1.4;
}
.learnpress table tbody td a, .learnpress table tbody th a, .learnpress table tfoot td a, .learnpress table tfoot th a {
  color: inherit;
  text-decoration: none;
}
.learnpress table tbody td a:hover, .learnpress table tbody th a:hover, .learnpress table tfoot td a:hover, .learnpress table tfoot th a:hover {
  color: var(--lp-primary-color);
}
.learnpress table tbody tr .column-status .result-percent, .learnpress table tfoot tr .column-status .result-percent {
  font-weight: 500;
}
.learnpress table tbody tr .column-status .lp-label, .learnpress table tfoot tr .column-status .lp-label {
  font-weight: 600;
}
.learnpress table tbody tr:nth-child(even) td, .learnpress table tfoot tr:nth-child(even) td {
  background: var(--tb-even-color, #fafafa);
}
.learnpress table .list-table-nav td {
  font-size: 0.875em;
}
.learnpress table .list-table-nav td.nav-text {
  text-align: left;
}
.learnpress table .list-table-nav td.nav-pages {
  text-align: right;
}
.learnpress table .list-table-nav td.nav-pages .learn-press-pagination {
  text-align: right;
}
.learnpress table .list-table-nav td.nav-pages .page-numbers {
  margin-bottom: 0;
}

.lp-label {
  display: inline-block;
  color: #666;
  font-weight: 300;
}

.learn-press-form .form-fields {
  margin: 0;
  padding: 0;
  list-style: none;
}
.lp-profile-content .learn-press-form .form-fields {
  margin: 0 -15px !important;
}
.lp-profile-content .learn-press-form .form-fields .form-field {
  padding-left: 15px;
  padding-right: 15px;
}
@media (min-width: 767px) {
  .lp-profile-content .learn-press-form .form-fields .form-field__50 {
    width: 50%;
    float: left;
  }
}
.learn-press-form .form-fields .form-field {
  margin: 0 0 20px 0;
}
.learn-press-form .form-fields .form-field > label {
  font-style: italic;
}
.learn-press-form .form-fields .form-field label {
  display: block;
  font-size: calc(var(--lp-font-size-base, 1em) * 0.86);
  font-weight: 700;
  margin: 0 0 10px 0;
}
.learn-press-form .form-fields .form-field input[type=email],
.learn-press-form .form-fields .form-field input[type=text],
.learn-press-form .form-fields .form-field input[type=password],
.learn-press-form .form-fields .form-field input[type=tel],
.learn-press-form .form-fields .form-field input[type=url],
.learn-press-form .form-fields .form-field input[type=number],
.learn-press-form .form-fields .form-field textarea {
  width: 100%;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  padding: 8px 16px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.learn-press-form .form-fields .form-field input[type=email]:focus,
.learn-press-form .form-fields .form-field input[type=text]:focus,
.learn-press-form .form-fields .form-field input[type=password]:focus,
.learn-press-form .form-fields .form-field input[type=tel]:focus,
.learn-press-form .form-fields .form-field input[type=url]:focus,
.learn-press-form .form-fields .form-field input[type=number]:focus,
.learn-press-form .form-fields .form-field textarea:focus {
  border-color: var(--lp-primary-color, #ffb606);
  outline: none;
}
.learn-press-form .form-fields .form-field .description {
  margin-top: 10px;
  font-size: 0.875em;
  font-style: italic;
  line-height: 1.4;
}
.learn-press-form .form-fields .form-field .asterisk, .learn-press-form .form-fields .form-field .required {
  color: #f00;
}
.learn-press-form.completed button::before {
  margin-right: 10px;
  font-family: "lp-icon";
  font-size: 1.125em;
  content: "\f00c";
}
.learn-press-form .form-field__clear {
  clear: both;
}
.learn-press-form form > p > label {
  display: flex;
  gap: 8px;
}
.learn-press-form p {
  margin: 0;
}

.learn-press-form-login,
.learn-press-form-register {
  max-width: 600px;
  margin-right: auto;
  margin-bottom: 60px;
  margin-left: auto;
  padding: 40px;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  border-radius: var(--lp-border-radius, 5px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
.learn-press-form-login h3,
.learn-press-form-register h3 {
  margin-bottom: 20px;
}
.learn-press-form-login button[type=submit],
.learn-press-form-register button[type=submit] {
  padding: 12px 16px;
  width: 100%;
  border-radius: var(--lp-border-radius, 5px);
  outline: none;
  color: #fff;
  background: var(--lp-primary-color);
}

.required label {
  font-weight: bold;
}
.required label:after {
  content: " *";
  display: inline;
}

.lp-password-input {
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  width: 100%;
}
.lp-password-input input[type=password] {
  padding-right: 2.5em;
}
.lp-password-input input::-ms-reveal {
  display: none;
}
.lp-password-input .lp-show-password-input {
  position: absolute;
  top: 8px;
  right: 10px;
  cursor: pointer;
}
.lp-password-input .lp-show-password-input::after {
  font-family: "lp-icon";
  content: "\f06e";
}
.lp-password-input .lp-show-password-input.display-password::after {
  color: #585858;
  content: "\f070";
}

.form-desc {
  font-size: smaller;
  font-style: italic;
}

.become-teacher-form {
  width: 90%;
  max-width: 500px;
  margin: 0 auto;
}
.become-teacher-form .form-field input[type=text],
.become-teacher-form .form-field input[type=email],
.become-teacher-form .form-field input[type=number],
.become-teacher-form .form-field input[type=password] {
  width: 100%;
}
.become-teacher-form .become-teacher-form__description {
  margin-top: 12px;
}
.become-teacher-form button {
  padding: 12px 24px;
}

.btn-base {
  border-color: var(--lp-primary-color);
  color: white;
  background-color: var(--lp-primary-color);
}

/**
 * Style for user cover amd avatar image.
 * @since *******
 * @version 1.0.0
 */
.lp-user-cover-image_background {
  position: relative;
}
.lp-user-cover-image_background:hover .lp-btn-to-edit-cover-image {
  opacity: 1;
  visibility: visible;
}
.lp-user-cover-image_background .lp-btn-to-edit-cover-image {
  position: absolute;
  bottom: 0;
  right: 0;
  background: var(--lp-white-grey, #F7F7FB);
  padding: 12px 20px;
  border-radius: var(--lp-border-radius, 5px) 0 var(--lp-border-radius, 5px) 0;
  text-transform: capitalize;
  cursor: pointer;
  color: var(--lp-primary-color, #ffb606);
  text-decoration: none;
  opacity: 0;
  visibility: hidden;
}
@media (max-width: 767px) {
  .lp-user-cover-image_background .lp-btn-to-edit-cover-image {
    font-size: 0;
    padding: 4px 12px;
    opacity: 1;
    visibility: visible;
  }
  .lp-user-cover-image_background .lp-btn-to-edit-cover-image:before {
    font-family: "lp-icon";
    content: "\f044";
    font-size: 16px;
  }
}

.lp-user-cover-image__display {
  width: 100%;
}
@media (max-width: 767px) {
  .lp-user-cover-image {
    position: relative;
    z-index: 1;
  }
}
.lp-user-cover-image .lp-cover-image-empty {
  display: flex;
  align-items: center;
  width: 100%;
  height: 250px;
  border: 2px dashed var(--lp-border-color, #E2E0DB);
  border-radius: var(--lp-border-radius, 5px);
  cursor: pointer;
  position: relative;
  text-align: center;
}
.lp-user-cover-image .lp-cover-image-empty:hover {
  border-color: var(--lp-primary-color, #ffb606);
}
.lp-user-cover-image .lp-cover-image-empty input[type=file] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
  z-index: 10;
}
.lp-user-cover-image .lp-cover-image-empty .lp-cover-image-empty__info {
  z-index: 1;
  flex: 1;
  position: relative;
  padding: 0 16px;
  line-height: 1.3;
}
.lp-user-cover-image .lp-cover-image-empty .lp-cover-image-empty__info__top {
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  flex-direction: column;
}
.lp-user-cover-image .lp-cover-image-empty .lp-cover-image-empty__info__top .lp-icon-file-image {
  font-size: 32px;
  transform: rotate(-90deg);
}
.lp-user-cover-image .lp-cover-image-empty .lp-cover-image-empty__info__top .lp-icon-file-image:before {
  color: var(--lp-primary-color);
  content: "\f08b";
}
.lp-user-cover-image .lp-cover-image-empty .lp-cover-image-empty__info__bottom {
  opacity: 0.7;
}
@media (max-width: 420px) {
  .lp-user-cover-image .lp-cover-image-empty {
    height: 200px;
  }
  .lp-user-cover-image .lp-cover-image-empty .lp-cover-image-empty__info {
    padding: 0 8px;
    font-size: 15px;
  }
}
.lp-user-cover-image .lp-user-cover-image__buttons {
  display: inline-flex;
  gap: 12px;
  margin-top: 16px;
}

.lp-user-cover-image_background {
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  margin-bottom: 20px;
  border-radius: var(--lp-border-radius, 5px);
  min-height: 100px;
}
.lp-user-cover-image_background > img {
  opacity: 0;
  visibility: hidden;
}

.learnpress_avatar__form label {
  display: inline-block;
}
.learnpress_avatar__form input[type=file] {
  display: none;
}
.learnpress_avatar__form__upload {
  display: flex;
  width: 200px;
  height: 200px;
  border: 1px dashed var(--lp-border-color, #E2E0DB);
  border-radius: 3px;
  background-color: #fafafa;
  font-size: 0.875em;
  font-weight: 300;
  font-style: italic;
  line-height: 2.6875em;
  text-align: center;
  cursor: pointer;
  align-items: center;
  justify-content: center;
}
.learnpress_avatar__form__upload div {
  line-height: 1.4;
}
.learnpress_avatar__button--loading:before {
  display: inline-block;
  font-family: "lp-icon";
  content: "\f110";
  animation: lp-rotating 1s linear infinite;
  margin-right: 5px;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  vertical-align: middle;
  margin-top: -2px;
}

button.learnpress_avatar__button {
  height: 40px;
  padding: 0 15px;
  border: 0;
  background: var(--lp-primary-color);
  color: white;
  margin-top: 15px;
  border-radius: 3px;
}
button.learnpress_avatar__button + button {
  margin-left: 15px;
  margin-right: 0;
}

.dashboard-statistic__row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}
.dashboard-statistic__row .statistic-box {
  padding: 20px;
  background: var(--lp-white-grey, #F7F7FB);
  text-align: center;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  border-radius: var(--lp-border-radius, 5px);
  width: calc((100% - 40px) / 3);
}
@media (min-width: 1200px) and (max-width: 1440px) {
  .dashboard-statistic__row .statistic-box {
    gap: 16px;
    padding: 16px;
  }
}
@media (max-width: 1200px) {
  .dashboard-statistic__row .statistic-box {
    width: calc((100% - 20px) / 2);
  }
}
@media (max-width: 768px) {
  .dashboard-statistic__row .statistic-box {
    gap: 16px;
    padding: 16px;
  }
}
@media (max-width: 600px) {
  .dashboard-statistic__row {
    gap: 8px;
  }
  .dashboard-statistic__row .statistic-box {
    width: 100%;
    gap: 12px;
    padding: 8px 12px;
  }
  .dashboard-statistic__row .statistic-box__text {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .dashboard-statistic__row .statistic-box__text label {
    margin: 0;
  }
  .dashboard-statistic__row .statistic-box__text__number {
    font-size: 1.2em;
  }
  .dashboard-statistic__row .statistic-box__icon span {
    --lp-width-icon: 40px;
    font-size: 18px;
  }
}

.statistic-box__icon span {
  --lp-width-icon: 60px;
  display: flex;
  width: var(--lp-width-icon);
  height: var(--lp-width-icon);
  border-radius: 50%;
  background: rgba(0, 199, 190, 0.1019607843);
  line-height: var(--lp-width-icon);
  align-items: center;
  justify-content: center;
  color: #00C7BE;
  font-size: 24px;
}
.statistic-box__icon [class^=lp-icon-]:before {
  width: 1em;
}
.statistic-box__icon .lp-icon-enrolled_courses:before, .statistic-box__icon .lp-icon-total_course:before {
  content: "\f518";
}
.statistic-box__icon .lp-icon-in_progress_course, .statistic-box__icon .lp-icon-student_in_progress {
  background: rgba(175, 82, 222, 0.1019607843);
  color: #AF52DE;
}
.statistic-box__icon .lp-icon-finished_courses, .statistic-box__icon .lp-icon-published_course {
  background: rgba(88, 86, 214, 0.1019607843);
  color: #5856D6;
}
.statistic-box__icon .lp-icon-passed_courses, .statistic-box__icon .lp-icon-student_completed {
  background: rgba(85, 190, 36, 0.2);
  color: #34C759;
}
.statistic-box__icon .lp-icon-failed_courses, .statistic-box__icon .lp-icon-pending_course {
  background: rgba(255, 59, 48, 0.1019607843);
  color: #FF3B30;
}
.statistic-box__icon .lp-icon-total_student {
  background: rgba(0, 122, 255, 0.1019607843);
  color: #007AFF;
}
.statistic-box__icon .lp-icon-total_student:before {
  content: "\f501";
}
.statistic-box__text {
  flex: 1;
  text-align: left;
}
.statistic-box__text label {
  display: block;
  margin-bottom: 4px;
  font-weight: normal;
  line-height: 1.3;
  font-size: calc(var(--lp-font-size-base, 1em) * 0.925);
}
.statistic-box__text__number {
  font-size: 1.6em;
  line-height: 1;
  font-weight: 600;
}

.wrapper-profile-header {
  position: relative;
}

.lp-user-profile {
  position: relative;
  --lp-item-padding: 40px;
}
.lp-user-profile img {
  max-width: 100%;
  height: auto;
}
.lp-user-profile .lp-user-profile-avatar img {
  display: block;
  border-radius: var(--lp-border-radius-avatar, 50%);
  width: 100%;
  height: auto;
}
.lp-user-profile .lp-user-profile-socials {
  display: flex;
  position: relative;
  margin: 0;
  gap: 12px;
}
.lp-user-profile .lp-user-profile-socials a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  color: var(--lp-color-base, #333);
  width: var(--lp-social-size, 40px);
  height: var(--lp-social-size, 40px);
  border-radius: 50%;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  font-size: 16px;
}
.lp-user-profile .lp-user-profile-socials a:hover {
  color: var(--lp-color-white, #fff);
  border-color: var(--lp-primary-color);
  background: var(--lp-primary-color);
}
.lp-user-profile .lp-user-profile-socials a:hover svg {
  fill: var(--lp-color-white, #fff);
}
.lp-user-profile .lp-profile-content-area {
  position: relative;
  padding: 0;
  display: flex;
  align-items: flex-start;
}
.lp-user-profile .lp-profile-left {
  min-width: 120px;
  max-width: 120px;
}
.lp-user-profile .lp-profile-left .user-avatar {
  position: relative;
}
.lp-user-profile .lp-profile-left .user-avatar img {
  border-radius: 50%;
}
.lp-user-profile .lp-profile-left .user-avatar:hover .lp-btn-to-edit-avatar {
  opacity: 1;
  visibility: visible;
}
.lp-user-profile .lp-profile-left .user-avatar .lp-btn-to-edit-avatar {
  position: absolute;
  top: 80px;
  right: 0;
  left: auto;
  width: 36px;
  height: 36px;
  font-size: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  cursor: pointer;
  background-color: var(--lp-white-grey, #F7F7FB);
  border-radius: 50%;
}
@media (max-width: 767px) {
  .lp-user-profile .lp-profile-left .user-avatar .lp-btn-to-edit-avatar {
    opacity: 1;
    top: 60px;
    visibility: visible;
  }
}
.lp-user-profile .lp-profile-left .user-avatar .lp-btn-to-edit-avatar::before {
  content: "\f044";
  font-size: 1.5rem;
  font-family: "lp-icon";
  font-weight: normal;
  color: var(--lp-primary-color);
}
.lp-user-profile .lp-profile-right {
  padding-left: 20px;
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}
.lp-user-profile .lp-profile-username {
  margin: 0;
  font-size: calc(var(--lp-font-size-base, 1em) * 1.25);
  font-weight: 500;
  text-transform: capitalize;
  color: inherit;
  flex: 1;
}
.lp-user-profile .lp-profile-user-bio {
  width: 100%;
}
.lp-user-profile #profile-sidebar {
  float: left;
  width: 270px;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  border-top: 0;
  margin-top: var(--lp-item-padding);
  margin-bottom: 32px;
}
@media (max-width: 990px) {
  .lp-user-profile #profile-sidebar {
    width: 100%;
    margin-bottom: 0;
  }
}
.lp-user-profile .lp-profile-nav-tabs {
  margin: 0;
  padding: 0;
  list-style: none;
}
.lp-user-profile .lp-profile-nav-tabs > li {
  position: relative;
  margin: 0;
  padding: 0;
  border-top: 1px solid var(--lp-border-color, #E2E0DB);
}
.lp-user-profile .lp-profile-nav-tabs > li a {
  padding: 10px 20px;
  color: inherit;
  font-weight: 500;
  text-decoration: none;
  display: flex;
  width: 100%;
  gap: 8px;
  line-height: 1.4;
}
.lp-user-profile .lp-profile-nav-tabs > li a > i {
  margin-top: 3px;
}
.lp-user-profile .lp-profile-nav-tabs > li > a {
  padding: 14px 20px;
  position: relative;
}
.lp-user-profile .lp-profile-nav-tabs > li > a > i {
  color: var(--lp-primary-color);
}
.lp-user-profile .lp-profile-nav-tabs > li ul li {
  margin: 0;
}
.lp-user-profile .lp-profile-nav-tabs > li ul li a {
  border-bottom: 1px solid var(--lp-border-color, #E2E0DB);
  font-size: calc(var(--lp-font-size-base, 1em) * 0.925);
}
.lp-user-profile .lp-profile-nav-tabs > li ul li a:hover {
  color: var(--lp-primary-color);
}
.lp-user-profile .lp-profile-nav-tabs > li.active {
  background: var(--lp-primary-color);
}
.lp-user-profile .lp-profile-nav-tabs > li.active > a, .lp-user-profile .lp-profile-nav-tabs > li.active > a i {
  color: #fff;
}
.lp-user-profile .lp-profile-nav-tabs > li:not(.active):hover {
  background: var(--lp-white-grey, #F7F7FB);
}
.lp-user-profile .lp-profile-nav-tabs > li.has-child > a:after {
  font-family: "lp-icon";
  content: "\f105";
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
}
.lp-user-profile .lp-profile-nav-tabs li > ul {
  display: none;
  position: absolute;
  z-index: 1000;
  top: 0;
  left: 100%;
  min-width: 180px;
  margin: 0;
  padding: 0;
  background: #fff;
  box-shadow: 0 15px 20px 0 rgba(0, 0, 0, 0.05);
  list-style: none;
}
.lp-user-profile .lp-profile-nav-tabs li > ul li {
  white-space: nowrap;
}
.lp-user-profile .lp-profile-nav-tabs li > ul li:hover, .lp-user-profile .lp-profile-nav-tabs li > ul li.active {
  color: var(--lp-primary-color);
}
.lp-user-profile .lp-profile-nav-tabs li:hover > ul {
  display: block;
}
.lp-user-profile .lp-profile-content {
  float: right;
  width: calc(100% - 300px);
  margin-bottom: 32px;
  padding-top: var(--lp-item-padding);
}
@media (max-width: 990px) {
  .lp-user-profile .lp-profile-content {
    width: 100%;
  }
}
@media (max-width: 767px) {
  .lp-user-profile .lp-profile-content .profile-orders {
    overflow-x: scroll;
  }
}
.lp-user-profile .lp-profile-content .course-categories, .lp-user-profile .lp-profile-content .course-instructor {
  margin-bottom: 14px;
}
.lp-user-profile .lp-profile-content .course-categories {
  padding: 0;
}
.lp-user-profile .lp-profile-content #profile-subtab-quiz-content .inline-form {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.lp-user-profile .lp-profile-content #profile-subtab-quiz-content .inline-form input[type=number] {
  padding: 8px;
  border: 1px solid #ccc;
  width: 60px;
}
.lp-user-profile .lp-profile-content #profile-subtab-quiz-content .inline-form button {
  cursor: pointer;
  line-height: 1;
}
.lp-user-profile .profile-orders .profile-heading {
  display: none;
}
.lp-user-profile .profile-orders table {
  margin-top: 0;
}
.lp-user-profile .profile-orders .column-order-actions a {
  margin-right: 6px;
  color: black;
  text-decoration: underline;
  font-weight: 500;
}
.lp-user-profile .profile-orders .column-order-actions a:hover, .lp-user-profile .profile-orders .column-order-actions a:focus {
  color: var(--lp-primary-color);
}
.lp-user-profile .learn-press-message {
  margin-top: 0;
}
.lp-user-profile .profile-heading {
  margin-bottom: 24px;
}
.lp-user-profile.guest .lp-content-area {
  display: flex;
  align-items: flex-start;
}
.lp-user-profile.guest .lp-content-area > div {
  flex: 1;
  width: 100%;
}
@media (min-width: 1366px) {
  .lp-user-profile .lp-content-area {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (max-width: 990px) {
  .lp-user-profile {
    --lp-item-padding: 30px;
  }
  .lp-user-profile #profile-nav {
    border-top: 1px solid var(--lp-border-color, #E2E0DB);
  }
  .lp-user-profile .lp-profile-nav-tabs {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-start;
    overflow-x: auto;
    white-space: nowrap;
    scroll-behavior: smooth;
  }
  .lp-user-profile .lp-profile-nav-tabs::after {
    display: none;
  }
  .lp-user-profile .lp-profile-nav-tabs > li {
    border: none;
    border-right: 1px solid var(--lp-border-color, #E2E0DB);
    flex: 1;
  }
  .lp-user-profile .lp-profile-nav-tabs > li:last-child {
    border-right: none;
  }
  .lp-user-profile .lp-profile-nav-tabs > li > a {
    height: auto;
    padding: 8px 12px 8px 12px;
    flex-direction: column;
    white-space: nowrap;
    align-items: center;
    font-size: calc(var(--lp-font-size-base, 1em) * 0.86);
    line-height: 1.4;
    gap: 4px;
  }
  .lp-user-profile .lp-profile-nav-tabs > li > a > i {
    position: static;
    transform: translate(0);
  }
  .lp-user-profile .lp-profile-nav-tabs > li > a::after {
    display: none !important;
    margin-left: 10px;
  }
  .lp-user-profile .lp-profile-nav-tabs > li.has-child a::after {
    display: none !important;
  }
  .lp-user-profile .lp-profile-nav-tabs > li:after {
    display: none;
  }
  .lp-user-profile .lp-profile-nav-tabs > li.active:after, .lp-user-profile .lp-profile-nav-tabs > li.active:before {
    display: none;
  }
  .lp-user-profile .lp-profile-nav-tabs > li.active.has-child ul {
    display: none;
    width: max-content;
    transform: translateX(-20%);
  }
  .lp-user-profile .lp-profile-nav-tabs > li.active.has-child ul::after {
    display: table;
    clear: both;
    box-shadow: 0 0 0;
    content: "";
  }
  .lp-user-profile .lp-profile-nav-tabs > li.active.has-child ul li {
    float: left;
    border-right: 1px solid var(--lp-border-color, #E2E0DB);
  }
  .lp-user-profile .lp-profile-nav-tabs > li.active.has-child ul li:last-child {
    border-right: none;
  }
  .lp-user-profile .lp-profile-nav-tabs > li.active.has-child ul li a {
    padding: 0 10px;
  }
  .lp-user-profile .lp-profile-nav-tabs li:not(.has-child) > a::after, .lp-user-profile .lp-profile-nav-tabs li:hover ul {
    display: none !important;
  }
}
@media (max-width: 768px) {
  .lp-user-profile .lp-profile-content-area {
    align-items: center;
  }
  .lp-user-profile .lp-profile-right {
    flex-direction: column;
    align-items: flex-start;
  }
  .lp-user-profile .lp-profile-user-bio {
    display: none;
  }
  .lp-user-profile.guest .lp-content-area {
    flex-direction: column;
  }
}
@media (max-width: 650px) {
  .lp-user-profile .lp-profile-left {
    min-width: 100px;
    max-width: 100px;
  }
  .lp-user-profile .lp-user-profile-socials {
    gap: 8px;
    --lp-social-size: 36px;
    font-size: 14px;
  }
}

#profile-content-withdrawals > h2 {
  font-size: 1.6em;
  margin-bottom: 12px;
}

.learn-press-profile-course__statistic {
  margin-bottom: 32px;
}
.learn-press-profile-course__progress .lp_profile_course_progress__item img {
  width: 100%;
  aspect-ratio: 16/9;
  object-fit: cover;
  height: auto;
  display: block;
}
.learn-press-profile-course__progress .lp_profile_course_progress__item td, .learn-press-profile-course__progress .lp_profile_course_progress__item th {
  text-align: center;
}
.learn-press-profile-course__progress .lp_profile_course_progress__item td:first-child, .learn-press-profile-course__progress .lp_profile_course_progress__item th:first-child {
  width: 15%;
  padding: 0;
}
@media (max-width: 768px) {
  .learn-press-profile-course__progress .lp_profile_course_progress__item td:first-child, .learn-press-profile-course__progress .lp_profile_course_progress__item th:first-child {
    display: none;
  }
}
.learn-press-profile-course__progress .lp_profile_course_progress__item td:nth-child(2), .learn-press-profile-course__progress .lp_profile_course_progress__item th:nth-child(2) {
  width: 25%;
}
.learn-press-profile-course__progress .lp_profile_course_progress__item td:nth-child(2) a, .learn-press-profile-course__progress .lp_profile_course_progress__item th:nth-child(2) a {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.learn-press-profile-course__progress .lp_profile_course_progress__item td:nth-child(3), .learn-press-profile-course__progress .lp_profile_course_progress__item th:nth-child(3) {
  width: 10%;
}
.learn-press-profile-course__progress .lp_profile_course_progress__item td:nth-child(4), .learn-press-profile-course__progress .lp_profile_course_progress__item th:nth-child(4) {
  width: 25%;
}
.learn-press-profile-course__progress .lp_profile_course_progress__item td:nth-child(5), .learn-press-profile-course__progress .lp_profile_course_progress__item th:nth-child(5) {
  width: 25%;
}
@media (max-width: 768px) {
  .learn-press-profile-course__progress .learn-press-course-tab__filter__content {
    overflow-x: auto;
  }
}

.lp-profile-content #profile-content-order-details h3 {
  margin-top: 0;
  font-size: 1.875em;
  line-height: 1.5;
  font-weight: 500;
  margin-bottom: 15px;
}

.recover-order__title {
  margin-bottom: 5px;
}
.recover-order__description {
  margin-bottom: 20px;
  opacity: 0.7;
  font-size: 0.8em;
  font-style: italic;
}

.lp-order-recover {
  display: flex;
  gap: 10px;
}
.lp-order-recover input[type=text] {
  font-style: italic;
  flex: 1 1 auto;
}

.lp-profile-content ul {
  list-style: none !important;
  padding: 0 !important;
}
.lp-profile-content ul.learn-press-courses {
  margin: 0 -16px;
}

.lp-user-avatar__buttons {
  display: inline-flex;
  gap: 12px;
  margin-top: 16px;
}

.lp-list-table .learn-press-pagination {
  text-align: left;
}

.course-extra-box {
  margin-bottom: 16px;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  border-radius: var(--lp-border-radius, 5px);
  width: 100%;
}
.course-extra-box.active .course-extra-box__content {
  height: auto;
}
.course-extra-box__title {
  --extra-height: 50px;
  display: flex;
  align-items: center;
  position: relative;
  height: var(--extra-height);
  margin: 0 !important;
  padding: 0 45px 0 20px;
  background: rgba(181, 187, 211, 0.15);
  font-size: 1em;
  font-weight: 700;
  cursor: pointer;
}
@media (max-width: 767px) {
  .course-extra-box__title {
    padding-left: 16px;
  }
}
.course-extra-box__title::after {
  position: absolute;
  top: 0;
  right: 20px;
  font-family: "lp-icon";
  line-height: var(--extra-height);
  content: "\f107";
}
.course-extra-box__content {
  overflow: hidden;
  transition: height 0.3s ease;
}
.course-extra-box__content-inner {
  -webkit-animation-name: course-extra-box__content-inner-transform;
  animation-name: course-extra-box__content-inner-transform;
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
  -webkit-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-direction: normal;
  animation-direction: normal;
}
.course-extra-box__content-inner > ul {
  padding-left: 0 !important;
  padding-bottom: 0 !important;
}
.course-extra-box__content ul,
.course-extra-box__content li {
  list-style: none;
}
.course-extra-box__content ul {
  margin: 0;
  padding: 0;
}
.course-extra-box__content li {
  margin: 0;
  padding: 12px 20px;
  border-bottom: 1px solid var(--lp-border-color, #E2E0DB);
}
@media (max-width: 767px) {
  .course-extra-box__content li {
    padding-left: 16px;
    padding-right: 16px;
  }
}
.course-extra-box__content li::before {
  margin-right: 8px;
  color: var(--lp-primary-color);
  font-family: "lp-icon";
  content: "\f00c";
}
.course-extra-box__content li:last-child {
  border-bottom: 0;
}
.course-extra-box:last-child {
  margin-bottom: 60px;
}
.course-extra-box.active .course-extra-box__title::after {
  content: "\f106";
}
.course-extra-box + .comment-respond, .course-extra-box + .comments-area {
  margin-top: 30px;
  margin-bottom: 30px;
}
.course-extra-box + .course-tabs {
  margin-top: 30px;
}

input[name=course-extra-box-ratio] {
  display: none;
}
input[name=course-extra-box-ratio]:checked + .course-extra-box .course-extra-box__content {
  display: block;
}
input[name=course-extra-box-ratio]:checked + .course-extra-box .course-extra-box__content .course-extra-box__content-inner {
  transform: scale(1);
}

@-webkit-keyframes course-extra-box__content-inner-transform {
  from {
    opacity: 0;
    -webkit-transform: translateX(5%);
    -moz-transform: translateX(5%);
    -ms-transform: translateX(5%);
    -o-transform: translateX(5%);
    transform: translateX(5%);
  }
  to {
    opacity: 1;
    -webkit-transform: translateX(0%);
    -moz-transform: translateX(0%);
    -ms-transform: translateX(0%);
    -o-transform: translateX(0%);
    transform: translateX(0%);
  }
}
@keyframes course-extra-box__content-inner-transform {
  from {
    opacity: 0;
    transform: translateX(5%);
  }
  to {
    opacity: 1;
    transform: translateX(0%);
  }
}
.course-tab-panel-faqs .course-faqs-box {
  margin-bottom: 20px;
  border: 1px solid rgba(204, 204, 204, 0.6);
  border-radius: 5px;
}
.course-tab-panel-faqs .course-faqs-box__title {
  display: block;
  position: relative;
  margin: 0;
  padding: 12px 45px 12px 20px;
  font-size: 1em;
  line-height: 1.5;
  font-weight: var(--lp-font-weight-link, 600);
  cursor: pointer;
}
.course-tab-panel-faqs .course-faqs-box__title::after {
  position: absolute;
  top: 12px;
  right: 20px;
  font-family: "lp-icon";
  content: "\f067";
}
.course-tab-panel-faqs .course-faqs-box:last-child {
  margin-bottom: 40px;
}
.course-tab-panel-faqs .course-faqs-box:hover .course-faqs-box__title {
  color: var(--lp-primary-color);
}
.course-tab-panel-faqs .course-faqs-box__content {
  display: none;
}
.course-tab-panel-faqs .course-faqs-box__content-inner {
  padding: 20px;
  -webkit-animation-name: course-faqs-box__content-inner-transform;
  animation-name: course-faqs-box__content-inner-transform;
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
  -webkit-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-direction: normal;
  animation-direction: normal;
}

input[name=course-faqs-box-ratio] {
  display: none;
}
input[name=course-faqs-box-ratio]:checked + .course-faqs-box .course-faqs-box__content {
  display: block;
}
input[name=course-faqs-box-ratio]:checked + .course-faqs-box .course-faqs-box__title {
  color: var(--lp-primary-color);
  background: rgba(241, 242, 248, 0.4);
}
input[name=course-faqs-box-ratio]:checked + .course-faqs-box .course-faqs-box__title::after {
  content: "\f068";
}

@-webkit-keyframes course-faqs-box__content-inner-transform {
  from {
    opacity: 0;
    -webkit-transform: translateY(-5%);
    -moz-transform: translateY(-5%);
    -ms-transform: translateY(-5%);
    -o-transform: translateY(-5%);
    transform: translateY(-5%);
  }
  to {
    opacity: 1;
    -webkit-transform: translateY(0%);
    -moz-transform: translateY(0%);
    -ms-transform: translateY(0%);
    -o-transform: translateY(0%);
    transform: translateY(0%);
  }
}
@keyframes course-faqs-box__content-inner-transform {
  from {
    opacity: 0;
    -webkit-transform: translateY(-5%);
    -moz-transform: translateY(-5%);
    -ms-transform: translateY(-5%);
    -o-transform: translateY(-5%);
    transform: translateY(-5%);
  }
  to {
    opacity: 1;
    -webkit-transform: translateY(0%);
    -moz-transform: translateY(0%);
    -ms-transform: translateY(0%);
    -o-transform: translateY(0%);
    transform: translateY(0%);
  }
}
.extra-box__title, .course-faqs__title, .course-material__title {
  margin-top: 0;
  margin-bottom: 12px;
}

.edit-content {
  margin-left: 5px;
}

.course-curriculum ul.curriculum-sections {
  position: relative;
  margin: 0;
  padding: 0;
  list-style: none;
}
.course-curriculum ul.curriculum-sections .closed .section-item__loadmore {
  display: none;
}
.course-curriculum ul.curriculum-sections .section {
  margin: 0;
  padding: 0;
  flex-wrap: wrap;
}
.course-curriculum ul.curriculum-sections .section:last-child {
  padding: 0;
}
.course-curriculum ul.curriculum-sections .section.section-empty .section-header {
  margin-bottom: 20px;
}
.course-curriculum ul.curriculum-sections .section.section-empty .learn-press-message {
  margin-right: 15px;
  margin-left: 15px;
}
.course-curriculum ul.curriculum-sections .section-title.c + .section-desc {
  display: block;
}
.course-curriculum ul.curriculum-sections .section-title.c span.show-desc::before {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
  top: 0;
}
.course-curriculum ul.curriculum-sections .item-meta.duration {
  background: #d9e0f1;
}
.course-curriculum .section-item__loadmore {
  display: flex;
  justify-content: center;
  align-items: center;
}
.course-curriculum .section-item__loadmore button {
  margin-top: 10px;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  border-radius: var(--lp-border-radius, 5px);
  padding: 8px 16px;
  box-shadow: none;
  outline: none;
}
.course-curriculum .section-item__loadmore.loading button:before {
  display: inline-block;
  font-family: "lp-icon";
  content: "\f110";
  animation: lp-rotating 1s linear infinite;
  margin-right: 5px;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  vertical-align: middle;
  margin-top: -2px;
}
.course-curriculum .section-header {
  display: table;
  width: 100%;
  padding: 20px 0;
  border-bottom: 0;
  border-bottom: 1px solid #d9e0f1;
  cursor: pointer;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.course-curriculum .section-header .section-title, .course-curriculum .section-header .section-desc {
  margin: 0;
}
.course-curriculum .section-header span.show-desc {
  display: inline-block;
  position: absolute;
  top: 50%;
  right: 30px;
  width: 20px;
  height: 20px;
  transform: translate(0, -50%);
}
.course-curriculum .section-header span.show-desc::before {
  font-family: "lp-icon";
  font-size: 1.125em;
  content: "\f107";
}
.course-curriculum .section-header span.show-desc:hover::before {
  border-top-color: #ccc;
}
.course-curriculum .section-header .section-desc {
  margin-top: 10px;
  font-size: calc(var(--lp-font-size-base, 1em) * 0.925);
  color: var(--lp-color-accent);
  font-style: italic;
  line-height: 1.3;
}
.course-curriculum .section-header .section-meta {
  display: block;
  padding-top: 17px;
  padding-bottom: 15px;
  font-size: 0.8em;
  text-align: right;
  vertical-align: middle;
  white-space: nowrap;
}
.course-curriculum .section-item {
  width: 100%;
}
.course-curriculum .section-content {
  margin: 0;
  padding: 0;
  list-style: none;
}
.course-curriculum .section-content .course-item-meta {
  display: table-cell;
  text-align: right;
  vertical-align: middle;
  white-space: nowrap;
}
.course-curriculum .section-content .course-item-meta .item-meta {
  display: inline-block;
  color: #fff;
}
.course-curriculum .section-content .course-item-meta .item-meta.final-quiz {
  background: #14c4ff;
}
.course-curriculum .section-content .course-item-meta .item-meta.trans {
  padding: 0;
}
.course-curriculum .section-content .course-item-meta .count-questions {
  background: #9672cf;
}
.course-curriculum .section-content .course-item-meta .duration {
  background: #c0c0c0;
}
.course-curriculum .section-content .course-item-meta .course-item-status {
  padding: 0;
  color: #999;
}
.course-curriculum .section-content .course-item-meta .course-item-status::before {
  font-family: "lp-icon";
  content: "\f00c";
}
.course-curriculum .section-content .course-item-preview {
  font-style: normal;
  padding: 0;
}
.course-curriculum .section-content .course-item-preview::before {
  font-family: "lp-icon";
  content: "\f06e";
  color: #999;
}
.course-curriculum .course-item {
  display: flex;
  position: relative;
  margin: 0 0 2px 0;
  padding: 0 16px;
  background: rgba(241, 242, 248, 0.4);
  transition: padding-left linear 0.15s;
}
.course-curriculum .course-item > span {
  display: flex;
  width: 28px;
  color: #666;
  font-weight: 300;
  align-items: center;
}
.course-curriculum .course-item .section-item-link {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  width: 100%;
  padding: 12px 0;
  color: inherit;
  outline: none;
  gap: 12px;
}
.course-curriculum .course-item .section-item-link:hover .item-name {
  color: var(--lp-primary-color);
}
.course-curriculum .course-item .section-item-link::before {
  color: var(--lp-primary-color);
  font-family: "lp-icon";
}
.course-curriculum .course-item .section-item-link .course-item-info {
  width: 100%;
}
.course-curriculum .course-item .section-item-link .course-item-info .course-item-info-pre {
  display: flex;
  flex-flow: row-reverse;
  justify-content: flex-end;
  gap: 16px;
  align-items: center;
  font-size: calc(var(--lp-font-size-base, 1em) * 0.925);
}
.course-curriculum .course-item .section-item-link .course-item-info .course-item-info-pre .item-meta.duration {
  background: transparent;
}
.course-curriculum .course-item .item-name {
  font-weight: 600;
}
.course-curriculum .course-item.course-item-lp_quiz .section-item-link::before {
  content: "\f059";
}
.course-curriculum .course-item.course-item-lp_assignment .section-item-link::before {
  content: "\e929" !important;
}
.course-curriculum .course-item.course-item.course-item-lp_h5p .section-item-link::before {
  content: "\e92a" !important;
}
.course-curriculum .course-item.course-item-lp_lesson .section-item-link::before {
  content: "\f15b";
}
.course-curriculum .course-item.course-item-lp_lesson.course-item-type-video .section-item-link::before {
  content: "\f03d";
}
.course-curriculum .course-item.course-item-lp_lesson.course-item-type-audio .section-item-link::before {
  content: "\f028";
}
.course-curriculum .course-item.item-locked .course-item-status::before {
  color: var(--lp-secondary-color);
  content: "\f023";
}
.course-curriculum .course-item.has-status {
  padding-top: 1px;
}
.course-curriculum .course-item.has-status.status-completed .course-item-status::before, .course-curriculum .course-item.has-status.status-evaluated .course-item-status::before {
  color: #3bb54a;
}
.course-curriculum .course-item.has-status.item-failed .course-item-status::before, .course-curriculum .course-item.has-status.failed .course-item-status::before {
  border-color: #f02425;
  color: #f02425;
  content: "\f00d";
}
.course-curriculum .course-item::before {
  position: absolute;
  top: 50%;
  left: 0;
  width: 3px;
  height: 0;
  background: #00adff;
  content: "";
  transition: height linear 0.15s, top linear 0.15s;
}
.course-curriculum .course-item.current {
  background: #f9f9f9;
}
.course-curriculum .course-item.current a::before {
  left: 10px;
}
.course-curriculum .course-item.current::before {
  top: 0;
  height: 100%;
}
.course-curriculum .section-left {
  vertical-align: top;
}
.course-curriculum .section-left .section-title {
  font-weight: 700;
  font-size: calc(var(--lp-font-size-base, 1em) * 1.25);
  text-transform: capitalize;
  display: block;
}
.course-curriculum .curriculum-more__button {
  border: 1px solid var(--lp-border-color, #E2E0DB);
  border-radius: var(--lp-border-radius, 5px);
  padding: 8px 16px;
  box-shadow: none;
  outline: none;
  width: 100%;
  margin-top: 20px;
  margin-bottom: 20px;
}
.course-curriculum .curriculum-more__button.loading:before {
  display: inline-block;
  font-family: "lp-icon";
  content: "\f110";
  animation: lp-rotating 1s linear infinite;
  margin-right: 5px;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  vertical-align: middle;
  margin-top: -2px;
}

body .content-item-summary .form-button-finish-course, body .lp-quiz-buttons .form-button-finish-course {
  float: right;
}

#wpadminbar #wp-admin-bar-edit-lp_quiz .ab-item::before, #wpadminbar #wp-admin-bar-edit-lp_lesson .ab-item::before, #wpadminbar #wp-admin-bar-edit-lp_question .ab-item::before {
  top: 2px;
  font-family: "lp-icon";
}
#wpadminbar #wp-admin-bar-edit-lp_quiz .ab-item::before {
  content: "\f017";
}
#wpadminbar #wp-admin-bar-edit-lp_lesson .ab-item::before {
  content: "\f15c";
}
#wpadminbar #wp-admin-bar-edit-lp_question .ab-item::before {
  content: "\f29c";
}

.scroll-wrapper {
  overflow: hidden;
  opacity: 0;
}
.scroll-wrapper .scroll-element {
  background: transparent;
}
.scroll-wrapper .scroll-element.scroll-y.scroll-scrolly_visible {
  transition: opacity 0.25s;
}
.scroll-wrapper:hover .scroll-element.scroll-y.scroll-scrolly_visible {
  opacity: 0.7;
}

.course-remaining-time .label-enrolled {
  font-size: inherit;
}

.lp-course-progress {
  position: relative;
}
.lp-course-progress .lp-passing-conditional {
  position: absolute;
  top: 0;
  width: 3px;
  height: 6px;
  margin-left: -1px;
  background: var(--lp-secondary-color);
}

.viewing-course-item .section-header .section-desc {
  display: none;
}

.lp-course-curriculum ul, .lp-course-curriculum li {
  list-style: none;
  margin: 0;
  padding: 0;
}
.lp-course-curriculum .course-curriculum-info {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 20px;
}
.lp-course-curriculum .course-curriculum-info__left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}
.lp-course-curriculum .course-curriculum-info__left li {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}
.lp-course-curriculum .course-curriculum-info__left li::after {
  content: "";
  width: 4px;
  height: 4px;
  background-color: var(--lp-border-color, #E2E0DB);
  display: inline-block;
}
.lp-course-curriculum .course-curriculum-info__left li:last-child::after {
  content: none;
}
.lp-course-curriculum .course-curriculum-info__right {
  font-weight: var(--lp-font-weight-link, 600);
  text-align: right;
  text-transform: capitalize;
}
.lp-course-curriculum .course-toggle-all-sections {
  cursor: pointer;
}
.lp-course-curriculum .course-section {
  margin-bottom: 8px;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  border-radius: var(--lp-border-radius, 5px);
  cursor: pointer;
  overflow: hidden;
}
.lp-course-curriculum .course-section.lp-collapse .course-section__items {
  display: none;
  animation: ease-in-out;
}
.lp-course-curriculum .course-section.lp-collapse .lp-icon-angle-up {
  display: none;
}
.lp-course-curriculum .course-section.lp-collapse .lp-icon-angle-down {
  display: block;
}
.lp-course-curriculum .course-section .lp-icon-angle-down {
  display: none;
}
.lp-course-curriculum .course-section:last-child {
  margin-bottom: 0;
}
.lp-course-curriculum .course-section .course-section-header {
  background-color: var(--lp-white-grey, #F7F7FB);
  padding: 20px;
  column-gap: 12px;
  display: flex;
  justify-content: space-between;
}
.lp-course-curriculum .course-section__title {
  font-weight: 600;
  font-size: 1.1em;
  line-height: 1.3em;
}
.lp-course-curriculum .course-section__description {
  margin: 4px 0 0 0;
}
.lp-course-curriculum .course-section .section-toggle {
  line-height: 1;
}
.lp-course-curriculum .course-section .section-toggle i {
  font-size: 24px;
}
.lp-course-curriculum .course-section .course-section-info {
  margin-left: 0;
  margin-right: auto;
}
.lp-course-curriculum .course-section .section-count-items {
  min-width: 24px;
  font-weight: 600;
  text-align: center;
  line-height: 1.3;
  white-space: nowrap;
}
.lp-course-curriculum .course-section .course-item {
  background-color: transparent;
  border-top: 1px solid var(--lp-border-color, #E2E0DB);
  padding: 12px 20px;
  margin: 0;
}
.lp-course-curriculum .course-section .course-item__link {
  display: flex;
  justify-content: space-between;
  width: 100%;
  column-gap: 12px;
  row-gap: 8px;
  position: relative;
  align-items: flex-start;
  color: inherit;
}
.lp-course-curriculum .course-section .course-item__info {
  display: flex;
  column-gap: 12px;
  row-gap: 8px;
}
.lp-course-curriculum .course-section .course-item__info .course-item-ico {
  min-width: 16px;
}
.lp-course-curriculum .course-section .course-item__content {
  display: flex;
  justify-content: space-between;
  column-gap: 12px;
  row-gap: 8px;
  align-items: baseline;
  flex: 1;
}
@media (max-width: 1024px) {
  .lp-course-curriculum .course-section .course-item__content {
    flex-wrap: wrap;
  }
}
.lp-course-curriculum .course-section .course-item__left:hover {
  color: var(--lp-primary-color, #ffb606);
}
.lp-course-curriculum .course-section .course-item__right {
  display: flex;
  column-gap: 12px;
  row-gap: 8px;
  align-items: center;
  flex: none;
  color: var(--lp-color-meta, #8a8a8a);
  flex-wrap: wrap;
  flex-direction: row-reverse;
  justify-content: flex-end;
}
@media (max-width: 1024px) {
  .lp-course-curriculum .course-section .course-item__right {
    width: 100%;
    order: 3;
  }
}
.lp-course-curriculum .course-section .course-item__status .course-item-ico {
  width: 24px;
  display: flex;
  justify-content: center;
}
.lp-course-curriculum .course-section .course-item-ico::before {
  content: "";
  display: inline-block;
  font-family: "lp-icon";
  font-weight: normal;
}
.lp-course-curriculum .course-section .course-item-ico.lp_lesson::before {
  content: "\f15b";
}
.lp-course-curriculum .course-section .course-item-ico.lp_quiz::before {
  content: "\f12e";
}
.lp-course-curriculum .course-section .course-item-ico.lp_assignment::before {
  content: "\e929";
}
.lp-course-curriculum .course-section .course-item-ico.lp_h5p::before {
  content: "\e92a";
}
.lp-course-curriculum .course-section .course-item-ico.preview::before {
  content: "\f06e";
  color: #999;
}
.lp-course-curriculum .course-section .course-item-ico.locked::before {
  content: "\f023";
  color: #999;
}
.lp-course-curriculum .course-section .course-item-ico.passed.completed::before {
  content: "\f00c";
  color: #3bb54a;
}
.lp-course-curriculum .course-section .course-item-ico.in-progress::before, .lp-course-curriculum .course-section .course-item-ico.completed::before {
  content: "\f00c";
  color: #999;
}
.lp-course-curriculum .course-section .course-item-ico.failed.completed::before {
  content: "\f00d";
  color: #f02425;
}
.lp-course-curriculum .course-section .course-item-ico.started::before {
  content: "\f00c";
  color: #999;
}
.lp-course-curriculum .course-section .course-item-ico.doing::before {
  content: "\e921";
  color: #999;
}

.info-learning .course-progress__line {
  width: 100%;
  background: #ccc;
  height: 5px;
  border-radius: 5px;
  position: relative;
}
.info-learning .course-progress__line__active {
  background: var(--lp-primary-color);
  height: 100%;
  border-radius: 5px;
  position: absolute;
  top: 0;
  left: 0;
}
.info-learning .course-progress__line__active {
  background: var(--lp-primary-color);
  height: 100%;
  border-radius: 5px;
  position: absolute;
  top: 0;
  left: 0;
}
.info-learning .course-progress__line__point {
  position: absolute;
  top: 0;
  width: 2px;
  height: 100%;
  background: var(--lp-secondary-color);
}

.lp-quiz-buttons {
  margin-bottom: 20px;
  display: block;
  clear: both;
  content: "";
}

.quiz-progress {
  margin-bottom: 30px;
  background: rgb(231, 247, 255);
}
.quiz-progress .progress-items {
  display: flex;
}
.quiz-progress .progress-items .progress-item {
  position: relative;
  color: #777;
  font-size: 0.938em;
  flex: 1;
}
.quiz-progress .progress-items .progress-item .progress-number, .quiz-progress .progress-items .progress-item .progress-label {
  display: block;
  line-height: 1;
  text-align: center;
}
.quiz-progress .progress-items .progress-item .progress-number {
  margin: 15px 0 10px 0;
  font-size: 1.25em;
}
.quiz-progress .progress-items .progress-item .progress-label {
  margin-bottom: 15px;
  font-size: 0.875em;
}
.quiz-progress .progress-items .progress-item i {
  display: none;
  float: left;
  width: 60px;
  height: 60px;
  color: #fff;
  background: #00adff;
  font-size: 1.875em;
  line-height: 60px;
  text-align: center;
}
.quiz-progress .progress-items .progress-item::after {
  display: block;
  clear: both;
  content: "";
}

.answer-options {
  margin: 0;
  padding: 0;
  list-style: none;
}
.answer-options .answer-option {
  display: flex;
  overflow: hidden;
  position: relative;
  margin: 0 0 18px 0;
  padding: 10px;
  cursor: pointer;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -webkit-transition: background linear 0.25s;
  -moz-transition: background linear 0.25s;
  -ms-transition: background linear 0.25s;
  -o-transition: background linear 0.25s;
  transition: background linear 0.25s;
}
.answer-options .answer-option .option-title {
  display: table-cell;
}
.answer-options .answer-option .option-title .option-title-content {
  display: inline-block;
  vertical-align: middle;
}
.answer-options .answer-option .option-title::before {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 3px;
  background: #ddd;
  content: "";
  -webkit-transition: background linear 0.25s;
  -moz-transition: background linear 0.25s;
  -ms-transition: background linear 0.25s;
  -o-transition: background linear 0.25s;
  transition: background linear 0.25s;
}
.answer-options .answer-option input[type=checkbox],
.answer-options .answer-option input[type=radio] {
  -webkit-appearance: initial;
  -moz-appearance: initial;
  position: relative;
  z-index: 10;
  width: 32px;
  min-width: 32px;
  height: 32px;
  margin: 0 10px 0 3px;
  border: 1px solid #cfcfcf;
  background: #f9fafc;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.answer-options .answer-option input[type=checkbox]::after,
.answer-options .answer-option input[type=radio]::after {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.answer-options .answer-option input[type=checkbox]:focus,
.answer-options .answer-option input[type=radio]:focus {
  outline: none;
}
.answer-options .answer-option input[type=checkbox]:checked ~ .option-title .option-title-content,
.answer-options .answer-option input[type=radio]:checked ~ .option-title .option-title-content {
  position: relative;
}
.answer-options .answer-option input[type=checkbox]:checked ~ .option-title::before,
.answer-options .answer-option input[type=radio]:checked ~ .option-title::before {
  background: #00adff;
}
.answer-options .answer-option input[type=checkbox]:checked::after,
.answer-options .answer-option input[type=radio]:checked::after {
  opacity: 1;
}
.answer-options .answer-option input[type=checkbox]::after {
  margin-top: 0;
}
.answer-options .answer-option input[type=checkbox]::after {
  width: auto;
  box-sizing: content-box;
  height: auto;
  color: #3db748;
  font-family: "lp-icon";
  font-size: 16px;
  opacity: 0;
  content: "\f00c";
  background: none;
  border: none;
}
.answer-options .answer-option input[type=checkbox]:checked::after {
  border: none;
}
.answer-options .answer-option input[type=radio] {
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
}
.answer-options .answer-option input[type=radio]::before {
  border-radius: 50%;
}
.answer-options .answer-option input[type=radio]:checked::after {
  content: "";
  width: 10px;
  height: 10px;
  border-radius: 10px;
  background: #00adff;
}
.answer-options .answer-option .option-title {
  margin: 0;
}
.answer-options .answer-option:hover {
  background: #e1f5ff;
}
.answer-options .answer-option.answered-wrong input[type=checkbox]::after {
  color: #f00;
}
.answer-options .answer-option.answered-wrong input[type=radio]::after {
  background: #f00;
}

button[data-counter] {
  position: relative;
}
button[data-counter]::after {
  padding-left: 5px;
  content: "(+" attr(data-counter) ")";
}

.quiz-result {
  max-width: 320px;
  margin: 20px auto 48px;
  text-align: center;
}
.quiz-result .result-heading {
  display: none;
}
.quiz-result.passed .result-message {
  background: #3bb54a;
}
.quiz-result.passed .result-message::after {
  content: "\f00c";
}
.quiz-result .result-message {
  margin-bottom: 30px;
  padding: 10px 0;
  color: #fff;
  background: #f02425;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-border-radius: var(--lp-border-radius, 5px);
  -moz-border-radius: var(--lp-border-radius, 5px);
  border-radius: var(--lp-border-radius, 5px);
  gap: 8px;
}
.quiz-result .result-message::after {
  font-family: "lp-icon";
  content: "\f00d";
}
.quiz-result .result-grade .result-achieved,
.quiz-result .result-grade .result-require {
  display: inline-block;
  margin: 0 auto;
}
.quiz-result .result-grade .result-achieved {
  padding-bottom: 7px;
  border-bottom: 1px solid var(--lp-border-color, #E2E0DB);
  font-size: 1.75em;
  font-weight: 500;
  line-height: 1;
}
.quiz-result .result-grade .result-require {
  display: block;
  padding-top: 5px;
  color: #666;
  font-size: 1em;
  font-weight: 400;
  line-height: 1;
}
.quiz-result .result-grade .result-message {
  font-size: 0.875em;
}
.quiz-result.passed .result-achieved {
  color: #04adff;
}
.quiz-result.passed .result-message strong {
  color: #04adff;
}
.quiz-result .result-statistic {
  margin: 0;
  padding: 0;
  text-align: left;
  list-style: none;
}
.quiz-result .result-statistic .result-statistic-field {
  display: flex;
  margin: 0;
}
.quiz-result .result-statistic .result-statistic-field + li {
  border-top: 1px dashed var(--lp-border-color, #E2E0DB);
}
.quiz-result .result-statistic .result-statistic-field span, .quiz-result .result-statistic .result-statistic-field p {
  margin: 0;
  flex: 1;
}
.quiz-result .result-statistic .result-statistic-field span {
  color: #666;
  font-size: 1em;
  font-weight: 400;
  line-height: 2.1875em;
}
.quiz-result .result-statistic .result-statistic-field span::before {
  display: inline-block;
  width: 15px;
  margin-right: 10px;
  color: var(--lp-primary-color);
  font-family: "lp-icon";
  font-size: 1em;
  font-weight: 900;
}
.quiz-result .result-statistic .result-statistic-field p {
  color: #333;
  font-weight: 500;
  text-align: right;
}
.quiz-result .result-statistic .result-statistic-field.result-time-spend label::before {
  font-weight: 400;
  content: "\f017";
}
.quiz-result .result-statistic .result-statistic-field.result-point label::before {
  font-weight: 400;
  content: "\f005";
}
.quiz-result .result-statistic .result-statistic-field.result-questions label::before {
  font-weight: 400;
  content: "\f059";
}
.quiz-result .result-statistic .result-statistic-field.result-questions-correct label::before {
  color: #3db748;
  content: "\f00c";
}
.quiz-result .result-statistic .result-statistic-field.result-questions-wrong label::before {
  color: #f02425;
  content: "\f00d";
}
.quiz-result .result-statistic .result-statistic-field.result-questions-skipped label::before {
  color: #ddd;
  content: "\f2f5";
}

.lp-sidebar-toggle__close #content-item-quiz .quiz-status > div {
  max-width: 100%;
  flex-direction: column;
}
@media (min-width: 426px) {
  .lp-sidebar-toggle__close #content-item-quiz .quiz-status > div {
    flex-direction: row;
    justify-content: space-between;
  }
}
@media (min-width: 1025px) {
  .lp-sidebar-toggle__close #content-item-quiz .quiz-status > div {
    max-width: 792px;
  }
}
@media (min-width: 450px) {
  .lp-sidebar-toggle__close #content-item-quiz .quiz-status > div .questions-index {
    width: 35%;
    text-align: inherit;
  }
}
@media (min-width: 1120px) {
  .lp-sidebar-toggle__close #content-item-quiz .quiz-status > div .questions-index {
    width: 50%;
  }
}
@media (min-width: 450px) {
  .lp-sidebar-toggle__close #content-item-quiz .quiz-status > div > div {
    width: 65%;
    justify-content: end;
    flex: auto;
  }
}
@media (min-width: 768px) {
  .lp-sidebar-toggle__close #content-item-quiz .quiz-status > div > div {
    width: 50%;
  }
}
@media (min-width: 1120px) {
  .lp-sidebar-toggle__close #content-item-quiz .quiz-status > div > div {
    width: 50%;
  }
}
@media (max-width: 768px) {
  .lp-sidebar-toggle__close #content-item-quiz .quiz-status > div > div .submit-quiz #button-submit-quiz {
    white-space: inherit;
    padding: 8px 16px;
  }
}
.lp-sidebar-toggle__open #content-item-quiz .quiz-status > div {
  max-width: 100%;
  flex-direction: column;
}
@media (min-width: 650px) {
  .lp-sidebar-toggle__open #content-item-quiz .quiz-status > div {
    flex-direction: row;
  }
}
@media (min-width: 1120px) {
  .lp-sidebar-toggle__open #content-item-quiz .quiz-status > div {
    max-width: 792px;
  }
}
.lp-sidebar-toggle__open #content-item-quiz .quiz-status > div .questions-index {
  width: 100%;
  text-align: center;
}
@media (min-width: 650px) {
  .lp-sidebar-toggle__open #content-item-quiz .quiz-status > div .questions-index {
    width: 35%;
    text-align: inherit;
  }
}
@media (min-width: 1120px) {
  .lp-sidebar-toggle__open #content-item-quiz .quiz-status > div .questions-index {
    width: 50%;
  }
}
.lp-sidebar-toggle__open #content-item-quiz .quiz-status > div > div {
  width: 100%;
  justify-content: center;
  flex-direction: column-reverse;
  gap: 0;
}
@media (min-width: 490px) {
  .lp-sidebar-toggle__open #content-item-quiz .quiz-status > div > div {
    flex-direction: row-reverse;
  }
}
@media (min-width: 650px) {
  .lp-sidebar-toggle__open #content-item-quiz .quiz-status > div > div {
    width: 65%;
    gap: 12px;
    justify-content: end;
    flex: auto;
  }
}
@media (min-width: 768px) {
  .lp-sidebar-toggle__open #content-item-quiz .quiz-status > div > div {
    width: 50%;
  }
}
@media (min-width: 1120px) {
  .lp-sidebar-toggle__open #content-item-quiz .quiz-status > div > div {
    width: 50%;
  }
}
@media (max-width: 768px) {
  .lp-sidebar-toggle__open #content-item-quiz .quiz-status > div > div .submit-quiz #button-submit-quiz {
    white-space: inherit;
    padding: 8px 16px;
  }
}
.quiz-status {
  position: sticky;
  z-index: 99;
  top: 0;
  right: 0;
  left: 0;
  margin: 0 0 35px 0;
}
.quiz-status > div {
  display: flex;
  box-sizing: border-box;
  width: 100%;
  max-width: 792px;
  margin: 0 auto;
  padding: 8px 16px;
  border-radius: 4px;
  background: var(--lp-primary-color);
  justify-content: center;
  align-items: center;
}
.quiz-status > div > div {
  display: flex;
  flex-direction: row-reverse;
  flex: 0 0 50%;
  align-items: center;
}
@media (max-width: 480px) {
  .quiz-status > div > div {
    justify-content: center;
  }
}
.quiz-status > div > div .submit-quiz #button-submit-quiz {
  margin: 0;
  border-radius: 4px;
  overflow: hidden;
  white-space: nowrap;
}
.quiz-status > div > div .countdown {
  margin: 0;
  border-radius: 4px;
  overflow: hidden;
  padding: 12px;
}
.quiz-status > div .current-point {
  display: none;
}
.quiz-status .questions-index {
  display: inline-block;
  color: #666;
  font-size: 1em;
  font-weight: 400;
  line-height: 1.625em;
}
.quiz-status .questions-index span {
  color: #333;
  font-weight: 500;
}
.quiz-status .countdown {
  min-height: 38px;
  color: #333;
  font-weight: 500;
  text-align: center;
}
.quiz-status .countdown .fas {
  color: #333;
  font-size: 1em;
}
.quiz-status .countdown .clock {
  display: none;
  width: 40px;
  height: 40px;
}
.quiz-status .countdown .clock::before {
  position: absolute;
  width: 40px;
  height: 40px;
  border: 4px solid #b1c1e6;
  border-radius: 50%;
  content: "";
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.quiz-status .countdown .clock .circle-progress-bar {
  display: inline-block;
  position: relative;
  width: 40px;
  height: 40px;
  stroke: #5383f7;
}
.quiz-status .countdown .clock .circle-progress-bar .circle-progress-bar__circle {
  transition: 0.35s stroke-dashoffset;
  transform: rotate(-90deg);
  transform-origin: 50% 50%;
}
.quiz-status .countdown .clock.x .circle-progress-bar__circle {
  stroke: #f00;
}
.quiz-status .countdown i {
  margin: 0 5px 0 0;
}
.quiz-status .submit-quiz button {
  margin: 0 0 0 10px;
  border: none;
  border-radius: 0;
  background: #fff;
  text-transform: uppercase;
}
.quiz-status .submit-quiz button:hover {
  background: var(--lp-secondary-color);
}
.quiz-status.submitting .submit-quiz button {
  background: #ddd;
}

.question-numbers {
  text-align: center;
  list-style: none;
}
.question-numbers li {
  display: inline-block;
  position: relative;
  margin-bottom: 3px;
}
.question-numbers li a {
  display: block;
  min-width: 20px;
  padding: 8px;
  border: 1px solid #ddd;
  color: #999;
  background: #f5f5f5;
  box-shadow: none;
  font-size: 0.75em;
  line-height: 1;
}
.question-numbers li a span {
  vertical-align: middle;
}
.question-numbers li a:hover {
  border: 1px solid #3880a2;
  color: #fff;
  background: #00adff;
}
.question-numbers li.current a {
  border-color: #3880a2;
  color: #fff !important;
  background: #00adff;
}
.question-numbers li.current.skipped::after {
  background: #fff;
}
.question-numbers li.answered a::after {
  margin-left: 3px;
  font-family: "lp-icon";
  font-size: 8px;
  vertical-align: middle;
}
.question-numbers li.answered.answered-wrong a {
  color: #f00;
}
.question-numbers li.answered.answered-true a {
  color: #00adff;
}
.question-numbers li.answered.answered-true.current a {
  color: #fff;
}
.question-numbers li.skipped::after {
  position: absolute;
  bottom: 3px;
  left: 50%;
  width: 10px;
  height: 4px;
  margin-left: -5px;
  border-radius: 2px;
  background: #aaa;
  content: "";
}

.quiz-intro {
  display: flex;
  margin: 0 0 20px;
  padding: 0;
  list-style: none;
  flex-flow: row wrap;
}
@media (max-width: 768px) {
  .quiz-intro {
    padding-right: 10px;
    padding-left: 10px;
    justify-content: space-between;
  }
}
.quiz-intro-item {
  display: flex;
  margin: 0 40px 0 0;
  align-items: center;
}
@media (max-width: 768px) {
  .quiz-intro-item {
    margin: 0 20px 0 0;
  }
}
.quiz-intro-item::before {
  position: relative;
  margin-right: 10px;
  color: var(--lp-primary-color);
  font-family: "lp-icon";
  font-size: 1em;
}
.quiz-intro-item--passing-grade {
  order: 2;
}
.quiz-intro-item--passing-grade::before {
  content: "\f012";
}
.quiz-intro-item--questions-count {
  order: 1;
}
.quiz-intro-item--questions-count::before {
  content: "\f12e";
}
.quiz-intro-item--duration {
  order: 2;
}
.quiz-intro-item--duration::before {
  content: "\f017";
}
.quiz-intro-item__title {
  margin: 0;
  padding: 0 8px 0 0;
  color: #333;
  font-weight: 300;
}
.quiz-intro-item__content {
  color: #222;
  font-weight: 400;
}

.question-explanation-content,
.question-hint-content {
  margin-bottom: 20px;
  padding: 10px 15px;
  background: #f5f5f5;
}

.redo-quiz button[type=submit] {
  content: attr(data-counter);
}

.circle-bar {
  position: relative;
  width: 300px;
  height: 300px;
  border-color: #ddd;
}
.circle-bar::before {
  position: absolute;
  z-index: 0;
  top: 0;
  left: 0;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border: 10px solid #ddd;
  border-radius: 50%;
  content: "";
}
.circle-bar .before,
.circle-bar .after {
  position: absolute;
  z-index: 0;
  top: 0;
  left: 0;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border: 10px solid #14c4ff;
  border-radius: 50%;
  transform: rotate(45deg);
}
.circle-bar .before {
  border-bottom-color: transparent;
  border-left-color: transparent;
  transform: rotate(45deg);
}
.circle-bar .after {
  border-color: #14c4ff;
  border-top-color: transparent;
  border-right-color: transparent;
  transform: rotate(45deg);
}
.circle-bar.bg50 .after {
  z-index: 10;
  border-bottom-color: inherit;
  border-left-color: inherit;
  transform: rotate(45deg);
}

.lp-quiz-buttons .complete-quiz,
.lp-quiz-buttons .back-quiz,
.lp-quiz-buttons .review-quiz {
  float: right;
}

.quiz-result .result-grade {
  display: flex;
  flex-direction: column;
  position: relative;
  box-sizing: border-box;
  height: 200px;
  margin-bottom: 30px;
  padding: 50px;
  justify-content: center;
  align-items: center;
  -webkit-transform: none !important;
  -moz-transform: none !important;
  -ms-transform: none !important;
  -o-transform: none !important;
  transform: none !important;
}
.quiz-result .result-grade::before,
.quiz-result .result-grade svg {
  position: absolute;
  top: 0;
  left: 50%;
  width: 200px;
  height: 200px;
  margin-left: -100px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
}
.quiz-result .result-grade::before {
  box-sizing: border-box;
  padding: 30px;
  border: 10px solid #ccc;
  content: "";
}
.quiz-result .result-grade svg {
  -webkit-transform: rotate(-90deg);
  -moz-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  -o-transform: rotate(-90deg);
  transform: rotate(-90deg);
}
.quiz-result .result-grade svg circle {
  stroke: var(--lp-primary-color);
}
.quiz-result.passed .result-grade svg {
  -webkit-transform: rotate(-90deg);
  -moz-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  -o-transform: rotate(-90deg);
  transform: rotate(-90deg);
}
.quiz-result.passed .result-grade .result-achieved {
  color: #333;
}
.quiz-result.passed .result-grade .result-message strong {
  color: #4caf50;
}

.quiz-questions .question {
  margin-bottom: 60px;
}
.quiz-questions .question-answers .answer-options {
  margin: 0;
  padding: 0;
}

.question .answer-option {
  padding: 0;
}
.question .answer-option input[type=radio],
.question .answer-option input[type=checkbox] {
  position: absolute;
  top: 50%;
  border-color: #d9e0f1;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  margin: 0 0 0 10px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.question .answer-option input[type=radio]::after,
.question .answer-option input[type=checkbox]::after {
  border-color: #d9e0f1;
}
.question .answer-option input[type=radio]:disabled,
.question .answer-option input[type=checkbox]:disabled {
  border-color: #ddd;
  background: #f9f9f9;
}
.question .answer-option input[type=radio]:disabled::after,
.question .answer-option input[type=checkbox]:disabled::after {
  border-color: #ddd;
}
.question .answer-option input[type=radio]:checked:not(:disabled)::after,
.question .answer-option input[type=checkbox]:checked:not(:disabled)::after {
  border-color: #99aee4;
}
.question .answer-option input[type=radio] {
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
}
.question .answer-option input[type=radio]::before {
  border-radius: 50%;
}
.question .answer-option .option-title {
  display: flex;
  width: 100%;
  margin: 0;
  padding: 10px 10px 10px 60px;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  color: #666;
  font-weight: normal;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.question .answer-option .option-title:hover {
  background: #f9f9f9;
}
.question .answer-option .option-title::before {
  display: none;
}
.question .answer-option.answer-correct .option-title {
  border-color: #4caf50;
}
.question .answer-option.answered-wrong .option-title {
  border-color: #ff6423;
}
.question .question-title {
  display: block;
  margin-top: 0;
  margin-bottom: 18px;
  font-size: 1.125em;
  font-weight: 500;
}
.question .question-title .edit-link {
  float: right;
  font-size: 0.875em;
  font-weight: normal;
}
.question .question-content {
  margin-bottom: 30px;
}
.question .question-content img {
  width: 100%;
  max-width: 100%;
}
.question .question-response {
  margin-bottom: 10px;
  font-size: 0.875em;
}
.question .question-response .label {
  display: inline-block;
  margin: 0 5px 0 0;
  padding: 8px 10px;
  border-radius: 4px;
  color: #fff;
  line-height: 1;
}
.question .question-response .point {
  display: inline-block;
}
.question .question-response.correct .label {
  background: #4caf50;
}
.question .question-response.incorrect .label {
  background: #ff6423;
}
.question .question-index {
  display: inline-block;
  margin: 0 5px 0 0;
}
.question .btn-show-hint {
  position: relative;
  margin: 0 0 0 10px;
  padding: 0;
  outline: none;
  color: #00adff;
  background: transparent;
  font-size: 0;
  text-decoration: none;
  border: none;
}
.question .btn-show-hint::before {
  font-family: "lp-icon";
  font-size: 18px;
  content: "\f059";
}
.question .btn-show-hint:hover span {
  position: absolute;
  bottom: 100%;
  left: 100%;
  width: auto;
  padding: 6px 9px;
  border-radius: 2px;
  color: #333;
  background: #eee;
  font-size: 14px;
  font-weight: 400;
  line-height: 1;
  white-space: nowrap;
  text-transform: none;
}
.question.question-fill_in_blanks .blanks .blank-input-wrap {
  display: inline-block;
}
.question.question-fill_in_blanks .blanks .blank-block {
  margin-bottom: 20px;
}
.question.question-fill_in_blanks .blanks .blank-input {
  display: inline-block;
  width: auto;
  min-width: 50px;
  margin: 0 10px;
  padding: 0 10px;
  border: none;
  border-bottom: 1px dashed var(--lp-border-color, #E2E0DB);
  text-align: center;
  white-space: nowrap;
}
.question.question-fill_in_blanks .blanks .blank-input br {
  display: none;
}
.question.question-fill_in_blanks .blanks .blank-input > * {
  display: inline;
  white-space: nowrap;
}
.question.question-fill_in_blanks .blanks .blank-select {
  display: inline-block;
  height: 30px;
  margin-left: 4px;
  padding: 1px;
}
.question.question-fill_in_blanks .blanks .blank-fills {
  display: inline-block;
}
.question.question-fill_in_blanks .blanks .blank-fills code {
  margin: 0 5px;
  padding: 0 6px;
  background: #ddd;
  line-height: 1;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.question.question-fill_in_blanks .blanks.ordered {
  list-style-position: inside;
}
.question.question-fill_in_blanks .blanks.one-paragraph {
  line-height: 3;
}

.lp-sort-bg label.option-title {
  background: rgba(255, 182, 6, 0.68) !important;
}

.quiz-attempts .attempts-heading {
  margin-bottom: 15px;
}
.quiz-attempts table th, .quiz-attempts table td {
  text-align: center;
}

.learn-press-message.fixed {
  position: fixed;
  top: 32px;
  left: 0;
  right: 0;
  background: rgba(0, 173, 255, 0.6);
  text-align: center;
  z-index: 100;
  color: #FFF;
  padding: 10px;
}
.learn-press-message.fixed[data-delay-in] {
  display: none;
}

.lp-ajax-message {
  display: none;
  margin-top: 20px;
  padding: 20px;
  border: 2px solid #EBF8E5;
  border-radius: 9px;
  font-size: 0.9em;
  line-height: 1.4;
}
.lp-ajax-message.error {
  border-color: #FEE5E5;
}

.lp-icon-passed {
  margin-right: 5px;
}
.lp-icon-passed::before {
  color: #059601;
  font-family: "lp-icon";
  content: "\f058";
}

.lp-user-item.graduation {
  padding: 10px;
}
.lp-user-item.graduation.passed {
  background-color: #EBF8E5;
  color: #3AB500;
}
.lp-user-item.graduation.failed {
  background-color: #FDECEC;
  color: #D85554;
}

.course-graduation span {
  color: #999;
}
.course-graduation .icon {
  margin-right: 5px;
  font-size: 1.25em;
}
.course-graduation.passed .icon {
  color: #059601;
}
.course-graduation.error .icon {
  color: #d85554;
}

.learn-press-template-warning::before {
  color: #ffc107;
  font-family: "lp-icon";
  content: "\f071";
}

.lp-badge {
  display: flex;
  height: 30px;
  padding: 0;
  line-height: 1;
  align-items: center;
  justify-content: center;
}
.lp-badge::before, .lp-badge::after {
  content: "";
}
.lp-badge::before {
  display: inline-block;
  position: relative;
  z-index: 10;
  color: #fff;
  font-weight: bold;
  line-height: 1;
}
.lp-badge.featured-course {
  position: absolute;
  z-index: 1;
  top: 15px;
  left: -120px;
  width: 300px;
  background: #FF2828;
  text-align: center;
  transform: rotate(-45deg);
  height: auto;
  padding: 5px 0;
}
.lp-badge.featured-course::before {
  font-size: 0.75em;
  text-transform: uppercase;
  content: attr(data-text);
}

/**
 * CSS for overriding some style defaults of themes
 */
body div.entry-content > div.learnpress, body div.entry-content > div.learnpress:not(.alignwide):not(.alignfull):not(.alignleft):not(.alignright):not(.is-style-wide) {
  --responsive--aligndefault-width: 100%;
  max-width: var(--lp-content-width, 100%);
}
body .entry-content.has-global-padding {
  padding-left: 0;
  padding-right: 0;
}

.lp-archive-courses ul, .lp-archive-courses ol {
  list-style-type: decimal;
  list-style-position: inside;
  padding: 0 0 1.5em;
}
.lp-archive-courses ul {
  list-style-type: revert;
  padding: 0 0 1em 1em;
}
.lp-archive-courses ul.course-nav-tabs {
  list-style: none;
  padding: 0;
}
.lp-archive-courses ol ul {
  padding: 2px 0 2px 35px;
}
.lp-archive-courses ol ol {
  padding: 2px 0 2px 20px;
}

.learnpress #left-area ul, .learnpress .entry-content ul, .learnpress .et-l--body ul, .learnpress .et-l--footer ul, .learnpress .et-l--header ul, .learnpress-profile #left-area ul, .learnpress-profile .entry-content ul, .learnpress-profile .et-l--body ul, .learnpress-profile .et-l--footer ul, .learnpress-profile .et-l--header ul {
  padding: 0;
  list-style: none !important;
}

.learnpress.dt-the7 .learn-press-filters {
  margin-bottom: 20px;
}
.learnpress.dt-the7 .learn-press-courses[data-size="3"] .course {
  width: 50%;
}

.bridge.learnpress .lp-archive-courses {
  margin-top: 100px;
}
.bridge.learnpress .lp-archive-courses ul.learn-press-breadcrumb {
  padding-top: 45px;
}
.bridge.learnpress .content {
  z-index: 110;
}
.bridge.learnpress .comment-respond .comment-form {
  margin: 0;
}
.bridge.learnpress .comment-respond .comment-form > p, .bridge.learnpress .comment-respond .comment-form > div {
  padding: 0;
}
.bridge.learnpress.learnpress-profile .content {
  z-index: 100;
}
.bridge.learnpress #learn-press-item-comments {
  background: #f6f6f6;
}

.learnpress.ast-separate-container .ast-article-single {
  padding-left: 15px;
  padding-right: 15px;
}

.ast-container {
  --lp-cotainer-padding: 0 ;
}

body.learnpress-page.twentysixteen #primary .lp-entry-content {
  float: none;
  width: auto;
  margin: 0 15%;
}
body.learnpress-page.twentyfifteen .course-summary-sidebar .widget {
  width: 100%;
  padding: 0;
}
body.learnpress-page.twentysixteen .entry-footer {
  display: none;
}
body.learnpress-page.twentysixteen .lp-entry-content {
  float: none;
  width: auto;
}
body.learnpress-page.twentyseventeen #primary article.type-page {
  width: 100%;
}
body div.entry-content > div.learnpress {
  --responsive--aligndefault-width: 100%;
  max-width: var(--lp-content-width, 100%);
}
body .entry-content.has-global-padding {
  padding-left: 0;
  padding-right: 0;
}

@media screen and (min-width: 61.5625em) {
  body.twentysixteen.learnpress-page .entry-footer {
    display: none;
  }
  body.twentysixteen.learnpress-page .lp-entry-content {
    float: none;
    width: auto;
  }
  body:not(.search-results) article:not(.type-page) .entry-footer {
    display: none;
  }
  body:not(.search-results) article:not(.type-page) .lp-entry-content {
    float: none;
    width: auto;
  }
}
body.twentyseventeen.learnpress-page #primary article.page .entry-header,
body.twentyseventeen.learnpress-page #primary article.page .lp-entry-content {
  width: 100%;
  float: none;
}

@media screen and (min-width: 48em) {
  body.twentyseventeen.learnpress-page #primary article.page .entry-header,
  body.twentyseventeen.learnpress-page #primary article.page .lp-entry-content {
    width: 100%;
    float: none;
  }
}
.edu-press:not(.nofixcss), .theme-edu-press:not(.nofixcss) {
  --lp-font-size-base: 1em;
}
.edu-press:not(.nofixcss) .course-instructor-category > div:nth-child(2) label, .theme-edu-press:not(.nofixcss) .course-instructor-category > div:nth-child(2) label {
  display: none;
}
.edu-press:not(.nofixcss) .course-instructor-category > div:nth-child(2) a, .theme-edu-press:not(.nofixcss) .course-instructor-category > div:nth-child(2) a {
  font-size: 1rem;
}
.edu-press:not(.nofixcss) .course-instructor-category .course-categories a, .theme-edu-press:not(.nofixcss) .course-instructor-category .course-categories a {
  font-weight: normal;
}
.edu-press:not(.nofixcss) .lp-archive-courses .lp-content-area .learn-press-courses[data-layout=grid] .course-categories, .theme-edu-press:not(.nofixcss) .lp-archive-courses .lp-content-area .learn-press-courses[data-layout=grid] .course-categories {
  position: absolute;
}
.edu-press:not(.nofixcss) .lp-archive-courses .lp-content-area .learn-press-courses[data-layout=grid] .course-content .course-instructor, .theme-edu-press:not(.nofixcss) .lp-archive-courses .lp-content-area .learn-press-courses[data-layout=grid] .course-content .course-instructor {
  display: inline;
}
.edu-press:not(.nofixcss) .lp-archive-courses .lp-content-area .learn-press-courses[data-layout=grid] .course-info > *, .theme-edu-press:not(.nofixcss) .lp-archive-courses .lp-content-area .learn-press-courses[data-layout=grid] .course-info > * {
  display: revert;
}
.edu-press:not(.nofixcss) .lp-archive-courses .lp-content-area .learn-press-courses[data-layout=grid] .course-info .course-readmore, .theme-edu-press:not(.nofixcss) .lp-archive-courses .lp-content-area .learn-press-courses[data-layout=grid] .course-info .course-readmore {
  width: auto;
}
.edu-press:not(.nofixcss) .lp-archive-courses .lp-content-area .learn-press-courses[data-layout=grid] .course-info .course-readmore a, .theme-edu-press:not(.nofixcss) .lp-archive-courses .lp-content-area .learn-press-courses[data-layout=grid] .course-info .course-readmore a {
  padding: 0;
}
.edu-press:not(.nofixcss) .lp-archive-courses .lp-content-area .learn-press-courses[data-layout=grid] .course-info .course-readmore a:hover, .theme-edu-press:not(.nofixcss) .lp-archive-courses .lp-content-area .learn-press-courses[data-layout=grid] .course-info .course-readmore a:hover {
  background: transparent;
  color: var(--thim-body_primary_color);
}
.edu-press:not(.nofixcss) .lp-archive-courses .lp-content-area .course-info, .theme-edu-press:not(.nofixcss) .lp-archive-courses .lp-content-area .course-info {
  border-top: 1px solid rgba(153, 153, 153, 0.2);
  padding-top: 20px;
}
.edu-press:not(.nofixcss) .lp-archive-courses .lp-content-area .learn-press-courses[data-layout=list] .course-info, .theme-edu-press:not(.nofixcss) .lp-archive-courses .lp-content-area .learn-press-courses[data-layout=list] .course-info {
  margin-top: 50px;
}
.edu-press:not(.nofixcss) .lp-archive-courses .lp-courses-bar .search-courses input, .theme-edu-press:not(.nofixcss) .lp-archive-courses .lp-courses-bar .search-courses input {
  height: unset;
  padding: 10px 20px;
}
.edu-press:not(.nofixcss) .lp-archive-courses .lp-courses-bar .search-courses button, .theme-edu-press:not(.nofixcss) .lp-archive-courses .lp-courses-bar .search-courses button {
  background-image: none !important;
}
.edu-press:not(.nofixcss) .lp-archive-courses .lp-courses-bar .search-courses button i, .theme-edu-press:not(.nofixcss) .lp-archive-courses .lp-courses-bar .search-courses button i {
  display: block;
}
.edu-press:not(.nofixcss) .lp-archive-courses .lp-content-area .lp-courses-bar .switch-layout .switch-btn, .theme-edu-press:not(.nofixcss) .lp-archive-courses .lp-content-area .lp-courses-bar .switch-layout .switch-btn {
  opacity: 1;
}
.edu-press:not(.nofixcss) .learn-press-courses .course-content .course-readmore a, .theme-edu-press:not(.nofixcss) .learn-press-courses .course-content .course-readmore a {
  border: none;
}
.edu-press:not(.nofixcss) .lp-user-profile .dashboard-statistic__row .statistic-box, .theme-edu-press:not(.nofixcss) .lp-user-profile .dashboard-statistic__row .statistic-box {
  background-color: var(--lp-white-grey, #F7F7FB);
  min-width: unset;
  margin-bottom: 0;
}
.edu-press:not(.nofixcss) .learn-press-courses .course-wrap-meta .meta-item::before, .theme-edu-press:not(.nofixcss) .learn-press-courses .course-wrap-meta .meta-item::before {
  font-family: "lp-icon" !important;
  font-weight: normal !important;
  margin: 0 !important;
}
.edu-press:not(.nofixcss).single-lp_course .lp-archive-courses .learn-press-nav-tabs, .theme-edu-press:not(.nofixcss).single-lp_course .lp-archive-courses .learn-press-nav-tabs {
  background: transparent;
  border-radius: 0;
}

.lp-4 {
  --lp-font-size-base: 1em;
}

.elearningwp:not(.nofixcss) .lp-courses-bar .switch-layout .switch-btn {
  border: none !important;
  background-color: unset !important;
  padding: 0;
}
.elearningwp:not(.nofixcss) .lp-content-area.learn-press-message {
  padding: 10px 15px;
}

.course-builder:not(.nofixcss) .lp-courses-bar .search-courses button {
  padding: 0;
}
.course-builder:not(.nofixcss) .lp-courses-bar .courses-order-by {
  height: unset !important;
  padding: 8px 35px 8px 15px !important;
}
.course-builder:not(.nofixcss) #learn-press-profile.lp-user-profile.no-bio-user #profile-sidebar {
  margin-top: 0 !important;
}
.course-builder:not(.nofixcss) #learn-press-profile.lp-user-profile.no-bio-user .lp-profile-content-area {
  align-items: center;
}
.course-builder:not(.nofixcss) .lp-courses-bar .switch-layout .switch-btn {
  border-radius: 5px;
  border: none;
  padding: 0;
}
.course-builder:not(.nofixcss) #learn-press-profile.lp-user-profile .wrapper-profile-header {
  padding-bottom: 30px;
}
.course-builder:not(.nofixcss) #learn-press-profile.lp-user-profile .wrapper-profile-header .lp-profile-user-bio {
  padding-bottom: 0;
}

.learnpress-v4:not(.nofixcss), .learnpress-v3:not(.nofixcss) {
  --lp-border-radius-avatar: 0;
}
.learnpress-v4:not(.nofixcss) .statistic-box, .learnpress-v3:not(.nofixcss) .statistic-box {
  --thim-lp-profile-bg-color: #F7F7FB;
}
.learnpress-v4:not(.nofixcss) #learn-press-profile.lp-user-profile .learn-press-filters, .learnpress-v3:not(.nofixcss) #learn-press-profile.lp-user-profile .learn-press-filters {
  border: none !important;
  margin: 0 0 0 -1px 0 !important;
}
.learnpress-v4:not(.nofixcss) .lp-user-profile .lp-profile-left, .learnpress-v3:not(.nofixcss) .lp-user-profile .lp-profile-left {
  min-width: unset !important;
  position: static !important;
}
.learnpress-v4:not(.nofixcss) #learn-press-profile .wrapper-profile-header .lp-profile-content-area, .learnpress-v3:not(.nofixcss) #learn-press-profile .wrapper-profile-header .lp-profile-content-area {
  min-height: unset;
  padding-left: 0;
  display: flex;
}
.learnpress-v4:not(.nofixcss) #learn-press-profile .wrapper-profile-header .lp-profile-left, .learnpress-v3:not(.nofixcss) #learn-press-profile .wrapper-profile-header .lp-profile-left {
  border: none !important;
}
.learnpress-v4:not(.nofixcss) #learn-press-profile .wrapper-profile-header .lp-profile-left img, .learnpress-v3:not(.nofixcss) #learn-press-profile .wrapper-profile-header .lp-profile-left img {
  border-radius: 50%;
}
.learnpress-v4 .lp-course-curriculum, .learnpress-v3 .lp-course-curriculum {
  --lp-primary-color: var(--thim-body-primary-color, inherit);
}
.learnpress-v4 .lp-course-curriculum .course-section-header .course-section__title, .learnpress-v4 .lp-course-curriculum .section-count-items, .learnpress-v3 .lp-course-curriculum .course-section-header .course-section__title, .learnpress-v3 .lp-course-curriculum .section-count-items {
  font-family: var(--thim-font-title-font-family, inherit);
}
.learnpress-v4 .lp-course-curriculum .course-item__link, .learnpress-v3 .lp-course-curriculum .course-item__link {
  color: inherit;
}
.learnpress-v4 .lp-course-curriculum .course-item-ico, .learnpress-v3 .lp-course-curriculum .course-item-ico {
  color: var(--lp-primary-color, #ffb606);
}
.learnpress-v4 .lp-course-curriculum .course-item__link, .learnpress-v3 .lp-course-curriculum .course-item__link {
  column-gap: 8px;
}
.learnpress-v4 .lp-course-curriculum .course-section__title, .learnpress-v3 .lp-course-curriculum .course-section__title {
  font-size: 1.1em;
}
.learnpress-v4 .lp-course-curriculum .section-toggle, .learnpress-v4 .lp-course-curriculum .section-count-items, .learnpress-v3 .lp-course-curriculum .section-toggle, .learnpress-v3 .lp-course-curriculum .section-count-items {
  color: var(--lp-primary-color, #ffb606);
}
.learnpress-v4 #popup-sidebar .course-section-header, .learnpress-v4 #popup-sidebar .lp-course-curriculum .course-item, .learnpress-v3 #popup-sidebar .course-section-header, .learnpress-v3 #popup-sidebar .lp-course-curriculum .course-item {
  padding-left: 16px;
  padding-right: 16px;
}

.learnpress-v4 .lp-course-curriculum {
  --lp-white-grey: transparent;
  --lp-border-radius: 0;
}
.learnpress-v4 .lp-course-curriculum .course-section {
  border-width: 0 0 1px 0;
}
.learnpress-v4 .lp-course-curriculum .course-section:last-child {
  border-width: 0;
}
.learnpress-v4 .lp-course-curriculum .course-section-header {
  padding: 0 0 8px 0;
}
.learnpress-v4 .lp-course-curriculum .course-item {
  padding-left: 0;
  padding-right: 0;
}
.learnpress-v4 .lp-course-curriculum .course-section {
  margin-bottom: 0;
}
.learnpress-v4 .lp-course-curriculum .course-section-header {
  padding: 24px 0 24px 0;
}

.assignments .lp-icon-file-alt:before {
  content: "\e929";
}

.eduma:not(.nofixcss) .learnpress #learn-press-profile.lp-user-profile .learn-press-filters {
  padding-bottom: 0 !important;
  overflow-y: visible;
}
.eduma:not(.nofixcss) #learn-press-profile .wrapper-profile-header .lp-profile-content-area {
  flex-direction: column;
}
.eduma .lp-course-curriculum .course-section-header {
  padding: var(--thim-ekit-padding-lesson, 12px 0);
}
.eduma .sc_heading + .lp-course-curriculum .lp-course-curriculum__title {
  display: none;
}

.ivy-school:not(.nofixcss) .lp-user-profile .lp-user-profile-avatar img {
  border-radius: 0;
}
.ivy-school.single-lp_course .course-curriculum {
  border: none;
}
.ivy-school .lp-course-curriculum {
  --lp-white-grey: transparent;
  --lp-border-radius: 0;
  --lp-primary-color: var(--thim-body-primary-color, inherit);
}
.ivy-school .lp-course-curriculum .course-section-header .course-section__title {
  font-family: var(--thim-font-title-font-family, inherit);
}
.ivy-school .lp-course-curriculum .course-item-ico {
  color: var(--lp-primary-color, #ffb606);
}

.course-builder .lp-course-curriculum {
  --lp-white-grey: #f2f2f2;
  --lp-border-color: #e6e6e6;
}
.course-builder .lp-course-curriculum .course-sections {
  border: 1px solid var(--lp-border-color, #e6e6e6);
  border-radius: var(--lp-border-radius, 5px);
  overflow: hidden;
}
.course-builder .lp-course-curriculum .course-section {
  margin: 0;
  border-radius: 0;
  border-width: 1px 0 0 0;
}
.course-builder .lp-course-curriculum .course-section:first-child {
  border: none;
}
.course-builder #popup-sidebar .lp-course-curriculum {
  --lp-border-radius: 0;
}
.course-builder .course-tab-panels .lp-course-curriculum .course-sections {
  border: none;
}
.course-builder .course-tab-panels .lp-course-curriculum .course-section-header, .course-builder .course-tab-panels .lp-course-curriculum .course-item {
  padding-left: 0;
  --lp-white-grey: trasparent;
  padding-right: 0;
}

.eduma .lp-course-curriculum .course-item-order.lp-hidden, .coaching .lp-course-curriculum .course-item-order.lp-hidden {
  display: inline-block !important;
}
.eduma .lp-course-curriculum .course-section__title, .coaching .lp-course-curriculum .course-section__title {
  text-transform: uppercase;
}

.demo-marketplace .thim-ekit-tablist .lp-course-curriculum__title, .demo-ecommerce .thim-ekit-tablist .lp-course-curriculum__title {
  display: none;
}
.demo-marketplace .thim-ekit-tablist .course-section-header, .demo-ecommerce .thim-ekit-tablist .course-section-header {
  background-color: var(--thim-breacrumb-bg-color);
}
.demo-marketplace .thim-ekit-tablist .course-section__items, .demo-ecommerce .thim-ekit-tablist .course-section__items {
  background-color: var(--lp-fix-bg-section-item, #F5F7F8);
}
.demo-marketplace .style-marketplace-2, .demo-ecommerce .style-marketplace-2 {
  --thim-breacrumb-bg-color: #0f1c44;
  --lp-fix-bg-section-item: transparent;
}
.thim-ekit-light-mode .demo-marketplace .style-marketplace-2, .thim-ekit-light-mode .demo-ecommerce .style-marketplace-2 {
  --thim-breacrumb-bg-color: #F4F4F4 ;
}

.demo-ecommerce .course-section-header {
  --thim-breacrumb-bg-color: #E1E9FD;
}

.demo-online-learning .thim-ekit-tablist .lp-course-curriculum__title {
  display: none;
}
.demo-online-learning .thim-ekit-tablist .course-section {
  margin-bottom: 8px;
  border: none;
}
.demo-online-learning .thim-ekit-tablist .course-section-header {
  background-color: #DFEFFF;
  border-radius: 8px 8px 8px 8px;
}

.demo-life-coaching .thim-ekit-tablist .lp-course-curriculum .course-section {
  border: none;
}
.demo-life-coaching .thim-ekit-tablist .lp-course-curriculum .course-item {
  border: 1px solid var(--thim-border-color);
  margin-bottom: 10px;
  padding-left: 20px;
  padding-right: 20px;
}

.business-consulting .lp-course-curriculum__title, .demo-business-v2 .lp-course-curriculum__title, .business-coaching .lp-course-curriculum__title, .demo-healthy-coaching .lp-course-curriculum__title {
  display: none;
}
.business-consulting .thim-ekit-tablist .lp-course-curriculum .course-section, .demo-business-v2 .thim-ekit-tablist .lp-course-curriculum .course-section, .business-coaching .thim-ekit-tablist .lp-course-curriculum .course-section, .demo-healthy-coaching .thim-ekit-tablist .lp-course-curriculum .course-section {
  border-width: 1px;
  margin-bottom: 15px;
}
.business-consulting .thim-ekit-tablist .lp-course-curriculum .course-section:last-child, .demo-business-v2 .thim-ekit-tablist .lp-course-curriculum .course-section:last-child, .business-coaching .thim-ekit-tablist .lp-course-curriculum .course-section:last-child, .demo-healthy-coaching .thim-ekit-tablist .lp-course-curriculum .course-section:last-child {
  border-width: 1px;
  margin-bottom: 0;
}
.business-consulting .thim-ekit-tablist .lp-course-curriculum .course-section-header, .demo-business-v2 .thim-ekit-tablist .lp-course-curriculum .course-section-header, .business-coaching .thim-ekit-tablist .lp-course-curriculum .course-section-header, .demo-healthy-coaching .thim-ekit-tablist .lp-course-curriculum .course-section-header {
  padding: 20px 15px 5px 15px;
}
.business-consulting .thim-ekit-tablist .lp-course-curriculum .course-item, .demo-business-v2 .thim-ekit-tablist .lp-course-curriculum .course-item, .business-coaching .thim-ekit-tablist .lp-course-curriculum .course-item, .demo-healthy-coaching .thim-ekit-tablist .lp-course-curriculum .course-item {
  padding-left: 15px;
  padding-right: 15px;
}

.demo-business-v2 .thim-ekit-tablist .lp-course-curriculum .course-section-header {
  background-color: var(--thim-bg-color, #f5f5f5);
  padding: 10px 15px;
}

.business-coaching .thim-ekit-tablist .lp-course-curriculum .course-section-header {
  padding: 15px 20px;
}
.business-coaching .thim-ekit-tablist .lp-course-curriculum .course-section__items {
  padding-left: 20px;
  padding-right: 20px;
}
.business-coaching .thim-ekit-tablist .lp-course-curriculum .course-item {
  padding-left: 0;
  padding-right: 0;
}

.demo-healthy-coaching .thim-ekit-tablist .lp-course-curriculum .course-section {
  border: none;
  border-radius: 5px;
}
.demo-healthy-coaching .thim-ekit-tablist .lp-course-curriculum .course-section-header {
  background: #fff;
  padding: 10px 15px;
}

.learn-press-tip {
  display: none;
  margin: 0 5px;
  color: #444;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  vertical-align: baseline;
  cursor: pointer;
}
.learn-press-tip.ready {
  display: inline-block;
}
.learn-press-tip::before {
  font-family: "lp-icon";
  content: "\f059";
}
.learn-press-tip:hover {
  opacity: 0.8;
}

.learn-press-tip-floating {
  position: absolute;
  z-index: 9999999;
  min-width: 20px;
  margin-left: -1px;
  padding: 0.618em 1em;
  color: #fff;
  background: #383838;
  font-size: 0.8em;
  line-height: 1.2em;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.learn-press-tip-floating > * {
  font-size: 0.8em;
}
.learn-press-tip-floating .close {
  display: inline-block;
  position: absolute;
  top: -8px;
  right: -8px;
  width: 16px;
  height: 16px;
  border: 1px solid #fff;
  color: #fff;
  background: #468fbc;
  line-height: 1rem;
  text-align: center;
  cursor: pointer;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
}
.learn-press-tip-floating .close::before {
  font-family: "lp-icon";
  content: "\f00d";
}
.learn-press-tip-floating p {
  margin: 0;
}
.learn-press-tip-floating::before {
  position: absolute;
  bottom: -6px;
  left: 50%;
  width: 0;
  height: 0;
  margin-left: -6px;
  border: 6px solid transparent;
  border-top-color: #383838;
  border-bottom-width: 0;
  content: "";
}

body.lp-preview.admin-bar #learn-press-content-item {
  top: 32px !important;
}
body.lp-preview #learn-press-course-curriculum {
  display: none;
}
body.lp-preview #learn-press-content-item {
  top: 0 !important;
  left: 0 !important;
}

/**
 * CSS for jAlerts
 */
/**
 * Mixin
 */
@-webkit-keyframes rotating4 {
  from {
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes rotating4 {
  from {
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes animation4 {
  from {
    left: -40%;
    width: 40%;
  }
  to {
    left: 100%;
    width: 10%;
  }
}
@keyframes animation4 {
  from {
    left: -40%;
    width: 40%;
  }
  to {
    left: 100%;
    width: 10%;
  }
}
#popup_container {
  opacity: 0;
  transform: scale(0.5);
}

body.confirm #popup_overlay {
  z-index: 999998 !important;
}
body.confirm #popup_container {
  z-index: 999999 !important;
  max-width: 90% !important;
  min-width: 300px !important;
  padding: 10px !important;
  background: #F5F5F5;
  transition: opacity 0.25s;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
body.confirm #popup_container #popup_title {
  display: none !important;
}
body.confirm #popup_container #popup_message {
  margin: -10px;
  background: #FFF;
  padding: 20px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
body.confirm #popup_container .close {
  position: absolute;
  top: 3px;
  right: 10px;
}
body.confirm #popup_container #popup_panel {
  margin-top: 20px;
  text-align: center;
}
body.confirm #popup_container #popup_panel button, body.confirm #popup_container #popup_panel input[type=button], body.confirm #popup_container #popup_panel input[type=submit] {
  height: 30px;
  line-height: 1.875rem;
  padding: 0 25px;
}
body.confirm #popup_container #popup_cancel {
  display: none;
}
body.confirm #popup_container.ready {
  opacity: 1;
  transform: scale(1);
}

/**
 * Archive courses page.
 */
.lp-archive-courses {
  width: 100%;
  scroll-margin: 30px;
}
.lp-archive-courses .lp-content-area.has-sidebar {
  display: flex;
  gap: 32px;
  align-items: flex-start;
}
@media (max-width: 768px) {
  .lp-archive-courses .lp-content-area.has-sidebar {
    display: inherit;
  }
}
.lp-archive-courses .lp-content-area.has-sidebar .learn-press-courses[data-layout=grid] li {
  width: 33.33%;
}
@media (max-width: 1200px) {
  .lp-archive-courses .lp-content-area.has-sidebar .learn-press-courses[data-layout=grid] li {
    width: 50%;
  }
}
@media (max-width: 600px) {
  .lp-archive-courses .lp-content-area.has-sidebar .learn-press-courses[data-layout=grid] li {
    width: 100%;
  }
}
.lp-archive-courses .lp-content-area .lp-main-content {
  flex: 1;
}
.lp-archive-courses .lp-content-area .lp-archive-courses-sidebar {
  width: 25%;
}
@media (max-width: 768px) {
  .lp-archive-courses .lp-content-area .lp-archive-courses-sidebar {
    width: 100%;
  }
}
.lp-archive-courses.loading ul.learn-press-courses {
  position: relative;
}
.lp-archive-courses.loading ul.learn-press-courses::before, .lp-archive-courses.loading ul.learn-press-courses::after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 15px;
  right: 15px;
}
.lp-archive-courses.loading ul.learn-press-courses::after {
  z-index: 3;
  background: #f3f3f3;
  background: linear-gradient(90deg, hsla(0, 0%, 74.5%, 0.2) 25%, hsla(0, 0%, 50.6%, 0.24) 37%, hsla(0, 0%, 74.5%, 0.2) 63%);
  background-size: 400% 100%;
  list-style: none;
  animation: lp-skeleton-loading 1.4s ease infinite;
}
.lp-archive-courses.loading ul.learn-press-courses::before {
  z-index: 2;
  opacity: 0.8%;
  background: var(--lp-color-white, #fff);
}
.lp-archive-courses .learn-press-courses-header {
  margin-bottom: 16px;
}
.lp-archive-courses .learn-press-courses-header h1 {
  margin: 0;
  font-size: calc(var(--lp-font-size-base, 1em) * 1.8);
}

.learn-press-courses {
  clear: both;
  margin: 0 -16px;
  padding: 0 !important;
  min-height: 300px;
  list-style: none;
  position: relative;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -moz-flex;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
}
.learn-press-courses .course-item {
  flex-direction: column;
  display: flex;
  transition: all 0.3s;
}
.learn-press-courses .course-item:hover .course-thumbnail .thumbnail::before {
  opacity: 1;
}
.learn-press-courses .course-item:hover .course-thumbnail a img {
  transform: scale(1.05);
}
.learn-press-courses .course-item:hover .course-wishlist {
  opacity: 1;
}
.learn-press-courses .course-item .course-wishlist.filled {
  opacity: 1;
}
.learn-press-courses .course-content {
  position: relative;
  border-top: 0;
}
.learn-press-courses .course-content .course-permalink {
  display: block;
  border: none;
  text-decoration: none;
  color: inherit;
  line-height: 1.3;
}
.learn-press-courses .course-content .course-review {
  display: none;
}
.learn-press-courses .course-content .course-tags {
  display: inline-block;
}
.learn-press-courses .course-content .course-info > span {
  display: block;
}
.learn-press-courses .course-content .course-info .course-price .free,
.learn-press-courses .course-content .course-info .course-price .price {
  font-size: calc(var(--lp-font-size-base, 1em) * 1.25);
  font-weight: var(--lp-font-weight-link, 600);
}
.learn-press-courses .course-content .course-info .course-price .origin-price {
  margin-right: 8px;
  opacity: 0.8;
  font-size: calc(var(--lp-font-size-base, 1em) * 0.925);
  text-decoration: line-through;
}
.learn-press-courses .course-content .course-short-description {
  display: -webkit-box;
  overflow: hidden;
  margin-bottom: 16px;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.learn-press-courses .course-content .separator {
  display: none;
}
.learn-press-courses .course-thumbnail {
  overflow: hidden;
  position: relative;
  border-radius: var(--lp-border-radius, 5px);
}
.learn-press-courses .course-thumbnail .lp-badge.featured {
  position: absolute;
  z-index: 100;
  top: 28px;
  left: -110px;
  width: 300px;
  text-align: center;
  transform: rotate(-45deg);
}
.learn-press-courses .course-thumbnail .course-wishlist {
  position: absolute;
  z-index: 2;
  top: 6px;
  right: 6px;
  opacity: 0;
  color: var(--lp-color-white, #fff);
  cursor: pointer;
}
.learn-press-courses .course-thumbnail .course-wishlist::before {
  font-family: "lp-icon";
  content: "\f004";
}
.learn-press-courses .course-thumbnail .course-wishlist .course-wishlist__btn {
  display: none;
}
.learn-press-courses .course-thumbnail .course-wishlist:hover {
  color: var(--lp-primary-color);
}
.learn-press-courses .course-thumbnail:hover::before {
  opacity: 1;
}
.learn-press-courses .course-thumbnail:hover a img {
  transform: scale(1.05);
}
.learn-press-courses .course-thumbnail:hover .course-wishlist {
  opacity: 1;
}
.learn-press-courses .course-thumbnail a {
  display: block;
  overflow: hidden;
}
.learn-press-courses .course-thumbnail .thumbnail {
  margin: 0;
  padding: 0;
  border: 0;
  border-radius: unset;
  line-height: unset;
}
.learn-press-courses .course-thumbnail img {
  display: block;
  width: 100%;
  height: auto;
  transition: all 0.5s ease;
  transform: scale(1);
  max-width: unset !important;
}
.learn-press-courses .wap-course-title {
  margin: 0 0 12px 0;
  padding: 0;
  display: block;
  font-size: calc(var(--lp-font-size-base, 1em) * 1.15);
  font-weight: var(--lp-font-weight-link, 600);
}
.learn-press-courses .wap-course-title a:hover {
  color: var(--lp-primary-color);
}
.learn-press-courses .course-permalink:hover {
  color: var(--lp-primary-color);
}
@media (min-width: 769px) {
  .learn-press-courses .course-summary-content .course-info-left {
    width: calc(100% - 340px);
  }
}
@media (max-width: 1024px) {
  .learn-press-courses .course-summary-content .course-meta.course-meta-primary .course-meta__pull-left .meta-item .meta-item__value span,
  .learn-press-courses .course-summary-content .course-meta.course-meta-primary .course-meta__pull-left .meta-item .meta-item__value > div {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
.learn-press-courses .course-summary-content .course-meta.course-meta-secondary {
  margin-bottom: 0;
}
.learn-press-courses .course-summary-content .course-meta.course-meta-secondary .course-meta__pull-left .meta-item {
  height: auto;
}
.learn-press-courses .course-summary-content .course-meta.course-meta-secondary .course-meta__pull-left .meta-item:last-child {
  margin-right: 0;
}
@media (max-width: 1024px) {
  .learn-press-courses .course-summary-content .course-meta.course-meta-secondary .course-meta__pull-left .meta-item {
    margin-right: 0 !important;
    width: calc((100% - 32px) / 3);
  }
}
@media (max-width: 768px) {
  .learn-press-courses .course-summary-content .course-meta.course-meta-secondary .course-meta__pull-left .meta-item {
    width: 100%;
  }
}
@media (max-width: 768px) {
  .learn-press-courses .course-summary-content {
    width: 100%;
  }
}
.learn-press-courses .course-price .free {
  color: #3ab500;
}
.learn-press-courses .course {
  width: 25%;
  margin: 0 0 32px 0;
  box-shadow: none;
  list-style: none;
}
.learn-press-courses .course-info {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}
.learn-press-courses .course-instructor-category {
  display: flex;
  padding: 0;
  gap: 4px;
  flex-wrap: wrap;
  margin-bottom: 12px;
}
.learn-press-courses .course-instructor-category > div > * {
  display: inline;
  vertical-align: middle;
}
.learn-press-courses .course-instructor-category label {
  font-size: inherit;
  font-weight: inherit;
}
.learn-press-courses .course-instructor-category a {
  font-weight: var(--lp-font-weight-link, 600);
  text-decoration: none;
  color: inherit;
}
.learn-press-courses .course-instructor-category a:hover {
  color: var(--lp-primary-color, #ffb606);
}
.learn-press-courses .course-wrap-meta {
  display: flex;
  padding: 0;
  margin-bottom: 12px;
  gap: 20px;
  flex-wrap: wrap;
  row-gap: 8px;
}
.learn-press-courses .course-wrap-meta .meta-item {
  text-transform: capitalize;
  display: flex;
  gap: 8px;
}
.learn-press-courses .course-wrap-meta .meta-item::before {
  color: var(--lp-primary-color);
  font-family: "lp-icon";
}
.learn-press-courses .course-wrap-meta .meta-item > div {
  display: inline-block;
}
.learn-press-courses .course-wrap-meta .meta-item-level::before {
  content: "\f012";
}
.learn-press-courses .course-wrap-meta .meta-item-duration::before {
  content: "\f017";
}
.learn-press-courses .course-wrap-meta .meta-item-lesson::before {
  content: "\f15b";
}
.learn-press-courses .course-wrap-meta .meta-item-quiz::before {
  content: "\f12e";
}
.learn-press-courses .course-wrap-meta .meta-item-student::before {
  content: "\f501";
}
.learn-press-courses .course-wrap-meta .meta-item-address::before {
  content: "\e91b";
}
.learn-press-courses .course-content .course-readmore a {
  padding: 8px 24px;
  border-radius: var(--lp-border-radius, 5px);
  color: var(--lp-color-base, #333);
  border: 1px solid var(--lp-color-base, #333);
  transition: all 0.3s;
  display: block;
  background: transparent;
  text-decoration: none;
  text-align: center;
}
.learn-press-courses .course-content .course-readmore a:hover {
  background: var(--lp-primary-color, #ffb606);
  color: var(--lp-color-white, #fff);
  border-color: var(--lp-primary-color, #ffb606);
}
.learn-press-courses[data-layout=list] {
  margin: 0;
}
.learn-press-courses[data-layout=list] .course {
  display: flex;
  width: 100%;
  border-bottom: 1px solid var(--lp-border-color, #E2E0DB);
  flex-wrap: wrap;
  justify-content: flex-end;
  padding-bottom: 32px;
}
.learn-press-courses[data-layout=list] .course-item {
  flex-direction: row;
  width: 100%;
  border: unset;
  align-items: start;
}
@media (max-width: 767px) {
  .learn-press-courses[data-layout=list] .course-item {
    flex-direction: column;
  }
}
.learn-press-courses[data-layout=list] .course-content {
  padding: 0 0 0 24px;
}
@media (max-width: 767px) {
  .learn-press-courses[data-layout=list] .course-content {
    padding: 16px 0;
  }
}
.learn-press-courses[data-layout=list] .course-content .course-tags a {
  margin: 0 10px 10px 0;
  padding: 3px 5px;
  border-radius: 3px;
  color: var(--lp-color-white, #fff);
  background: #e4a2a2;
  line-height: 1;
}
.learn-press-courses[data-layout=list] .course-content .course-excerpt {
  width: 100%;
}
.learn-press-courses[data-layout=list] .course-content .course-info {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
@media (max-width: 520px) {
  .learn-press-courses[data-layout=list] .course-content .course-info {
    width: 100%;
  }
}
.learn-press-courses[data-layout=list] .course-content .course-info .course-students {
  display: none;
}
.learn-press-courses[data-layout=list] .course-thumbnail {
  width: 35%;
}
.learn-press-courses[data-layout=list] .course-content {
  width: 65%;
}
@media (max-width: 767px) {
  .learn-press-courses[data-layout=list] .course-thumbnail,
  .learn-press-courses[data-layout=list] .course-content {
    width: 100%;
  }
}
.learn-press-courses[data-layout=list] .course-title {
  font-size: calc(var(--lp-font-size-base, 1em) * 1.25);
}
.learn-press-courses[data-layout=grid] .course, .learn-press-courses.lp-courses-related .course {
  padding: 0 16px;
}
@media (max-width: 992px) {
  .learn-press-courses[data-layout=grid] .course, .learn-press-courses.lp-courses-related .course {
    width: 50%;
  }
}
@media (max-width: 767px) {
  .learn-press-courses[data-layout=grid] .course, .learn-press-courses.lp-courses-related .course {
    width: 100%;
  }
}
.learn-press-courses[data-layout=grid] .wp-block-learnpress-course-item-template,
.learn-press-courses[data-layout=grid] .course-item, .learn-press-courses.lp-courses-related .wp-block-learnpress-course-item-template,
.learn-press-courses.lp-courses-related .course-item {
  border: 1px solid var(--lp-border-color, #E2E0DB);
  border-radius: var(--lp-border-radius, 5px);
}
.learn-press-courses[data-layout=grid] .course-wrap-meta .wp-block-learnpress-course-duration,
.learn-press-courses[data-layout=grid] .course-wrap-meta .wp-block-learnpress-course-level,
.learn-press-courses[data-layout=grid] .course-wrap-meta .wp-block-learnpress-course-quiz,
.learn-press-courses[data-layout=grid] .course-wrap-meta .meta-item-duration,
.learn-press-courses[data-layout=grid] .course-wrap-meta .meta-item-level,
.learn-press-courses[data-layout=grid] .course-wrap-meta .meta-item-quiz, .learn-press-courses.lp-courses-related .course-wrap-meta .wp-block-learnpress-course-duration,
.learn-press-courses.lp-courses-related .course-wrap-meta .wp-block-learnpress-course-level,
.learn-press-courses.lp-courses-related .course-wrap-meta .wp-block-learnpress-course-quiz,
.learn-press-courses.lp-courses-related .course-wrap-meta .meta-item-duration,
.learn-press-courses.lp-courses-related .course-wrap-meta .meta-item-level,
.learn-press-courses.lp-courses-related .course-wrap-meta .meta-item-quiz {
  display: none;
}
.learn-press-courses[data-layout=grid] .course-content, .learn-press-courses.lp-courses-related .course-content {
  width: 100%;
  padding: 20px;
  flex-grow: 1;
}
.learn-press-courses[data-layout=grid] .course-content .course-info, .learn-press-courses.lp-courses-related .course-content .course-info {
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
}
.learn-press-courses[data-layout=grid] .course-content .course-info .course-readmore, .learn-press-courses.lp-courses-related .course-content .course-info .course-readmore {
  width: 100%;
  text-align: center;
}
.learn-press-courses[data-layout=grid] .course-content .course-excerpt,
.learn-press-courses[data-layout=grid] .course-content .course-short-description, .learn-press-courses.lp-courses-related .course-content .course-excerpt,
.learn-press-courses.lp-courses-related .course-content .course-short-description {
  display: none;
}
.learn-press-courses[data-layout=grid] .learn-press-message, .learn-press-courses.lp-courses-related .learn-press-message {
  margin-left: 15px;
  margin-right: 15px;
}
.learn-press-courses[data-layout=grid] .course-thumbnail, .learn-press-courses.lp-courses-related .course-thumbnail {
  border-radius: var(--lp-border-radius, 5px) var(--lp-border-radius, 5px) 0 0;
}
.learn-press-courses[data-size="3"] .course {
  width: 33.3333%;
}
@media (max-width: 767px) {
  .learn-press-courses[data-size="3"] .course {
    width: 100%;
  }
}
.learn-press-courses .lp-archive-course-skeleton {
  position: absolute;
  height: 100%;
  background: linear-gradient(90deg, hsla(0, 0%, 74.5%, 0.2) 25%, hsla(0, 0%, 50.6%, 0.24) 37%, hsla(0, 0%, 74.5%, 0.2) 63%);
  animation: lp-skeleton-loading 1.4s ease infinite;
  background-size: 400% 100%;
}
.learn-press-courses .lp-archive-course-skeleton li {
  display: none;
}

.lp-list-courses-default {
  position: relative;
}
.lp-list-courses-default .lp-skeleton-animation {
  clear: both;
  padding: 5px 0 0 0;
}

.lp-courses-bar {
  display: flex;
  width: 100%;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 32px;
  flex-wrap: wrap;
}
.lp-courses-bar .courses-order-by {
  border: 1px solid var(--lp-border-color, #E2E0DB);
  border-radius: var(--lp-border-radius, 5px);
  padding: 8px 16px;
  height: inherit;
  font-size: inherit;
  line-height: 1.1;
  box-shadow: none;
  padding-right: 30px;
  background-image: url(data:image/svg+xml;base64,Cjxzdmcgd2lkdGg9IjE4cHgiIGhlaWdodD0iMTBweCIgdmlld0JveD0iMCAwIDE4IDEwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj4KICAgICAgICA8ZyBpZD0iVmVjdG9yLSgxKSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMS4wMDAwMDAsIDEuMDAwMDAwKSIgc3Ryb2tlPSIjMzMzMzMzIiBzdHJva2Utd2lkdGg9IjIiPgogICAgICAgICAgICA8cG9seWxpbmUgaWQ9IlBhdGgiIHBvaW50cz0iMCAwIDggOCAxNiAwIj48L3BvbHlsaW5lPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+);
  background-size: 0.8em;
  background-position: calc(100% - 0.5em) center;
  background-repeat: no-repeat;
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  line-height: 1.1;
}
.woocommerce-js .lp-courses-bar .courses-order-by {
  background-position-x: calc(100% - 10px);
}
.lp-courses-bar .courses-order-by:focus {
  border-color: var(--lp-primary-color);
  outline: 0;
}
.lp-courses-bar > * {
  display: flex;
}
.lp-courses-bar .search-courses {
  display: flex;
  flex: 1;
  margin-bottom: 0;
}
.lp-courses-bar .search-courses input {
  width: 100%;
  max-width: 240px;
  margin: 0 4px 0 0;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  border-radius: var(--lp-border-radius, 5px);
  padding: 8px 16px;
  height: inherit;
  font-size: inherit;
  line-height: 1.1;
  box-shadow: none;
}
.lp-courses-bar .search-courses input::-webkit-input-placeholder {
  color: #999;
}
.lp-courses-bar .search-courses input::-moz-placeholder {
  color: #999;
}
.lp-courses-bar .search-courses input:-ms-input-placeholder {
  color: #999;
}
.lp-courses-bar .search-courses input:-moz-placeholder {
  color: #999;
}
.lp-courses-bar .search-courses input::placeholder {
  color: #999;
}
.lp-courses-bar .search-courses input:focus {
  border-color: var(--lp-primary-color);
  outline: 0;
}
.lp-courses-bar .search-courses button {
  border: 1px solid var(--lp-border-color, #E2E0DB);
  border-radius: var(--lp-border-radius, 5px);
  padding: 8px 16px;
  height: inherit;
  font-size: inherit;
  line-height: 1.1;
  box-shadow: none;
  box-shadow: 0 0 0;
}
.lp-courses-bar .search-courses button i {
  font-size: 1.2em;
  line-height: 1.2;
}
.lp-courses-bar .search-courses button:focus {
  outline: 0;
}
.lp-courses-bar .search-courses button.loading > i:before {
  display: inline-block;
  content: "\f110";
  -webkit-animation: lp-rotating 1s linear infinite;
  -moz-animation: lp-rotating 1s linear infinite;
  animation: lp-rotating 1s linear infinite;
}
.lp-courses-bar .switch-layout {
  align-items: center;
  justify-content: center;
}
@media (max-width: 768px) {
  .lp-courses-bar .switch-layout {
    display: none;
  }
}
.lp-courses-bar .switch-layout input[type=radio] {
  display: none;
}
.lp-courses-bar .switch-layout .switch-btn {
  margin: 0;
  cursor: pointer;
  padding: 8px 12px;
}
.lp-courses-bar .switch-layout .switch-btn::before {
  display: inline-block;
  margin: 6px;
  color: var(--lp-color-base, #333);
  font-family: "lp-icon";
  line-height: 1.2;
  width: 1em;
}
.lp-courses-bar .switch-layout .switch-btn.grid {
  border-radius: var(--lp-border-radius, 5px) 0 0 var(--lp-border-radius, 5px);
  border: 1px solid var(--lp-border-color, #E2E0DB);
  border-right: none;
}
.lp-courses-bar .switch-layout .switch-btn.grid::before {
  content: "\f009";
}
.lp-courses-bar .switch-layout .switch-btn.list {
  border-radius: 0 var(--lp-border-radius, 5px) var(--lp-border-radius, 5px) 0;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  border-left: none;
}
.lp-courses-bar .switch-layout .switch-btn.list::before {
  content: "\f03a";
}
.lp-courses-bar .course-filter-btn-mobile {
  display: inline-flex;
  align-items: center;
}
@media (min-width: 769px) {
  .lp-courses-bar .course-filter-btn-mobile {
    display: none;
  }
}
@media (max-width: 767px) {
  .lp-courses-bar .search-courses {
    width: 100%;
  }
  .lp-courses-bar .search-courses input {
    min-width: 240px;
    max-width: unset;
  }
}

.switch-layout input[type=radio]:nth-child(1):checked ~ .switch-btn:nth-child(2) {
  background-color: var(--lp-white-grey, #F7F7FB);
}
input[type=radio]:nth-child(1):checked ~ .switch-btn:nth-child(2)::before {
  color: var(--lp-primary-color);
}

.switch-layout input[type=radio]:nth-child(3):checked ~ .switch-btn:nth-child(4) {
  background-color: var(--lp-white-grey, #F7F7FB);
}
input[type=radio]:nth-child(3):checked ~ .switch-btn:nth-child(4)::before {
  color: var(--lp-primary-color);
}

.switch-layout input[type=radio]:nth-child(5):checked ~ .switch-btn:nth-child(6) {
  background-color: var(--lp-white-grey, #F7F7FB);
}
input[type=radio]:nth-child(5):checked ~ .switch-btn:nth-child(6)::before {
  color: var(--lp-primary-color);
}

.switch-layout input[type=radio]:nth-child(7):checked ~ .switch-btn:nth-child(8) {
  background-color: var(--lp-white-grey, #F7F7FB);
}
input[type=radio]:nth-child(7):checked ~ .switch-btn:nth-child(8)::before {
  color: var(--lp-primary-color);
}

.switch-layout input[type=radio]:nth-child(9):checked ~ .switch-btn:nth-child(10) {
  background-color: var(--lp-white-grey, #F7F7FB);
}
input[type=radio]:nth-child(9):checked ~ .switch-btn:nth-child(10)::before {
  color: var(--lp-primary-color);
}

.switch-layout input[type=radio]:nth-child(11):checked ~ .switch-btn:nth-child(12) {
  background-color: var(--lp-white-grey, #F7F7FB);
}
input[type=radio]:nth-child(11):checked ~ .switch-btn:nth-child(12)::before {
  color: var(--lp-primary-color);
}

.switch-layout input[type=radio]:nth-child(13):checked ~ .switch-btn:nth-child(14) {
  background-color: var(--lp-white-grey, #F7F7FB);
}
input[type=radio]:nth-child(13):checked ~ .switch-btn:nth-child(14)::before {
  color: var(--lp-primary-color);
}

.switch-layout input[type=radio]:nth-child(15):checked ~ .switch-btn:nth-child(16) {
  background-color: var(--lp-white-grey, #F7F7FB);
}
input[type=radio]:nth-child(15):checked ~ .switch-btn:nth-child(16)::before {
  color: var(--lp-primary-color);
}

.switch-layout input[type=radio]:nth-child(17):checked ~ .switch-btn:nth-child(18) {
  background-color: var(--lp-white-grey, #F7F7FB);
}
input[type=radio]:nth-child(17):checked ~ .switch-btn:nth-child(18)::before {
  color: var(--lp-primary-color);
}

.switch-layout input[type=radio]:nth-child(19):checked ~ .switch-btn:nth-child(20) {
  background-color: var(--lp-white-grey, #F7F7FB);
}
input[type=radio]:nth-child(19):checked ~ .switch-btn:nth-child(20)::before {
  color: var(--lp-primary-color);
}

/**
 *  End Archive courses page.
 */
.lp_allow_repurchase_select {
  padding: 20px;
  background: #f7f7f7;
}
.lp_allow_repurchase_select > ul {
  padding: 0;
  list-style: none;
}
.lp_allow_repurchase_select > ul li label {
  display: flex;
  align-items: center;
  gap: 10px;
}
.lp_allow_repurchase_select > a {
  display: inline-block;
  margin-top: 10px;
  color: var(--lp-color-white, #fff);
  background-color: #222;
}

.lp-single-offline-course .lp-single-offline-course-main {
  display: flex;
  gap: 30px;
  margin-bottom: 80px;
}
.lp-single-offline-course .course-price .free {
  color: #3AB500;
}
.lp-single-offline-course .course-price .origin-price {
  text-decoration: line-through;
  opacity: 0.6;
  margin-right: 4px;
}
.lp-single-offline-course__left {
  width: 70%;
}
.lp-single-offline-course__left > div {
  margin-bottom: 32px;
}
.lp-single-offline-course__left .learn-press-breadcrumb {
  display: flex;
  list-style: none;
  flex-wrap: wrap;
  gap: 5px;
  font-size: var(--lp-font-size-base);
  align-items: center;
  margin-bottom: 8px;
  padding: 0;
}
.lp-single-offline-course__left .learn-press-breadcrumb li {
  list-style: none;
  padding: 0;
  margin: 0;
}
.lp-single-offline-course__left .course-title {
  display: block;
  font-weight: 700;
  font-size: calc(var(--lp-font-size-base, 1em) * 1.8);
  margin: 0 0 12px 0;
}
.lp-single-offline-course__left .course-img img {
  width: 100%;
}
.lp-single-offline-course__left .lp-single-course-offline-info-one {
  margin-bottom: 12px;
}
.lp-single-offline-course__left .lp-single-course-offline-info-one .item-meta {
  display: inline;
  line-height: 1.4;
  margin-left: 12px;
  padding-left: 12px;
  border-left: 1px solid #E2E0DB;
}
.lp-single-offline-course__left .lp-single-course-offline-info-one .item-meta:first-child {
  margin: 0;
  padding: 0;
  border: none;
}
.lp-single-offline-course__left .lp-single-course-offline-info-one .lp-item-wishlist.active {
  color: var(--lp-primary-color);
}
.lp-single-offline-course__left .lp-single-course-offline-info-one .star-info {
  display: inline-block;
}
.lp-single-offline-course__left .lp-single-course-offline-info-one .star-info .ico-star {
  display: inline-block;
  vertical-align: sub;
  margin-right: 4px;
}
.lp-single-offline-course__left .lp-single-course-offline-info-one .star-info svg {
  height: 22px;
}
.lp-single-offline-course__left .instructor-description {
  margin-top: 16px;
}
.lp-single-offline-course__left .course-features ul,
.lp-single-offline-course__left .course-target ul,
.lp-single-offline-course__left .course-requirements ul {
  margin: 0 0 0 20px;
  padding: 0;
}
.lp-single-offline-course__left .instructor-social {
  display: flex;
  gap: 8px;
  margin-top: 30px;
}
.lp-single-offline-course__left .instructor-social a {
  width: 37px;
  height: 37px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #F1F2F8;
  border-radius: 50%;
}
.lp-single-offline-course__left .instructor-social a:hover {
  background-color: var(--lp-primary-color);
}
.lp-single-offline-course__left .instructor-social a:hover i {
  color: #fff;
}
.lp-single-offline-course__left .instructor-social a .lp-icon-facebook {
  color: #216BF6;
}
.lp-single-offline-course__left .instructor-social a .lp-icon-twitter {
  color: #169DF0;
}
.lp-single-offline-course__left .instructor-social a .lp-icon-youtube-play {
  color: #FF0200;
}
.lp-single-offline-course__left .instructor-social a .lp-icon-linkedin {
  color: #0078B7;
}
.lp-single-offline-course__left .instructor-social a i {
  font-size: 18px;
}
.lp-single-offline-course__left .item-title {
  margin-bottom: 30px;
}
.lp-single-offline-course__right {
  width: 30%;
  position: relative;
}
.lp-single-offline-course__right__sticky {
  position: sticky;
  top: 0;
}
.lp-single-offline-course .info-metas,
.lp-single-offline-course .course-featured-review {
  border: 1px solid #E2E0DB;
  padding: 30px;
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .lp-single-offline-course .info-metas,
  .lp-single-offline-course .course-featured-review {
    margin-bottom: 30px;
  }
}
.lp-single-offline-course .info-metas .info-meta-item,
.lp-single-offline-course .course-featured-review .info-meta-item {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #E2E0DB;
  padding-bottom: 16px;
  margin-bottom: 16px;
  font-size: var(--lp-font-size-base);
}
.lp-single-offline-course .info-metas .info-meta-item:nth-last-of-type(2),
.lp-single-offline-course .course-featured-review .info-meta-item:nth-last-of-type(2) {
  border: none;
  padding-bottom: 0;
  margin-bottom: 0;
}
.lp-single-offline-course .info-metas .info-meta-left,
.lp-single-offline-course .course-featured-review .info-meta-left {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}
.lp-single-offline-course .info-metas .info-meta-left > span,
.lp-single-offline-course .course-featured-review .info-meta-left > span {
  min-width: 16px;
}
.lp-single-offline-course form.purchase-course, .lp-single-offline-course form.enroll-course, .lp-single-offline-course form.retake-course {
  width: 100%;
}
.lp-single-offline-course .course-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 30px;
}
.lp-single-offline-course .course-buttons .lp-button {
  padding: 12px 16px;
  border-radius: var(--lp-border-radius, 3px);
  font-size: var(--button--font-size);
  line-height: 1.5em;
  font-weight: 600;
  width: 100%;
}
.lp-single-offline-course .course-buttons .lp-button.button {
  border: 0;
}
.lp-single-offline-course .course-buttons a.course-btn-extra {
  border-color: var(--lp-primary-color);
  color: var(--lp-primary-color);
}
.lp-single-offline-course .course-buttons a.course-btn-extra:focus, .lp-single-offline-course .course-buttons a.course-btn-extra:hover {
  background-color: var(--lp-primary-color);
  color: #fff;
}
.lp-single-offline-course .course-buttons .button-purchase-course {
  background-color: var(--lp-primary-color);
  color: #fff;
}
.lp-single-offline-course .featured-review__title {
  font-weight: bold;
  font-size: 1.4rem;
  margin-top: 10px;
}
.lp-single-offline-course .course-featured-review {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  gap: 16px;
  position: relative;
}
.lp-single-offline-course .course-featured-review::before {
  background-image: url("data:image/svg+xml,%3Csvg width='56' height='56' viewBox='0 0 56 56' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='56' height='56' rx='28' fill='%23F2F1F8'/%3E%3Cpath d='M17.425 40.5325C17.3136 40.5325 17.2015 40.5061 17.0958 40.4518C16.7823 40.2903 16.6073 39.9215 16.6673 39.5504L18.2548 31.142H12.7691C12.3442 31.142 12 30.7678 12 30.3058V15.5028C12 15.0409 12.3442 14.6666 12.7691 14.6666H25.8927C26.3176 14.6666 26.6618 15.0409 26.6618 15.5028V30.3051C26.6618 30.5046 26.5961 30.6979 26.4761 30.8501L18.0084 40.2406C17.8584 40.43 17.6442 40.5325 17.425 40.5325ZM13.5389 29.4689H19.1731C19.4009 29.4689 19.6165 29.5783 19.7629 29.7678C19.9093 29.9572 19.9707 30.2072 19.9307 30.451L18.6739 36.8314L25.1243 29.9953V16.3398H13.5389V29.4689Z' fill='%23121212'/%3E%3Cpath d='M34.7609 40.5325C34.6495 40.5325 34.5374 40.5061 34.4317 40.4518C34.1182 40.2903 33.9433 39.9215 34.0033 39.5496L35.5907 31.1413H30.105C29.6801 31.1413 29.3359 30.767 29.3359 30.3051V15.5028C29.3359 15.0409 29.6801 14.6666 30.105 14.6666H43.2286C43.4329 14.6666 43.6285 14.7551 43.7728 14.912C43.917 15.0688 43.9977 15.2815 43.9977 15.5036V30.3058C43.9977 30.5054 43.9313 30.6987 43.8113 30.8509L35.3436 40.2414C35.1937 40.4308 34.9787 40.5333 34.7595 40.5333L34.7609 40.5325ZM30.8748 29.4689H36.509C36.7368 29.4689 36.9525 29.5783 37.0989 29.7678C37.2452 29.9572 37.3067 30.2072 37.2667 30.451L36.0099 36.8306L42.4603 29.9953V16.3398H30.8755L30.8748 29.4689Z' fill='%23121212'/%3E%3C/svg%3E%0A");
  content: "";
  width: 56px;
  height: 56px;
  position: absolute;
  top: -25px;
}
.lp-single-offline-course .featured-review__stars {
  order: 3;
  color: var(--lp-primary-color);
}
.lp-single-offline-course .lp-list-courses-related .section-title {
  margin-bottom: 40px;
}

@media (max-width: 991px) {
  .lp-single-offline-course .lp-single-offline-course-main {
    flex-direction: column;
    margin-bottom: 0;
  }
  .lp-single-offline-course__left, .lp-single-offline-course__right {
    width: 100%;
  }
}
@media (max-width: 767px) {
  .lp-single-offline-course__left .lp-instructor-info {
    flex-direction: column;
  }
}
.lp-archive-courses .course-detail-info {
  padding: 28px 0 24px 0;
  color: var(--lp-color-white, #fff);
  background: var(--lp-secondary-color);
  position: relative;
}
.lp-archive-courses .course-detail-info .course-title {
  margin: 0 0 24px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--lp-color-white, #fff);
  font-weight: 700;
  font-size: calc(var(--lp-font-size-base, 1em) * 1.8);
  padding-bottom: 24px;
}
@media (min-width: 769px) {
  .lp-archive-courses .course-detail-info {
    overflow-x: hidden;
  }
  .lp-archive-courses .course-detail-info .course-title, .lp-archive-courses .course-detail-info .course-meta-secondary {
    padding-right: 300px;
  }
  .lp-archive-courses .course-detail-info::before, .lp-archive-courses .course-detail-info::after {
    content: "";
    width: 50%;
    height: 100%;
    background: var(--lp-secondary-color);
    position: absolute;
    top: 0;
  }
  .lp-archive-courses .course-detail-info::before {
    right: 100%;
    left: auto;
  }
  .lp-archive-courses .course-detail-info::after {
    left: 100%;
    right: auto;
  }
}
.lp-archive-courses .course-detail-info .course-meta-secondary {
  margin-bottom: 0;
}
.lp-archive-courses .course-meta__pull-left {
  font-size: calc(var(--lp-font-size-base, 1em) * 0.925);
}
.lp-archive-courses .course-meta__pull-left .meta-item {
  display: flex;
  text-transform: capitalize;
  align-items: center;
}
.lp-archive-courses .course-meta__pull-left .meta-item::before {
  margin-right: 8px;
  color: var(--lp-primary-color);
  font-family: "lp-icon";
}
.lp-archive-courses .course-meta__pull-left .meta-item .meta-item__image {
  margin-right: 8px;
  flex: 0 0 46px;
}
.lp-archive-courses .course-meta__pull-left .meta-item .meta-item__image img {
  overflow: hidden;
  position: relative;
  width: 46px;
  height: 46px;
  border-radius: 50%;
  display: block;
}
.lp-archive-courses .course-meta__pull-left .meta-item.meta-item-duration::before {
  content: "\f017";
}
.lp-archive-courses .course-meta__pull-left .meta-item.meta-item-level::before {
  content: "\f012";
}
.lp-archive-courses .course-meta__pull-left .meta-item.meta-item-lesson::before {
  content: "\f0c5";
}
.lp-archive-courses .course-meta__pull-left .meta-item.meta-item-quiz::before {
  content: "\f12e";
}
.lp-archive-courses .course-meta__pull-left .meta-item.meta-item-student::before {
  content: "\f501";
}
.lp-archive-courses .course-meta__pull-left .meta-item__value label {
  margin: 0;
  color: var(--lp-color-accent-1);
  font-size: calc(var(--lp-font-size-base, 1em) * 0.86);
  font-weight: 300;
  line-height: 1.5;
  display: block;
}
.lp-archive-courses .course-meta__pull-left .meta-item__value > div {
  line-height: 1.5;
}
.lp-archive-courses .course-meta__pull-left .meta-item__value > div a {
  color: var(--lp-color-white, #fff);
  text-transform: capitalize;
  text-decoration: none;
}
.lp-archive-courses .course-meta__pull-left .meta-item__value > div a:hover {
  color: var(--lp-primary-color);
}
.lp-archive-courses .course-meta__pull-left .meta-item-categories {
  align-items: center;
}
.lp-archive-courses .course-meta__pull-left .meta-item-categories::before {
  margin-right: 8px;
  font-size: 2.25em;
  line-height: 1;
  content: "\f097";
}
.lp-archive-courses .course-meta__pull-left .meta-item-categories .meta-item__value div span {
  padding: 0 4px;
}
.lp-archive-courses .course-meta__pull-left .meta-item-review .meta-item__value {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.lp-archive-courses .course-meta__pull-left .meta-item-review .meta-item__value .review-stars-rated {
  margin-bottom: 0;
  line-height: 1;
}
.lp-archive-courses .course-meta-primary {
  margin-bottom: 20px;
}
.lp-archive-courses .course-meta-primary .meta-item-categories::before {
  font-weight: 400;
}
.lp-archive-courses .lp-entry-content {
  display: flex;
  flex-direction: row;
}
.lp-archive-courses .lp-entry-content .entry-content-left {
  width: calc(100% - 300px);
  max-width: 100%;
  margin: 0;
  padding-right: 40px;
  padding-top: 60px;
}
@media (max-width: 768px) {
  .lp-archive-courses .lp-entry-content .entry-content-left {
    width: 100%;
    padding-right: 0;
    margin-bottom: 50px;
  }
}
@media (max-width: 768px) {
  .lp-archive-courses .lp-entry-content {
    flex-direction: column;
  }
}
.lp-archive-courses .course-summary-sidebar {
  width: 300px;
  max-width: 100%;
  margin-top: -180px;
}
@media (max-width: 768px) {
  .lp-archive-courses .course-summary-sidebar {
    width: 100%;
    margin-top: 0;
  }
  .lp-archive-courses .course-summary-sidebar .course-summary-sidebar__inner {
    width: 100%;
  }
}
@media (max-width: 767px) {
  .lp-archive-courses .course-summary-sidebar {
    width: 100%;
    padding-left: 0;
  }
}
.lp-archive-courses .course-summary-sidebar__inner {
  width: 300px;
}
.lp-archive-courses .course-summary-sidebar__inner .course-sidebar-top {
  box-shadow: 0 15px 20px 0 rgba(0, 0, 0, 0.05);
}
.lp-archive-courses .course-summary-sidebar__inner .course-sidebar-secondary {
  margin-top: 30px;
  padding: 0;
}
.lp-archive-courses .course-summary-sidebar__inner .learn-press-course-wishlist {
  color: var(--lp-color-white, #fff);
  background: transparent !important;
  font-size: 0;
}
.lp-archive-courses .course-summary-sidebar__inner .learn-press-course-wishlist.on, .lp-archive-courses .course-summary-sidebar__inner .learn-press-course-wishlist:hover {
  color: var(--lp-primary-color);
}
.lp-archive-courses .course-summary-sidebar__inner .learn-press-course-wishlist::before {
  font-family: "lp-icon";
  font-size: 1em;
  content: "\f004";
}
.lp-archive-courses .course-summary-sidebar__inner > div {
  padding: 20px;
  background: var(--lp-color-white, #fff);
}
.lp-archive-courses .course-summary-sidebar__inner > div ul {
  list-style: none;
}
.lp-archive-courses .course-summary-sidebar__inner > div ul li a {
  color: #666;
  font-weight: 300;
  line-height: 1.5em;
}
.lp-archive-courses .course-summary-sidebar__inner > div ul li a:hover {
  color: var(--lp-primary-color);
  box-shadow: unset;
}
.lp-archive-courses .courses-btn-load-more {
  display: flex;
  align-items: center;
  gap: 5px;
}
.lp-archive-courses .course-wishlist:before {
  font-family: "lp-icon";
}
.lp-archive-courses .lp-course-curriculum ul {
  padding: 0;
}

.lp-single-course__header {
  background-color: #F1F2F8;
}
.lp-single-course__header__inner {
  max-width: var(--lp-container-max-width);
  margin: 0 auto;
  padding-right: var(--lp-cotainer-padding);
  padding-left: var(--lp-cotainer-padding);
  padding-top: 40px;
  padding-bottom: 40px;
}
@media (max-width: 767px) {
  .lp-single-course__header__inner {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}
@media (min-width: 992px) {
  .lp-single-course__header__inner > * {
    width: calc(100% - 440px);
  }
}
.lp-single-course__header .learn-press-breadcrumb {
  display: flex;
  list-style: none;
  gap: 4px;
  flex-wrap: wrap;
  margin-bottom: 8px;
  padding: 0;
}
.lp-single-course__header .course-title {
  font-weight: 700;
  font-size: calc(var(--lp-font-size-base, 1em) * 1.8);
  margin: 0 0 24px 0;
}
@media (max-width: 767px) {
  .lp-single-course__header .course-title {
    margin: 0 0 16px 0;
  }
}
.lp-single-course__header .course-instructor-category {
  display: flex;
  padding: 0;
  gap: 4px;
  flex-wrap: wrap;
  margin-bottom: 40px;
}
@media (max-width: 767px) {
  .lp-single-course__header .course-instructor-category {
    margin-bottom: 20px;
  }
}
.lp-single-course__header .course-instructor-category > div > * {
  display: inline;
  vertical-align: middle;
}
.lp-single-course__header .course-instructor-category label {
  font-size: inherit;
  font-weight: inherit;
}
.lp-single-course__header .course-instructor-category a {
  text-decoration: none;
  color: inherit;
}
.lp-single-course__header .course-instructor-category a:hover {
  color: var(--lp-primary-color, #ffb606);
}
.lp-single-course__header .lp-single-course-info-one {
  display: flex;
  column-gap: 20px;
  row-gap: 8px;
  flex-wrap: wrap;
}
.lp-single-course__header .lp-single-course-info-one .star-info {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding-right: 20px;
  position: relative;
}
.lp-single-course__header .lp-single-course-info-one .star-info::after {
  content: "";
  right: 0;
  left: auto;
  width: 1px;
  height: 20px;
  position: absolute;
  background-color: var(--lp-border-color, #E2E0DB);
}
@media (max-width: 500px) {
  .lp-single-course__header .lp-single-course-info-one .star-info {
    padding-right: 0;
  }
  .lp-single-course__header .lp-single-course-info-one .star-info::after {
    content: none;
  }
}
.lp-single-course__header .lp-single-course-info-one .ico-star,
.lp-single-course__header .lp-single-course-info-one .lp-review-svg-star {
  display: inline-flex;
  align-items: center;
}
.lp-single-course .lp-single-course-main {
  padding: 40px 0 80px;
  display: flex;
  column-gap: 60px;
  row-gap: 30px;
  flex-wrap: wrap;
}
@media (max-width: 1500px) {
  .lp-single-course .lp-single-course-main {
    column-gap: 30px;
  }
}
@media (max-width: 991px) {
  .lp-single-course .lp-single-course-main {
    flex-direction: column-reverse;
    padding: 40px 0;
  }
}
.lp-single-course .lp-single-course-main__left {
  width: 100%;
}
@media (min-width: 992px) {
  .lp-single-course .lp-single-course-main__left {
    width: calc(100% - 410px);
  }
}
@media (min-width: 1501px) {
  .lp-single-course .lp-single-course-main__left {
    width: calc(100% - 440px);
  }
}
.lp-single-course .lp-single-course-main__left > div {
  margin-bottom: 40px;
}
@media (max-width: 767px) {
  .lp-single-course .lp-single-course-main__left > div {
    margin-bottom: 30px;
  }
}
.lp-single-course .lp-single-course-main__left > div:last-child {
  margin-bottom: 0;
}
.lp-single-course .lp-single-course-main__left .extra-box__title,
.lp-single-course .lp-single-course-main__left .course-faqs__title,
.lp-single-course .lp-single-course-main__left .course-material__title,
.lp-single-course .lp-single-course-main__left .lp-course-curriculum__title,
.lp-single-course .lp-single-course-main__left .comment-reply-title,
.lp-single-course .lp-single-course-main__left .section-title,
.lp-single-course .lp-single-course-main__left .item-title {
  margin: 0 0 20px 0;
  font-size: calc(var(--lp-font-size-base, 1em) * 1.6);
}
@media (max-width: 767px) {
  .lp-single-course .lp-single-course-main__left .extra-box__title,
  .lp-single-course .lp-single-course-main__left .course-faqs__title,
  .lp-single-course .lp-single-course-main__left .course-material__title,
  .lp-single-course .lp-single-course-main__left .lp-course-curriculum__title,
  .lp-single-course .lp-single-course-main__left .comment-reply-title,
  .lp-single-course .lp-single-course-main__left .section-title,
  .lp-single-course .lp-single-course-main__left .item-title {
    margin: 0 0 16px 0;
  }
}
.lp-single-course .lp-single-course-main__left .extra-box ul {
  margin: 0 0 0 20px;
  padding: 0;
}
.lp-single-course .lp-single-course-main__left .comments-area {
  width: 100%;
  max-width: 100%;
  margin: 0;
}
.lp-single-course .lp-single-course-main__right {
  width: 100%;
}
@media (min-width: 992px) {
  .lp-single-course .lp-single-course-main__right {
    width: 380px;
    margin-top: -280px;
  }
}
.lp-single-course .lp-single-course-main__right__inner {
  position: sticky;
  top: 0;
  background-color: var(--lp-color-white, #fff);
  border: 1px solid var(--lp-border-color, #E2E0DB);
  padding: 20px;
  border-radius: var(--lp-border-radius, 5px);
}
.lp-single-course .lp-single-course-main__right__inner > div {
  margin-bottom: 20px;
}
.lp-single-course .lp-single-course-main__right__inner > div:last-child {
  margin-bottom: 0;
}
.lp-single-course .lp-single-course-main .info-metas .info-meta-item {
  display: flex;
  gap: 4px;
  margin-bottom: 12px;
}
.lp-single-course .lp-single-course-main .info-metas .info-meta-item:last-child {
  margin-bottom: 0;
}
.lp-single-course .lp-single-course-main .info-metas .info-meta-left {
  display: flex;
  gap: 8px;
  align-items: center;
}
.lp-single-course .lp-single-course-main .info-metas i {
  color: var(--lp-primary-color, #ffb606);
}
.lp-single-course .lp-single-course-main .course-img {
  margin-bottom: 20px;
  overflow: hidden;
  margin: -20px -20px 20px -20px;
  border-radius: var(--lp-border-radius, 5px) var(--lp-border-radius, 5px) 0 0;
}
.lp-single-course .lp-single-course-main .course-img img {
  max-width: 100%;
  display: block;
}
.lp-single-course .lp-single-course-main .course-price {
  display: block;
  margin-bottom: 20px;
}
.lp-single-course .lp-single-course-main .course-price .free {
  color: #3AB500;
  font-size: calc(var(--lp-font-size-base, 1em) * 1.25);
}
.lp-single-course .lp-single-course-main .course-price .origin-price {
  text-decoration: line-through;
  opacity: 0.6;
  font-size: var(--lp-font-size-base, 1em);
  margin-right: 4px;
}
.lp-single-course .lp-single-course-main .course-price .price {
  font-size: calc(var(--lp-font-size-base, 1em) * 1.25);
  font-weight: var(--lp-font-weight-link, 600);
}
.lp-single-course .lp-single-course-main .course-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.lp-single-course .lp-single-course-main .course-buttons form, .lp-single-course .lp-single-course-main .course-buttons .lp-button {
  width: 100%;
}
.lp-single-course .lp-single-course-main .course-buttons .lp-button {
  background-color: var(--lp-primary-color, #ffb606);
  border: var(--lp-primary-color, #ffb606);
  color: var(--lp-color-white, #fff);
}
.lp-single-course .lp-single-course-main .course-buttons .lp-button:hover {
  background-color: var(--lp-secondary-color);
  border-color: var(--lp-secondary-color);
}
.lp-single-course .lp-single-course-main .course-buttons .learn-press-message {
  margin: 0;
}
.lp-single-course .lp-single-course-main .featured-review__title {
  font-weight: var(--lp-font-weight-link, 600);
  font-size: calc(var(--lp-font-size-base, 1em) * 1.25);
}
.lp-single-course .lp-list-courses-related {
  margin: 0 0 40px 0;
}
@media (max-width: 767px) {
  .lp-single-course .lp-list-courses-related {
    margin: 0 0 20px 0;
  }
}
.lp-single-course .lp-list-courses-related .section-title {
  margin: 0 0 40px 0;
  font-size: calc(var(--lp-font-size-base, 1em) * 1.6);
}
.lp-single-course .course-detail-info__inner {
  max-width: var(--lp-container-max-width);
  margin: 0 auto;
  padding-right: var(--lp-cotainer-padding);
  padding-left: var(--lp-cotainer-padding);
}
.lp-single-course .course-detail-info .course-meta {
  gap: 16px;
  flex-wrap: wrap;
}
@media (min-width: 769px) {
  .lp-single-course .course-detail-info .course-meta__pull-left {
    width: calc(100% - 340px);
    max-width: 100%;
    margin: 0;
  }
}
.lp-single-course .course-tabs .course-faqs__title,
.lp-single-course .course-tabs .course-material__title {
  display: none;
}
.lp-single-course .course-summary-sidebar__inner {
  background-color: var(--lp-color-white, #fff);
  border: 1px solid var(--lp-border-color, #E2E0DB);
  padding: 20px;
  border-radius: var(--lp-border-radius, 5px);
}
.lp-single-course .course-summary-sidebar .course-summary-sidebar__inner > * {
  padding: 0;
  margin-bottom: 20px;
}
.lp-single-course .course-summary-sidebar .course-summary-sidebar__inner > *:last-child {
  margin-bottom: 0;
}
.lp-single-course .course-summary-sidebar .course-img {
  margin: -20px -20px 20px;
  border-radius: var(--lp-border-radius, 5px) var(--lp-border-radius, 5px) 0 0;
}
.lp-single-course .course-summary-sidebar .course-img img {
  max-width: 100%;
  width: 100%;
  height: auto;
}
.lp-single-course .course-summary-sidebar .course-price {
  display: block;
  font-size: calc(var(--lp-font-size-base, 1em) * 1.25);
  font-weight: 700;
}
.lp-single-course .course-summary-sidebar .course-price .free {
  color: #3AB500;
}
.lp-single-course .course-summary-sidebar .course-price .origin-price {
  text-decoration: line-through;
  opacity: 0.6;
  font-size: calc(var(--lp-font-size-base, 1em) * 0.925);
  margin-right: 4px;
  font-weight: normal;
}
.lp-single-course button {
  font-size: 1em;
}

.course-featured {
  padding: 4px 12px;
  border-radius: var(--lp-border-radius, 5px);
  background-color: #FF3B30;
  color: white;
  text-transform: uppercase;
  font-size: calc(var(--lp-font-size-base, 1em) * 0.86);
  font-weight: var(--lp-font-weight-link, 600);
  margin-bottom: 12px;
  display: inline-block;
}

.course-summary div.lp-list-co-instructor {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  margin-top: 30px;
}
.course-summary div.lp-list-co-instructor__item {
  display: flex;
  column-gap: 30px;
}
@media (max-width: 767px) {
  .course-summary div.lp-list-co-instructor__item {
    align-items: center;
    flex-direction: column;
    padding: 0 15px;
    column-gap: 0;
    row-gap: 10px;
  }
}
.course-summary div.lp-list-co-instructor__avatar > img {
  width: 100%;
  max-width: 96px;
  border-radius: 999px;
}
.course-summary div.lp-list-co-instructor__bio {
  flex: 1;
}
.course-summary div.lp-list-co-instructor__bio__top > a {
  font-size: 1.125em;
  font-weight: 600;
}
.course-summary .course-price {
  margin-bottom: 10px;
}
.course-summary .course-price .origin-price,
.course-summary .course-price .origin-price {
  margin-right: 10px;
  font-size: 1.125em;
  font-style: italic;
  text-decoration: line-through;
}
.course-summary .course-price .price {
  font-size: 1.5em;
}
.course-summary .course-summary-sidebar .lp-course-buttons {
  margin-bottom: 20px;
  text-align: center;
}
.course-summary .course-featured-review .featured-review__title {
  margin-top: 0;
  margin-bottom: 6px;
  font-size: 1.2em;
  font-weight: 500;
}
.course-summary .course-featured-review .featured-review__stars {
  padding-bottom: 12px;
  color: var(--lp-primary-color);
}
.course-summary .course-featured-review .featured-review__content {
  position: relative;
  color: #666;
  font-style: italic;
}
.course-summary .course-featured-review .featured-review__content::after {
  position: absolute;
  top: -42px;
  right: -15px;
  color: rgba(102, 102, 102, 0.1);
  font-family: Arial;
  font-size: 180px;
  font-weight: 700;
  font-style: normal;
  content: "‘‘";
  transform: rotate(180deg);
}
.course-summary .course-tags a {
  display: inline-block;
  padding: 3px 5px;
  border-radius: 4px;
  color: #fff;
  background: #9aa5ab;
  font-size: 0.75em;
  line-height: 1;
}

/***********/
.course-meta {
  display: flex;
  margin-bottom: 40px;
}
.course-meta .course-students::before {
  font-family: "lp-icon";
  content: "";
}
.course-meta .course-meta__pull-left,
.course-meta .course-meta__pull-right {
  display: flex;
  flex-wrap: wrap;
  gap: 50px;
}
@media (max-width: 767px) {
  .course-meta .course-meta__pull-left,
  .course-meta .course-meta__pull-right {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 20px;
  }
}
@media (max-width: 560px) {
  .course-meta .course-meta__pull-left,
  .course-meta .course-meta__pull-right {
    grid-template-columns: repeat(2, 1fr);
  }
}
.course-meta.course-meta-primary .course-meta__pull-left {
  gap: 16px;
}
@media (max-width: 767px) {
  .course-meta.course-meta-primary .course-meta__pull-left {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 480px) {
  .course-meta.course-meta-primary .course-meta__pull-left {
    grid-template-columns: 1fr;
  }
}
.course-meta.course-meta-primary .course-meta__pull-left .meta-item {
  margin-right: 0 !important;
}
@media (max-width: 560px) {
  .course-meta.course-meta-primary .course-meta__pull-left .meta-item {
    width: 100%;
  }
}
@media (max-width: 1024px) {
  .course-meta.course-meta-secondary .course-meta__pull-left {
    gap: 16px;
  }
}
@media (max-width: 1024px) {
  .course-meta.course-meta-secondary .course-meta__pull-left .meta-item {
    margin-right: 0 !important;
    width: calc((100% - 32px) / 3);
  }
}
@media (max-width: 768px) {
  .course-meta.course-meta-secondary .course-meta__pull-left .meta-item {
    width: 100%;
  }
}
.course-meta .course-meta__pull-left {
  flex: 1;
}
.course-meta .course-meta__pull-right {
  flex: 0 0 50%;
}
.course-meta.course-meta-primary .meta-item {
  flex: 1;
}
.course-meta.two-columns .course-meta {
  flex: 1;
}

.lp-single-course .comment-form .comment-form-url,
.lp-single-course .comment-form .comment-form-email,
.lp-single-course .comment-form .comment-form-author,
.lp-single-course .comment-form .comment-form-comment, .wp-block-learnpress-course-comment .comment-form .comment-form-url,
.wp-block-learnpress-course-comment .comment-form .comment-form-email,
.wp-block-learnpress-course-comment .comment-form .comment-form-author,
.wp-block-learnpress-course-comment .comment-form .comment-form-comment {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.lp-single-course .comment-form input[type=url],
.lp-single-course .comment-form input[type=text],
.lp-single-course .comment-form input[type=email],
.lp-single-course .comment-form textarea, .wp-block-learnpress-course-comment .comment-form input[type=url],
.wp-block-learnpress-course-comment .comment-form input[type=text],
.wp-block-learnpress-course-comment .comment-form input[type=email],
.wp-block-learnpress-course-comment .comment-form textarea {
  padding: 12px 20px;
  outline: none;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  border-radius: var(--lp-border-radius, 5px);
}
.lp-single-course .comment-form input[type=submit], .wp-block-learnpress-course-comment .comment-form input[type=submit] {
  border-radius: var(--lp-border-radius, 5px);
}

.course-summary-sidebar {
  --lp-button-background-color: #28303d;
  --lp-button-color: #d1e4dd;
  position: relative;
}
.course-summary-sidebar .course-sidebar-preview {
  margin-bottom: 35px;
  background: #fff;
}
.course-summary-sidebar .has-global-padding {
  padding-right: 0;
  padding-left: 0;
}
.course-summary-sidebar .lp-course-buttons {
  margin-bottom: 34px;
}
.course-summary-sidebar .lp-course-buttons > * {
  margin-bottom: 10px;
}
.course-summary-sidebar .lp-course-buttons > *:last-child {
  margin-bottom: 0;
}
.course-summary-sidebar .lp-course-buttons form,
.course-summary-sidebar .lp-course-buttons button {
  width: 100%;
  text-decoration: none;
}
.course-summary-sidebar .lp-course-buttons button {
  font-weight: 500;
  background-color: var(--lp-button-background-color);
  color: var(--lp-button-color);
  border: none;
}
.course-summary-sidebar .lp-course-buttons button:hover {
  border-color: var(--lp-primary-color);
}
.course-summary-sidebar .course-results-progress .items-progress,
.course-summary-sidebar .course-results-progress .course-progress {
  display: flex;
  flex-direction: row;
  margin: 0 0 5px;
  padding: 0;
  justify-content: space-between;
  flex-wrap: wrap;
}
.course-summary-sidebar .course-results-progress .items-progress__heading,
.course-summary-sidebar .course-results-progress .course-progress__heading {
  margin: 0;
  margin-bottom: 7px;
  padding: 0;
  color: #333;
  font-size: 1em;
  font-weight: 500;
  flex: 1 1 auto;
  width: auto;
}
.course-summary-sidebar .course-results-progress .items-progress .learn-press-progress,
.course-summary-sidebar .course-results-progress .course-progress .learn-press-progress {
  clear: both;
  width: 100%;
}
.course-summary-sidebar .course-results-progress .number {
  display: block;
  margin: 0;
  color: #666;
  font-size: 1em;
  font-weight: 300;
  line-height: 1em;
  text-align: right;
}
@media (min-width: 769px) {
  .course-summary-sidebar.slide-down .course-summary-sidebar__inner {
    position: fixed;
    top: 0;
  }
}
.lp-course-buttons:empty {
  display: none;
}

.course-sidebar-preview .course-price {
  text-align: center;
}
.course-sidebar-preview .course-price .origin-price {
  color: var(--lp-primary-color);
  font-size: 1.3em;
  font-weight: 300;
  font-style: unset;
}
.course-sidebar-preview .course-price .price {
  color: var(--lp-primary-color);
  font-size: 1.3em;
  font-weight: 500;
}
.course-sidebar-preview .media-preview {
  overflow: hidden;
  position: relative;
  margin: -20px -20px 10px;
}
.course-sidebar-preview .media-preview img {
  max-width: 100%;
  height: auto;
  vertical-align: top;
  width: 100%;
}
.course-sidebar-preview .course-wishlist {
  float: none;
}
.course-sidebar-preview .course-time {
  margin-bottom: 28px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(204, 204, 204, 0.3);
}
.course-sidebar-preview .course-time-row {
  color: #666;
  font-size: 1em;
  font-weight: 300;
  margin-bottom: 0;
}
.course-sidebar-preview .course-time-row strong {
  color: #333;
  font-weight: 500;
}
.course-sidebar-preview .course-time-row time {
  color: #666;
  font-weight: 300;
}
.course-sidebar-preview > *:last-child {
  margin-bottom: 0;
}

.lp-instructor-info {
  display: flex;
  gap: 30px;
  margin-top: 30px;
  margin-bottom: 30px;
}
@media (max-width: 600px) {
  .lp-instructor-info {
    flex-wrap: wrap;
  }
}
.lp-instructor-info .lp-section-instructor {
  flex: 1;
}
.lp-instructor-info .instructor-display-name {
  font-size: 1.4rem;
}
.lp-instructor-info .instructor-avatar {
  max-width: 200px;
}
.lp-instructor-info .lp-instructor-meta {
  margin-top: 16px;
  display: flex;
  column-gap: 20px;
  row-gap: 8px;
}
.lp-instructor-info .instructor-description {
  margin-top: 16px;
}
.lp-instructor-info img {
  max-width: 100%;
  border-radius: var(--lp-border-radius, 5px);
}
.lp-instructor-info .instructor-social {
  display: inline-flex;
  padding: 0;
  gap: 12px;
  margin: 16px 0 0 0;
}
.lp-instructor-info .instructor-social > a {
  text-align: center;
  list-style: none;
}
.lp-instructor-info .instructor-social > a span {
  display: none;
}
.lp-instructor-info .instructor-social > a i {
  width: 40px;
  height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  border-radius: 50%;
}
.lp-instructor-info .instructor-social > a i:hover {
  background-color: var(--lp-primary-color, #ffb606);
  border-color: var(--lp-primary-color, #ffb606);
  color: var(--lp-color-white, #fff);
}

.instructor-display-name {
  font-weight: var(--lp-font-weight-link, 600);
}

.instructor-item-meta {
  display: inline-flex;
  border-left: 1px solid var(--lp-border-color, #E2E0DB);
  padding-left: 20px;
}

.lp-instructor-meta .instructor-item-meta:first-child {
  border: none;
  padding-left: 0;
}

.wrapper-instructor-total-students, .wrapper-instructor-total-courses {
  display: flex;
  gap: 4px;
  align-items: center;
}

.learnpress-page .lp-button.secondary {
  background: #9198ab;
}
.learnpress-page .lp-button.secondary:hover {
  background: #9ba6c5;
}
.learnpress-page .lp-button:disabled, .learnpress-page .lp-button[disabled] {
  background: #ddd;
  pointer-events: none;
  color: var(--lp-button-background-color);
}
.learnpress-page:hover {
  text-decoration: none;
}

.lp-sidebar-toggle__close .content-item-wrap .quiz-buttons.align-center .button-left.fixed {
  margin-left: 0 !important;
}

.course-item-nav {
  display: flex;
}
.course-item-nav .prev,
.course-item-nav .next {
  /* flex: 1;*/
}
.course-item-nav .prev span,
.course-item-nav .next span {
  display: block;
  font-weight: bold;
}
.course-item-nav .prev a,
.course-item-nav .next a {
  color: #999;
}
.course-item-nav .next {
  text-align: right;
}

#popup-course {
  display: flex;
  position: fixed;
  z-index: 99999;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: var(--lp-bg-color-lesson, #fff);
}
#popup-course .wp-block-learnpress-item-close .back-course {
  padding: 24px;
}
#popup-course .back-course {
  padding-left: 24px;
  padding-right: 24px;
  line-height: 70px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}
#popup-course .back-course:hover {
  background: rgba(255, 255, 255, 0.15);
}
@media (max-width: 768px) {
  #popup-course .back-course {
    background: transparent;
    opacity: 0.6;
    padding-left: 15px;
    padding-right: 15px;
  }
  #popup-course .back-course:hover {
    background: transparent;
    opacity: 1;
  }
}
#popup-course .popup-header__inner {
  display: flex;
  width: 792px;
  margin: 0 auto;
  justify-content: space-between;
  align-items: center;
  padding-left: 15px;
  padding-right: 15px;
  column-gap: 10px;
}
@media (max-width: 1024px) {
  #popup-course .popup-header__inner {
    width: 100%;
  }
}
@media (max-width: 767px) {
  #popup-course .popup-header__inner {
    flex-direction: column;
    gap: 5px;
  }
  #popup-course .popup-header__inner.can-finish-course .items-progress {
    display: none;
  }
}
#popup-course .lp-quiz-buttons .complete-quiz,
#popup-course .lp-quiz-buttons .back-quiz,
#popup-course .lp-quiz-buttons .review-quiz {
  float: right;
}
#popup-course .quiz-results,
#popup-course .quiz-content,
#popup-course .quiz-questions,
#popup-course .quiz-buttons,
#popup-course .quiz-attempts {
  margin-bottom: 60px;
}
#popup-course .quiz-questions .lp-fib-content {
  margin-bottom: 20px;
  padding: 20px;
  border: 2px solid var(--lp-border-color, #E2E0DB);
  border-radius: 6px;
  line-height: 1.6;
}
#popup-course .quiz-questions .lp-fib-input {
  display: inline-block;
  width: auto;
  max-width: none;
}
#popup-course .quiz-questions .lp-fib-input > input {
  height: 36px;
  padding: 6px 16px;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  line-height: 2.25em;
}
#popup-course .quiz-questions .lp-fib-answered {
  padding: 0 10px;
  background: #ececec;
  white-space: nowrap;
}
#popup-course .quiz-questions .lp-fib-answered.fail {
  border: 2px solid #d85554;
}
#popup-course .quiz-questions .lp-fib-answered.fail .lp-fib-answered__answer {
  text-decoration: line-through;
}
#popup-course .quiz-questions .lp-fib-answered.correct {
  border: 2px solid #00adff;
}
#popup-course .quiz-questions .lp-fib-note {
  display: flex;
  margin-left: 10px;
  font-size: 0.8em;
  font-weight: 400;
  align-items: center;
}
#popup-course .quiz-questions .lp-fib-note > span {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin: 0 5px 0 0;
}
#popup-course .quiz-questions .lp-sorting-choice__check-answer {
  padding: 5px 20px;
  border: 2px solid #EBF8E5;
  border-radius: 5px;
}
#popup-course .question .question-response {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
#popup-course .scrollbar-light > .scroll-element {
  z-index: 9999;
}
#popup-course .scrollbar-light > .scroll-element.scroll-y {
  display: none;
}
#popup-course .scrollbar-light > .scroll-element.scroll-y .scroll-bar {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

#popup-header {
  display: flex;
  position: fixed;
  z-index: 100;
  right: 0;
  left: 475px;
  padding: 0;
  border-bottom: 1px solid #d9e0f1;
  background: var(--lp-secondary-color);
  align-items: center;
  -webkit-transition: left 0.25s;
  -moz-transition: left 0.25s;
  -ms-transition: left 0.25s;
  -o-transition: left 0.25s;
  transition: left 0.25s;
}
#popup-header .course-title {
  margin: 0;
  padding: 0;
  font-size: var(--lp-font-size-base, 1em);
  letter-spacing: unset;
}
#popup-header .course-title a {
  display: -webkit-box;
  overflow: hidden;
  color: #fff;
  font-weight: 400;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  text-decoration: none;
}
#popup-header .course-title a:hover {
  color: var(--lp-primary-color);
}
#popup-header .items-progress {
  display: flex;
  justify-content: center;
  align-items: center;
  column-gap: 10px;
  white-space: nowrap;
}
#popup-header .number {
  padding-right: 10px;
  color: #fff;
  font-size: calc(var(--lp-font-size-base, 1em) * 0.86);
  font-weight: 300;
}
#popup-header .lp-button {
  display: flex;
  position: relative;
  height: auto;
  border: none;
  color: #fff;
  background: var(--lp-primary-color);
  white-space: nowrap;
  padding: 6px 15px;
  font-size: 1rem;
}
#popup-header .lp-button:hover {
  opacity: 0.8;
}
#popup-header .lp-button-back {
  float: right;
  margin: 9px 10px;
}
#popup-header .lp-button-back button::before, #popup-header .lp-button-back button::after {
  content: "";
}
#popup-header .lp-button-back button::before {
  border: 1px solid #ddd;
}
#popup-header .lp-button-back button::after {
  border: 1px solid #ddd;
}

#popup-sidebar {
  overflow: auto;
  position: relative;
  box-shadow: 0 15px 30px 0 rgba(0, 0, 0, 0.1);
  flex: 0 0 475px;
  -webkit-transition: 0.25s;
  -moz-transition: 0.25s;
  -ms-transition: 0.25s;
  -o-transition: 0.25s;
  transition: 0.25s;
}
#popup-sidebar .search-course {
  display: flex;
  position: relative;
  height: 70px;
  background: var(--lp-white-grey, #F7F7FB);
}
#popup-sidebar .search-course input[name=s] {
  display: block;
  width: 100%;
  padding-left: 20px;
  border: none;
  color: var(--lp-color-base, #333);
  background: transparent;
  box-shadow: none;
  height: auto;
  font-size: 1rem;
}
#popup-sidebar .search-course input[name=s]::-webkit-input-placeholder {
  color: #999;
}
#popup-sidebar .search-course input[name=s]::-moz-placeholder {
  color: #999;
}
#popup-sidebar .search-course input[name=s]:-ms-input-placeholder {
  color: #999;
}
#popup-sidebar .search-course input[name=s]:-moz-placeholder {
  color: #999;
}
#popup-sidebar .search-course input[name=s]::placeholder {
  color: #999;
}
#popup-sidebar .search-course input[name=s]:focus {
  outline: 0;
}
#popup-sidebar .search-course button {
  position: absolute;
  top: 0;
  right: 12px;
  height: 70px;
  padding: 0 16px;
  border: 0;
  background: transparent;
  line-height: 1px;
  box-shadow: none;
  font-size: 1rem;
}
#popup-sidebar .search-course button:focus {
  outline: none;
}
#popup-sidebar .search-course button i {
  color: var(--lp-color-accent, #666);
  width: 24px;
  display: flex;
  justify-content: center;
}
#popup-sidebar .search-course button.clear {
  display: none;
}
#popup-sidebar .search-course button.clear::before {
  content: "\f00d";
}
#popup-sidebar .search-course.searching button:before {
  display: inline-block;
  content: "\f110";
  animation: lp-rotating 1s linear infinite;
}
#popup-sidebar .course-curriculum {
  overflow: auto;
  position: absolute;
  top: 70px;
  bottom: 0;
  width: 475px;
}
#popup-sidebar .course-curriculum::-webkit-scrollbar-thumb {
  background: #ccc;
}
#popup-sidebar .course-curriculum::-webkit-scrollbar {
  width: 8px;
}
#popup-sidebar .course-curriculum::-webkit-scrollbar-track {
  background: #f5f5f5;
}
#popup-sidebar .section {
  position: relative;
  padding: 0 0 4px 0;
}
#popup-sidebar .section .circle-progress {
  display: inline-block;
  position: absolute;
  top: 50%;
  right: 10px;
  width: 24px;
  height: 24px;
  margin-top: -12px;
  border: 3px solid #d9e0f1;
  border-radius: 50%;
}
#popup-sidebar .section.section-empty .section-header {
  margin: 0;
}
#popup-sidebar .section-header {
  position: sticky;
  z-index: 1000;
  top: 0;
  padding: 20px 16px;
  cursor: pointer;
  background-color: var(--lp-bg-color-lesson, #fff);
}
#popup-sidebar .section-header .section-title .show-desc::before {
  font-family: "lp-icon";
  font-size: 1.125em;
  content: "\f0d7";
}
#popup-sidebar .section-header .section-toggle {
  justify-content: flex-end;
  flex: 0;
}
#popup-sidebar .section-header .section-toggle i {
  color: var(--lp-color-accent, #666);
}
#popup-sidebar .section-header .section-meta {
  padding-top: 6px;
  padding-bottom: 0;
}
#popup-sidebar .section-content {
  margin-bottom: 0;
}
#popup-sidebar .curriculum-more {
  padding-right: 16px;
  padding-left: 16px;
}
#popup-sidebar .course-item > span {
  display: none;
}
#popup-sidebar .course-item::before {
  top: -1px;
  bottom: -1px;
  height: auto;
  background: transparent;
}
#popup-sidebar .course-item::after {
  content: "";
}
#popup-sidebar .course-item.has-status.failed .trans {
  color: #f02425;
}
#popup-sidebar .course-item.status-completed .trans {
  color: #3bb54a;
}
#popup-sidebar .lp-course-curriculum .course-section {
  --lp-border-radius: 0;
  border-left: none;
}
#popup-sidebar .lp-course-curriculum .lp-course-curriculum__title,
#popup-sidebar .lp-course-curriculum .course-curriculum-info,
#popup-sidebar .lp-course-curriculum .course-section__description {
  display: none;
}
#popup-sidebar .lp-course-curriculum .course-item.current:before {
  background: var(--lp-primary-color, #ffb606);
}
#popup-sidebar .lp-course-curriculum .course-item__content {
  flex-direction: column;
  row-gap: 0;
}
#popup-sidebar .lp-course-curriculum .course-item__right {
  font-size: calc(var(--lp-font-size-base, 1em) * 0.925);
}

#popup-content {
  overflow: auto;
  position: relative;
  margin: 70px 0 50px 0;
  flex: 1;
  padding-left: 15px;
  padding-right: 15px;
}
#popup-content .lp-button {
  position: relative;
  margin: 0;
  padding: 8px 25px;
  border-color: var(--lp-border-color, #E2E0DB);
}
#popup-content .lp-button.instant-check .instant-check__icon {
  margin-right: 5px;
}
#popup-content .lp-button.instant-check .instant-check__icon::before {
  font-family: "lp-icon";
  content: "\f058";
}
#popup-content .lp-button.instant-check .instant-check__info {
  visibility: hidden;
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 3px;
  padding: 11px 20px;
  border-radius: 3px;
  background: var(--lp-white-grey, #F7F7FB);
  color: var(--lp-color-base, #333);
  white-space: nowrap;
  text-transform: none;
}
@media (max-width: 600px) {
  #popup-content .lp-button.instant-check .instant-check__info {
    white-space: normal;
    width: 90vw;
  }
}
#popup-content .lp-button.instant-check:hover .instant-check__info {
  visibility: visible;
}
#popup-content .lp-button.instant-check.loading::before {
  display: none;
}
#popup-content .lp-button.instant-check.loading .instant-check__icon {
  display: inline-block;
  animation: lp-rotating 1s linear infinite;
}
#popup-content .lp-button.instant-check.loading .instant-check__icon::before {
  font-weight: 800;
  content: "\f110";
}
#popup-content .lp-button:hover {
  border-color: var(--lp-primary-color);
}
#popup-content .lp-button.completed {
  display: flex;
  flex-direction: row-reverse;
  border-color: transparent;
  color: #fff;
  background: #3db748;
  align-items: center;
  justify-content: center;
  float: left;
}
#popup-content .lp-button.completed i {
  margin-left: 9px;
  font-size: 0.8em;
}
#popup-content.fixed-quiz-status .quiz-status {
  background: var(--lp-primary-color);
}
#popup-content.fixed-quiz-status .quiz-status > div {
  padding: 0;
}
#popup-content::-webkit-scrollbar {
  width: 8px;
}
#popup-content::-webkit-scrollbar-thumb {
  background: #ccc;
}
#popup-content::-webkit-scrollbar-track {
  background: #f5f5f5;
}

#popup-footer {
  position: fixed;
  z-index: 99;
  right: 0;
  bottom: 0;
  left: 475px;
  width: 100%;
  max-width: 792px;
  height: auto;
  margin: 0 auto;
  border-top: 1px solid #ebebeb;
  background: var(--lp-bg-color-lesson, #fff);
  -webkit-transition: left 0.25s;
  -moz-transition: left 0.25s;
  -ms-transition: left 0.25s;
  -o-transition: left 0.25s;
  transition: left 0.25s;
}
@media (max-width: 1024px) {
  #popup-footer {
    width: auto;
    padding: 0 15px;
  }
}
#popup-footer .course-item-nav {
  justify-content: space-between;
}
#popup-footer .course-item-nav .prev,
#popup-footer .course-item-nav .next {
  display: flex;
  line-height: 3.125em;
}
#popup-footer .course-item-nav .prev a,
#popup-footer .course-item-nav .next a {
  display: block;
  color: var(--lp-color-accent, #666);
}
#popup-footer .course-item-nav .prev::before,
#popup-footer .course-item-nav .next::before {
  color: #999;
  font-family: "lp-icon";
}
#popup-footer .course-item-nav .prev:hover a, #popup-footer .course-item-nav .prev:hover::before,
#popup-footer .course-item-nav .next:hover a,
#popup-footer .course-item-nav .next:hover::before {
  color: var(--lp-primary-color);
}
#popup-footer .course-item-nav .prev:hover .course-item-nav__name,
#popup-footer .course-item-nav .next:hover .course-item-nav__name {
  display: block;
}
#popup-footer .course-item-nav .next {
  flex-direction: row-reverse;
}
#popup-footer .course-item-nav .next::before {
  margin-left: 10px;
  content: "\f0da";
}
#popup-footer .course-item-nav[data-nav=next] {
  justify-content: flex-end;
}
#popup-footer .prev::before {
  margin-right: 10px;
  content: "\f0d9";
}
#popup-footer .prev .course-item-nav__name {
  right: auto;
  left: -30px;
}
#popup-footer .prev .course-item-nav__name::before {
  right: auto;
  left: 5px;
}
@media (max-width: 1024px) {
  #popup-footer .prev .course-item-nav__name {
    left: 15px;
  }
}
#popup-footer .course-item-nav__name {
  display: none;
  position: absolute;
  top: -20px;
  right: -30px;
  width: auto;
  padding: 10px 15px;
  color: var(--lp-color-accent, #666);
  background: #ccc;
  font-size: calc(var(--lp-font-size-base, 1em) * 0.925);
  line-height: 1;
}
@media (max-width: 1024px) {
  #popup-footer .course-item-nav__name {
    top: -25px;
    right: 15px;
    left: auto;
  }
}

#sidebar-toggle {
  display: inline-block;
  width: 32px;
  min-width: 32px;
  line-height: 70px;
  height: unset;
  margin: 0;
  background: rgba(255, 255, 255, 0.1);
  color: var(--lp-color-white, #fff);
  font-size: 1.4em;
  cursor: pointer;
  transition: 0.25s;
  -webkit-appearance: none;
  border: none;
  text-align: center;
}
#sidebar-toggle:after {
  display: none;
}
#sidebar-toggle::before {
  display: inline-block;
  position: static;
  margin: 0;
  width: auto;
  height: auto;
  font-family: "lp-icon";
  content: "\f0d9";
}
#sidebar-toggle:focus {
  border: 0;
  outline: 0;
}

.course-item-popup #tab-curriculum {
  display: block;
}
.course-item-popup .course-curriculum ul.curriculum-sections {
  z-index: 9;
}
.course-item-popup .lp-course-curriculum .course-section .course-item__content {
  flex-direction: column;
  row-gap: 4px;
}

/***********/
body.course-item-popup {
  overflow-y: hidden;
}
@media (max-width: 1200px) {
  body.course-item-popup #learn-press-course-curriculum {
    width: 300px;
  }
}
@media (max-width: 768px) {
  body.course-item-popup #learn-press-course-curriculum {
    margin-right: 0%;
  }
}
@media (max-width: 768px) {
  body.course-item-popup #learn-press-course-curriculum .course-curriculum {
    width: 200px;
  }
}
@media (max-width: 1300px) {
  body.course-item-popup #learn-press-course-curriculum .progress-bg {
    width: 40px;
  }
}
@media (max-width: 768px) {
  body.course-item-popup #learn-press-course-curriculum .items-progress,
  body.course-item-popup #learn-press-course-curriculum .course-progress {
    float: none;
    width: 100%;
    margin-right: 0%;
    margin-bottom: 20px;
  }
}
@media (max-width: 1300px) {
  body.course-item-popup #content-item-nav {
    left: 300px;
  }
}
@media (max-width: 768px) {
  body.course-item-popup #content-item-nav {
    left: 200px;
  }
}
@media (max-width: 1300px) {
  body.course-item-popup .section-desc {
    display: none;
  }
}
@media (max-width: 768px) {
  body.course-item-popup.wpadminbar #learn-press-content-item,
  body.course-item-popup.wpadminbar #learn-press-course-curriculum {
    top: 106px;
  }
}

body.admin-bar #popup-course {
  top: 32px;
}
body.lp-sidebar-toggle__close #sidebar-toggle::before {
  content: "\f0da";
}
body.lp-sidebar-toggle__close #popup-sidebar {
  flex: 0 0 0;
}
body.lp-sidebar-toggle__close #popup-header, body.lp-sidebar-toggle__close #popup-footer {
  left: 0;
}
@media (max-width: 768px) {
  body:not(.lp-sidebar-toggle__open) #sidebar-toggle::before {
    content: "\f0da";
  }
  body:not(.lp-sidebar-toggle__open) #popup-sidebar {
    flex: 0 0 0;
  }
}

@media screen and (max-width: 480px) {
  #popup-header, #popup-footer {
    width: 100%;
  }
  .learnpress-v4 #popup-header, .lp-4 #popup-header, .learnpress-v4 #popup-footer, .lp-4 #popup-footer {
    width: auto;
  }
  .learnpress-v4.starkid #popup-header, .learnpress-v4.starkid #popup-footer {
    width: 100%;
  }
  #popup-content {
    min-width: 100vw;
  }
}
.course-curriculum .section-header .section-meta {
  position: relative;
}

@media screen and (max-width: 1280px) {
  #popup-sidebar {
    flex-basis: 300px;
  }
  #popup-sidebar .course-curriculum {
    width: 300px;
  }
  #popup-header, #popup-footer {
    left: 300px;
  }
}
@media screen and (max-width: 782px) {
  body.admin-bar #popup-course {
    top: 46px;
  }
  #popup-course .course-item-meta .item-meta.count-questions, #popup-course .course-item-meta .item-meta.duration {
    display: none;
  }
  #popup-sidebar {
    flex-basis: 300px;
  }
  #popup-sidebar .course-curriculum {
    width: 300px;
  }
  #popup-header,
  #popup-footer {
    left: 300px;
  }
}
.content-item-wrap {
  width: 792px;
  max-width: 100%;
  margin: 0 auto;
  --lp-item-padding: 60px;
  padding-top: var(--lp-item-padding);
  padding-bottom: var(--lp-item-padding);
}
@media (max-width: 1024px) {
  .content-item-wrap {
    width: 100%;
    --lp-item-padding: 40px;
  }
}
.content-item-wrap .course-item-title {
  margin-top: 0;
  margin-bottom: 24px;
  font-size: calc(var(--lp-font-size-base, 1em) * 1.8);
  font-weight: 700;
}
@media (max-width: 767px) {
  .content-item-wrap .course-item-title {
    text-align: center;
  }
}
.content-item-wrap .content-item-summary:after {
  clear: both;
  content: "";
  display: block;
}
.content-item-wrap .content-item-description {
  margin-bottom: 30px;
}
.content-item-wrap .content-item-description .wp-video, .content-item-wrap .content-item-description .mejs-container {
  margin-bottom: 15px;
}
.content-item-wrap .content-item-description h2, .content-item-wrap .content-item-description h3, .content-item-wrap .content-item-description h4, .content-item-wrap .content-item-description h5, .content-item-wrap .content-item-description h6 {
  margin-top: 10px;
  margin-bottom: 15px;
  letter-spacing: unset;
}
.content-item-wrap .content-item-description img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}
.content-item-wrap #learn-press-quiz-app {
  margin-bottom: 40px;
}
.content-item-wrap .quiz-content {
  margin-bottom: 40px;
  color: #666;
  font-weight: 300;
}
.content-item-wrap .quiz-content img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}
.content-item-wrap .quiz-buttons {
  display: flex;
  justify-content: center;
  text-align: center;
  gap: 10px;
}
.content-item-wrap .quiz-buttons .button-right .lp-button {
  margin: 0 0 0 15px;
}
.content-item-wrap .quiz-buttons.align-center {
  display: block;
  text-align: center;
}
.content-item-wrap .quiz-buttons.align-center .button-left {
  text-align: center;
}
.content-item-wrap .quiz-buttons.align-center .button-left.fixed {
  position: fixed;
  z-index: 999;
  bottom: 0;
  left: 50%;
  width: 100%;
  max-width: 792px;
  height: auto;
  margin-left: 237px;
  transform: translateX(-50%);
  background: #fff;
  padding-bottom: 10px;
}
@media (max-width: 768px) {
  .content-item-wrap .quiz-buttons.align-center .button-left.fixed {
    width: 100% !important;
  }
}
.content-item-wrap .quiz-buttons.align-center .button-left.nav-center {
  height: 50px;
  margin-left: 0 !important;
}
.content-item-wrap .quiz-buttons:not(.infinity).is-first .prev {
  display: none;
}
.content-item-wrap .quiz-buttons:not(.infinity).is-last .next {
  display: none;
}
.content-item-wrap .questions-pagination .nav-links {
  text-align: center;
  display: flex;
  justify-content: center;
  gap: 4px;
  margin-top: 6px;
}
.content-item-wrap .questions-pagination .nav-links .page-numbers {
  padding: 10px 15px;
  margin: 0;
  color: var(--lp-color-base, #333);
  background-color: transparent;
  font-weight: normal;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  line-height: 1;
  border-radius: var(--lp-border-radius, 5px);
}
.content-item-wrap .questions-pagination .nav-links .page-numbers.dots {
  border: none;
}
.content-item-wrap .questions-pagination .nav-links .page-numbers.current, .content-item-wrap .questions-pagination .nav-links .page-numbers:hover {
  color: var(--lp-primary-color);
}
@media (max-width: 575px) {
  .content-item-wrap .questions-pagination .nav-links {
    margin-top: 10px;
  }
  .content-item-wrap .questions-pagination .nav-links .page-numbers {
    padding: 5px 10px;
  }
}

.course-curriculum .section-title {
  position: relative;
  margin-bottom: 0;
}
.course-curriculum .section-title span.show-desc {
  display: inline-block;
  position: absolute;
  top: 50%;
  right: 30px;
  width: 20px;
  height: 20px;
  transform: translate(0, -50%);
}
.course-curriculum .section-title span.show-desc::before {
  font-family: "lp-icon";
  font-size: 1.125em;
  content: "\f107";
}
.course-curriculum .section-title span.show-desc:hover::before {
  border-top-color: var(--lp-border-color, #E2E0DB);
}

@media (max-width: 575px) {
  #popup-course .quiz-attempts {
    overflow-x: auto;
  }
}

.social-share-toggle {
  position: relative;
}
.social-share-toggle .share-toggle-icon {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}
.social-share-toggle .share-toggle-icon .share-label {
  margin: 0;
  cursor: pointer;
}
.social-share-toggle .share-toggle-icon:hover {
  color: var(--lp-primary-color, #ffb606);
}
.social-share-toggle .content-widget-social-share {
  padding: 20px;
  background: #fff;
  box-shadow: 0 1px 16px 0 rgba(0, 0, 0, 0.12);
}
.social-share-toggle .lp-social-media {
  display: inline-flex;
  padding: 0;
  gap: 12px;
  margin: 0;
}
.social-share-toggle .lp-social-media > li {
  text-align: center;
  list-style: none;
}
.social-share-toggle .lp-social-media > li span {
  display: none;
}
.social-share-toggle .lp-social-media > li i {
  width: 40px;
  height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--lp-border-color, #E2E0DB);
  border-radius: 50%;
}
.social-share-toggle .lp-social-media > li i:hover {
  background-color: var(--lp-primary-color, #ffb606);
  border-color: var(--lp-primary-color, #ffb606);
  color: var(--lp-color-white, #fff);
}
.social-share-toggle .clipboard-post {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 12px;
}
.social-share-toggle .btn-clipboard {
  position: relative;
  padding: 12px 20px;
  white-space: nowrap;
}
.social-share-toggle .btn-clipboard .tooltip {
  display: none;
  position: absolute;
  z-index: 2;
  left: 50%;
  right: auto;
  bottom: 100%;
  transform: translateX(-50%);
  width: max-content;
  color: #fff;
  font-size: 0.825em;
  padding: 5px 10px;
  background: rgba(0, 0, 0, 0.72);
  border-radius: 3px;
  margin-bottom: 10px;
}
.social-share-toggle .btn-clipboard .tooltip:before {
  content: "";
  position: absolute;
  z-index: 2;
  left: 50%;
  bottom: -5px;
  margin-left: -3px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid rgba(0, 0, 0, 0.72);
}
.social-share-toggle .btn-clipboard:hover {
  background-color: #eee;
}
.social-share-toggle .btn-clipboard:hover .tooltip {
  display: block;
}
.social-share-toggle .clipboard-value {
  border: 1px solid var(--lp-border-color, #E2E0DB);
  padding: 12px 20px;
  flex: 1;
}
@media (max-width: 600px) {
  .social-share-toggle .clipboard-value {
    width: 100%;
  }
}
.social-share-toggle .wrapper-content-widget {
  visibility: hidden;
  text-align: left;
  opacity: 0;
  -webkit-transition: all 0.27s ease;
  -moz-transition: all 0.27s ease;
  -o-transition: all 0.27s ease;
  transition: all 0.27s ease;
  position: absolute;
  right: 0;
  top: auto;
  z-index: 9;
  margin-top: 20px;
}
.social-share-toggle.social-share-toggle__open .wrapper-content-widget {
  margin-top: 0;
  visibility: visible;
  opacity: 1;
}
@media (max-width: 768px) {
  .social-share-toggle .wrapper-content-widget {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
  }
  .social-share-toggle .wrapper-content-widget .content-widget-social-share {
    position: relative;
    z-index: 1;
    max-width: fit-content;
    top: 50%;
    min-width: 320px;
    transform: translateY(-50%);
    margin: 0 auto;
  }
}

.lp-overlay {
  display: none;
  position: fixed;
  z-index: 99999;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(209, 213, 219, 0.8);
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  transition-property: opacity;
}

.wp-admin .lp-modal-dialog .lp-modal-header {
  padding: 2em;
}
.wp-admin .lp-modal-dialog .lp-modal-header h3 {
  font-size: 1.4em;
}

.lp-modal-dialog {
  display: flex;
  position: fixed;
  z-index: 9999;
  top: 0;
  right: 0;
  left: 0;
  width: 100%;
  min-height: 100vh;
  text-align: center;
  align-items: center;
  justify-content: center;
}
.lp-modal-dialog .learn-press-message {
  width: auto;
  margin: 0 !important;
}
.lp-modal-dialog .lp-modal-content {
  display: inline-block;
  overflow: hidden;
  z-index: 2;
  width: auto;
  max-width: 600px;
  border-radius: 8px;
  text-align: left;
  vertical-align: middle;
  background: white;
  color: black;
}
.lp-modal-dialog .lp-modal-content h2, .lp-modal-dialog .lp-modal-content h3 {
  margin: 0;
}
.lp-modal-dialog .lp-modal-content .lp-group-step h3 {
  border-bottom: 1px solid #eee;
}
.lp-modal-dialog .lp-modal-content .main-content .terms-upgrade .pd-2em {
  padding: 0 1em;
}
.lp-modal-dialog .lp-modal-header {
  background: #7c60d9;
  border-bottom: 1px solid #eee;
  padding: 1em;
}
.lp-modal-dialog .lp-modal-header h3 {
  margin: 0;
  color: white;
  font-weight: 400;
}
.lp-modal-dialog .lp-modal-body .main-content {
  max-height: 500px;
  overflow: auto;
  overscroll-behavior: contain;
}
.lp-modal-dialog .lp-modal-body .main-content h3, .lp-modal-dialog .lp-modal-body .main-content h2, .lp-modal-dialog .lp-modal-body .main-content .pd-2em {
  padding: 1em;
}
.lp-modal-dialog .lp-modal-footer {
  padding: 20px;
  background-color: #f9fafb;
  text-align: right;
}
.lp-modal-dialog .btn-yes {
  color: #fff;
  background-color: #7c60d9;
}

#lp-modal-overlay {
  display: none;
  position: fixed;
  z-index: 999999;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  opacity: 0.5;
  background: #000;
}

#lp-modal-window {
  display: none;
  position: fixed;
  z-index: 999999;
  top: 50%;
  left: 50%;
  padding: 35px 60px 28px 60px;
  background: #fff;
  transform: translate(-50%, -50%);
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
#lp-modal-window #lp-modal-content {
  margin: 0 0 24px 0;
  color: #333;
  font-weight: 400;
  text-align: center;
}
#lp-modal-window #lp-modal-content > * {
  margin: 0 0 0.5em;
}
#lp-modal-window #lp-modal-buttons {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  justify-content: center;
}
#lp-modal-window #lp-modal-buttons .lp-button {
  position: relative;
  margin: 0 10px 0 10px;
}

.learn-press-comments {
  width: 792px;
  max-width: 100%;
  margin: 0 auto;
}
@media (max-width: 1024px) {
  .learn-press-comments {
    width: 100%;
  }
}

@media (min-width: 769px) {
  #learn-press-item-comments {
    padding-bottom: 50px;
  }
}

.learn-press-progress {
  overflow: hidden;
  position: relative;
  width: 80px;
  height: 6px;
  border-radius: 3px;
}
.learn-press-progress .progress-bg {
  overflow: hidden;
  position: relative;
  height: 6px;
  background: #ccc;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
}
.learn-press-progress .progress-bg .progress-active {
  position: absolute;
  left: 50%;
  width: 100%;
  height: 100%;
  margin-left: -100%;
  background: var(--lp-primary-color);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
}
.learn-press-progress .learn-press-progress__active {
  position: absolute;
  z-index: 1;
  left: -100%;
  width: 100%;
  height: 100%;
  border-radius: 3px;
  background: var(--lp-primary-color);
  -webkit-transition: 0.5s;
  -moz-transition: 0.5s;
  -ms-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
}
.learn-press-progress::before {
  display: block;
  position: absolute;
  z-index: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #ccc;
  content: "";
}

.course-curriculum .section-header .section-left {
  display: flex;
  align-items: center;
  gap: 16px;
}
.course-curriculum .section-header .section-left .wrapper-section-title {
  flex: 1;
}
.course-curriculum .section-header .section-left .section-toggle {
  flex: 0 0 40px;
  align-items: center;
  text-align: center;
  cursor: pointer;
  min-width: 1em;
}
.course-curriculum .section-header .section-left .section-toggle .fa-caret-down, .course-curriculum .section-header .section-left .section-toggle .lp-icon-caret-down, .course-curriculum .section-header .section-left .section-toggle .lp-icon-angle-down {
  display: none;
}
.course-curriculum .section-header .learn-press-progress {
  width: 80px;
}
.course-curriculum .section.closed .section-toggle .fa-caret-down, .course-curriculum .section.closed .section-toggle .lp-icon-caret-down, .course-curriculum .section.closed .section-toggle .lp-icon-angle-down {
  display: block;
}
.course-curriculum .section.closed .section-toggle .fa-caret-up, .course-curriculum .section.closed .section-toggle .lp-icon-caret-up {
  display: none;
}
.course-curriculum .section.closed .section-content {
  overflow: hidden;
  height: 0;
}

.lp-skeleton-animation {
  margin: 0;
  padding: 0;
  list-style: none;
}
.lp-skeleton-animation > li {
  width: 100%;
  height: 16px;
  margin-top: 15px;
  border-radius: 2px;
  background: linear-gradient(90deg, hsla(0, 0%, 74.5%, 0.2) 25%, hsla(0, 0%, 50.6%, 0.24) 37%, hsla(0, 0%, 74.5%, 0.2) 63%);
  background-size: 400% 100%;
  list-style: none;
  animation: lp-skeleton-loading 1.4s ease infinite;
}

@keyframes lp-skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  to {
    background-position: 0 50%;
  }
}
.lp-material-skeleton {
  overflow: auto;
}
.lp-material-skeleton .course-material-table {
  width: 100%;
  display: none;
}
.lp-material-skeleton .course-material-table th {
  text-align: center;
}
.lp-material-skeleton .course-material-table th:first-child {
  text-align: left;
}
.lp-material-skeleton .course-material-table tr td:not(:first-child) {
  text-align: center;
}
.lp-material-skeleton .course-material-table tfoot td {
  text-align: left;
  font-weight: bold;
}
.lp-material-skeleton .lp-loadmore-material.lp-button {
  display: none;
  margin-top: 20px;
  padding: 12px 24px;
}

#popup-content .lp-material-skeleton .lp-loadmore-material.lp-button {
  display: none;
  margin-top: 20px;
  padding: 12px 24px;
}

.learnpress.theme-twentytwentytwo .wp-site-blocks, .learnpress.twentytwentytwo .wp-site-blocks {
  max-width: none;
  padding-right: 0;
  padding-left: 0;
}
.learnpress.theme-twentytwentytwo .wp-site-blocks > .wp-block-group, .learnpress.twentytwentytwo .wp-site-blocks > .wp-block-group {
  max-width: none;
  margin-right: auto;
  margin-left: auto;
}
.learnpress.theme-twentytwentytwo .lp-archive-courses, .learnpress.twentytwentytwo .lp-archive-courses {
  max-width: none;
}
.learnpress.theme-twentytwentytwo .wp-container-11 .alignfull, .learnpress.theme-twentytwentytwo .wp-container-9 .alignfull, .learnpress.theme-twentytwentytwo .wp-container-12 .alignfull, .learnpress.twentytwentytwo .wp-container-11 .alignfull, .learnpress.twentytwentytwo .wp-container-9 .alignfull, .learnpress.twentytwentytwo .wp-container-12 .alignfull {
  max-width: 100%;
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.learnpress.twentytwentyone .lp-archive-courses a:focus, .learnpress.theme-twentytwentyone .lp-archive-courses a:focus {
  background: transparent !important;
  text-decoration: none !important;
  outline: none !important;
}
.learnpress.twentytwentyone .pagination, .learnpress.twentytwentyone .comments-pagination, .learnpress.theme-twentytwentyone .pagination, .learnpress.theme-twentytwentyone .comments-pagination {
  border-top: 0;
  padding-top: 0;
}

@media (max-width: 1199px) {
  .learnpress.twentytwentytwo .wp-site-blocks .wp-block-template-part, .learnpress.theme-twentytwentyone .wp-site-blocks .wp-block-template-part {
    padding-left: 15px;
    padding-right: 15px;
  }
}
.learnpress-block-pagination,
.learn-press-pagination {
  margin: 20px 0;
  text-align: center;
}
.learnpress-block-pagination .page-numbers,
.learn-press-pagination .page-numbers {
  display: inline-block;
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  background: transparent;
  list-style: none;
}
.learnpress-block-pagination .page-numbers > li,
.learn-press-pagination .page-numbers > li {
  display: inline-block;
  margin: 0;
}
.learnpress-block-pagination .page-numbers > li .page-numbers,
.learn-press-pagination .page-numbers > li .page-numbers {
  float: unset;
  padding: 0 12px;
  color: #666;
  text-decoration: none;
}
.learnpress-block-pagination .page-numbers > li .page-numbers.current,
.learn-press-pagination .page-numbers > li .page-numbers.current {
  color: var(--lp-primary-color);
  font-weight: 400;
}
.learnpress-block-pagination .page-numbers > li .page-numbers:hover,
.learn-press-pagination .page-numbers > li .page-numbers:hover {
  color: var(--lp-primary-color);
}

ul.learn-press-breadcrumb {
  max-width: var(--lp-container-max-width);
  width: 100%;
  margin: 0 auto;
  padding: 1em var(--lp-cotainer-padding);
  list-style: none;
}
ul.learn-press-breadcrumb li {
  display: inline-block;
  margin: 0;
}
ul.learn-press-breadcrumb li a:hover {
  color: var(--lp-primary-color);
}
ul.learn-press-breadcrumb i {
  margin: 0 8px;
}
ul.learn-press-breadcrumb a {
  color: inherit;
}

/*  end reset css */
@media (max-width: 1024px) {
  body.learnpress-page {
    --lp-cotainer-padding: 1rem;
  }
}
footer {
  clear: both;
}

.margin-bottom {
  margin-bottom: 20px;
}

.hide-if-js {
  display: none !important;
}

.clearfix::after {
  display: block;
  clear: both;
  content: "";
}

.lp-form-fields {
  margin: 0;
  padding: 0;
  list-style: none;
}
.lp-form-fields .form-field {
  margin: 0 0 20px 0;
  list-style: none;
}

.lp-entry-content .course-tabs {
  margin-bottom: 40px;
}

/**
 * Forms
 */
form.retake-course,
form.enroll-course,
form.purchase-course {
  display: inline-flex;
  flex-direction: column;
  gap: 10px;
}

/* */
.table-orders th, .table-orders td {
  padding: 5px 10px;
}

.form-button {
  display: inline-block;
}

ul.list-table-nav {
  display: flex;
  margin-left: 0;
  list-style: none;
}
ul.list-table-nav .nav-text {
  text-align: left;
  flex: 1;
}
ul.list-table-nav .nav-pages {
  text-align: right;
  flex: 1;
}
ul.list-table-nav .nav-pages .learn-press-pagination {
  display: inline-block;
  margin-bottom: 0;
}

.primary-color {
  color: var(--lp-primary-color);
}

.primary-color-before::before {
  color: var(--lp-primary-color);
}

.primary-color-after::after {
  color: var(--lp-primary-color);
}

.primary-background-color {
  background: var(--lp-primary-color);
}

.primary-background-color {
  background: var(--lp-primary-color);
}

.course-origin-price {
  margin-right: 5px;
  font-size: 85%;
  text-decoration: line-through;
}

.content-item-wrap #comments {
  margin-right: 0;
  margin-left: 0;
}
.content-item-wrap #comments #comment {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.course-tabs input[name=learn-press-course-tab-radio],
.course-tabs .course-tab-panel {
  display: none;
}

.no-event {
  pointer-events: none;
}

@media screen and (max-width: 768px) {
  ul.learn-press-courses .course {
    width: 48%;
  }
}
@media screen and (max-width: 600px) {
  ul.learn-press-courses .course {
    width: 100%;
    margin-right: 0;
  }
}
.course-price:empty {
  display: none !important;
}