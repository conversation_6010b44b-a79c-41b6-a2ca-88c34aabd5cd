(()=>{"use strict";const e="lp-hidden",s=(s,a=0)=>{s&&(a?s.classList.remove(e):s.classList.add(e))},a={};let t;"undefined"!=typeof lpDataAdmin&&(t=lpDataAdmin.lp_rest_url,a.admin={apiAdminNotice:t+"lp/v1/admin/tools/admin-notices",apiAdminOrderStatic:t+"lp/v1/orders/statistic",apiAddons:t+"lp/v1/addon/all",apiAddonAction:t+"lp/v1/addon/action-n",apiAddonsPurchase:t+"lp/v1/addon/info-addons-purchase",apiSearchCourses:t+"lp/v1/admin/tools/search-course",apiSearchUsers:t+"lp/v1/admin/tools/search-user",apiAssignUserCourse:t+"lp/v1/admin/tools/assign-user-course",apiUnAssignUserCourse:t+"lp/v1/admin/tools/unassign-user-course"}),"undefined"!=typeof lpData&&(t=lpData.lp_rest_url,a.frontend={apiWidgets:t+"lp/v1/widgets/api",apiCourses:t+"lp/v1/courses/archive-course",apiAJAX:t+"lp/v1/load_content_via_ajax/",apiProfileCoverImage:t+"lp/v1/profile/cover-image"}),t&&(a.apiCourses=t+"lp/v1/courses/");const o=a;function n(){const e=document.querySelectorAll(".learnpress-widget-wrapper:not(.loaded)");if(!e.length)return;e.forEach(e=>{e.classList.add("loaded"),e.classList.contains("learnpress-widget-wrapper__restapi")&&(e=>{const a=lpData.urlParams.lang?`?lang=${lpData.urlParams.lang}`:"",t=e.dataset.widget?JSON.parse(e.dataset.widget):"",n=o.frontend.apiWidgets+a,r={method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...t,params_url:lpData.urlParams})};0!==parseInt(lpData.user_id)&&(r.headers["X-WP-Nonce"]=lpData.nonce),((e,s={},a={})=>{"function"==typeof a.before&&a.before(),fetch(e,{method:"GET",...s}).then(e=>e.json()).then(e=>{"function"==typeof a.success&&a.success(e)}).catch(e=>{"function"==typeof a.error&&a.error(e)}).finally(()=>{"function"==typeof a.completed&&a.completed()})})(n,r,{before:()=>{},success:a=>{const{data:t,status:o,message:n}=a;t&&"success"===o?e.insertAdjacentHTML("afterbegin",t):n&&e.insertAdjacentHTML("afterbegin",`<div class="lp-ajax-message error" style="display:block">${n}</div>`);const r=e.querySelector(".course-filter-submit.lp-btn-done");r&&(window.outerWidth<=991?s(r,1):s(r,0))},error:e=>{},completed:()=>{const s=e.querySelector(".lp-skeleton-animation");s&&s.remove();const a=document.querySelector(".lp-form-course-filter");a&&window.lpCourseFilter.countFieldsSelected(a)}})})(e)})}n(),document.addEventListener("readystatechange",e=>{n()}),document.addEventListener("DOMContentLoaded",()=>{n()})})();