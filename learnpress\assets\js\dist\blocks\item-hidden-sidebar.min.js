(()=>{"use strict";const e=window.React,t=(window.wp.i18n,window.wp.components,window.wp.blockEditor),r=r=>{const n=(0,t.useBlockProps)();return(0,e.createElement)("div",{...n},(0,e.createElement)("input",{type:"checkbox",id:"sidebar-toggle",title:"Show/Hide curriculum"}))},n=e=>null,s=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/item-hidden-sidebar","title":"Item Hidden Sidebar","category":"learnpress-category","icon":"hidden","description":"Renders template Single Course Legacy PHP templates.","textdomain":"learnpress","keywords":["item hidden sidebar course","learnpress"],"usesContext":[],"supports":{"align":true}}'),o=window.wp.blocks,i=window.wp.data;let l=null;var c,a,d;c=["learnpress/learnpress//single-lp_course_item"],a=s,d=e=>{(0,o.registerBlockType)(e.name,{...e,edit:r,save:n})},(0,i.subscribe)(()=>{const e={...a},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&l!==r&&(l=r,(0,o.getBlockType)(e.name)&&((0,o.unregisterBlockType)(e.name),c.includes(r)?(e.ancestor=null,d(e)):(e.ancestor||(e.ancestor=[]),d(e))))}),(0,o.registerBlockType)(s.name,{...s,edit:r,save:n})})();