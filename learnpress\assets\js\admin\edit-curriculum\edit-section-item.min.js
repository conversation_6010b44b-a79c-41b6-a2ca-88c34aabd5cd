import*as lpEditCurriculumShare from"./share.js";import <PERSON><PERSON>lert from"sweetalert2";import Sortable from"sortablejs";const className={...lpEditCurriculumShare.className,elSectionListItems:".section-list-items",elItemClone:".section-item.clone",elSectionItem:".section-item",elBtnSelectItemType:".lp-btn-select-item-type",elAddItemTypeClone:".lp-add-item-type.clone",elSectionActions:".section-actions",elAddItemType:".lp-add-item-type",elAddItemTypeTitleInput:".lp-add-item-type-title-input",elBtnAddItemCancel:".lp-btn-add-item-cancel",elBtnAddItem:".lp-btn-add-item",elItemTitleInput:".lp-item-title-input",elBtnUpdateItemTitle:".lp-btn-update-item-title",elBtnCancelUpdateTitle:".lp-btn-cancel-update-item-title",elBtnDeleteItem:".lp-btn-delete-item",elBtnShowPopupItemsToSelect:".lp-btn-show-popup-items-to-select",elPopupItemsToSelectClone:".lp-popup-items-to-select.clone",elPopupItemsToSelect:".lp-popup-items-to-select",elSelectItem:".lp-select-item",elListItemsWrap:".list-items-wrap",elListItems:".list-items",elBtnAddItemsSelected:".lp-btn-add-items-selected",elBtnCountItemsSelected:".lp-btn-count-items-selected",elBtnBackListItems:".lp-btn-back-to-select-items",elHeaderCountItemSelected:".header-count-items-selected",elListItemsSelected:".list-items-selected",elItemSelectedClone:".li-item-selected.clone",elItemSelected:".li-item-selected",elBtnSetPreviewItem:".lp-btn-set-preview-item"};let{courseId:courseId,elCurriculumSections:elCurriculumSections,showToast:showToast,lpUtils:lpUtils,updateCountItems:updateCountItems}=lpEditCurriculumShare;const idUrlHandle="edit-course-curriculum",init=()=>{({courseId:courseId,elCurriculumSections:elCurriculumSections,showToast:showToast,lpUtils:lpUtils,updateCountItems:updateCountItems}=lpEditCurriculumShare)},addItemType=(e,t)=>{const s=t.closest(`${className.elBtnSelectItemType}`);if(!s)return;const l=s.dataset.itemType,c=s.dataset.placeholder,o=s.dataset.buttonAddText,i=s.closest(`${className.elSection}`).querySelector(`${className.elSectionActions}`),a=i.querySelector(`${className.elAddItemTypeClone}`).cloneNode(!0),n=a.querySelector(`${className.elAddItemTypeTitleInput}`),d=a.querySelector(`${className.elBtnAddItem}`);a.classList.remove("clone"),a.classList.add(l),lpUtils.lpShowHideEl(a,1),n.setAttribute("placeholder",c),n.dataset.itemType=l,d.textContent=o,i.insertAdjacentElement("beforebegin",a),n.focus()},cancelAddItemType=(e,t)=>{if(!t.closest(`${className.elBtnAddItemCancel}`))return;const s=t.closest(`${className.elAddItemType}`);s&&s.remove()},addItemToSection=(e,t)=>{let s=!1;if((t.closest(`${className.elBtnAddItem}`)||t.closest(`${className.elAddItemTypeTitleInput}`)&&"Enter"===e.key)&&(s=!0),!s)return;e.preventDefault();const l=t.closest(`${className.elAddItemType}`),c=l.closest(`${className.elSection}`),o=c.dataset.sectionId,i=l.querySelector(`${className.elAddItemTypeTitleInput}`),a=i.value.trim(),n=i.dataset.itemType,d=i.dataset.messEmptyTitle;if(0===a.length)return void showToast(d,"error");const r=c.querySelector(`${className.elItemClone}`),m=r.cloneNode(!0),p=m.querySelector(`${className.elItemTitleInput}`);m.classList.remove("clone"),m.classList.add(n),m.dataset.itemType=n,lpUtils.lpShowHideEl(m,1),lpUtils.lpSetLoadingEl(m,1),p.value=a,p.dataset.old=a,r.insertAdjacentElement("beforebegin",m),l.remove();const u={success:e=>{const{message:t,status:s,data:l}=e;if(showToast(t,s),"error"===s)m.remove();else if("success"===s){const{section_item:e,item_link:t}=l||{};m.dataset.itemId=e.item_id||0,m.querySelector(".edit-link").setAttribute("href",t||"")}},error:e=>{showToast(e,"error"),m.remove()},completed:()=>{lpUtils.lpSetLoadingEl(m,0),updateCountItems(c)}},S={course_id:courseId,action:"create_item_add_to_section",section_id:o,item_title:a,item_type:n,args:{id_url:idUrlHandle}};window.lpAJAXG.fetchAJAX(S,u)},changeTitle=(e,t)=>{const s=t.closest(`${className.elItemTitleInput}`);if(!s)return;const l=s.closest(`${className.elSectionItem}`);if(!l)return;s.value.trim()===(s.dataset.old||"")?l.classList.remove("editing"):l.classList.add("editing")},focusTitleInput=(e,t,s=!0)=>{const l=t.closest(`${className.elItemTitleInput}`);if(!l)return;const c=l.closest(`${className.elSectionItem}`);c&&(s?c.classList.add("focus"):c.classList.remove("focus"))},changeTitleAddNew=(e,t)=>{const s=t.closest(`${className.elAddItemTypeTitleInput}`);if(!s)return;const l=s.closest(`${className.elAddItemType}`);if(!l)return;const c=l.querySelector(`${className.elBtnAddItem}`);if(!c)return;0===s.value.trim().length?c.classList.remove("active"):c.classList.add("active")},updateTitle=(e,t)=>{let s=!1;if((t.closest(`${className.elBtnUpdateItemTitle}`)||t.closest(`${className.elItemTitleInput}`)&&"Enter"===e.key)&&(s=!0),!s)return;e.preventDefault();const l=t.closest(`${className.elSectionItem}`);if(!l)return;const c=l.closest(`${className.elSection}`);if(!c)return;const o=l.querySelector(`${className.elItemTitleInput}`);if(!o)return;const i=l.dataset.itemId,a=l.dataset.itemType,n=o.value.trim(),d=o.dataset.old,r=o.dataset.messEmptyTitle;if(0===n.length)return void showToast(r,"error");if(n===d)return;o.blur(),lpUtils.lpSetLoadingEl(l,1);const m={success:e=>{const{message:t,status:s}=e;"success"===s?o.dataset.old=n:o.value=d,showToast(t,s)},error:e=>{showToast(e,"error")},completed:()=>{lpUtils.lpSetLoadingEl(l,0),l.classList.remove("editing")}},p={course_id:courseId,action:"update_item_of_section",section_id:c.dataset.sectionId,item_id:i,item_type:a,item_title:n,args:{id_url:idUrlHandle}};window.lpAJAXG.fetchAJAX(p,m)},cancelUpdateTitle=(e,t)=>{const s=t.closest(`${className.elBtnCancelUpdateTitle}`);if(!s)return;const l=s.closest(`${className.elSectionItem}`),c=l.querySelector(`${className.elItemTitleInput}`);c.value=c.dataset.old||"",l.classList.remove("editing")},deleteItem=(e,t)=>{const s=t.closest(`${className.elBtnDeleteItem}`);if(!s)return;const l=s.closest(`${className.elSectionItem}`);if(!l)return;const c=l.dataset.itemId,o=l.closest(`${className.elSection}`),i=o.dataset.sectionId;SweetAlert.fire({title:s.dataset.title,text:s.dataset.content,icon:"warning",showCloseButton:!0,showCancelButton:!0,cancelButtonText:lpDataAdmin.i18n.cancel,confirmButtonText:lpDataAdmin.i18n.yes,reverseButtons:!0}).then(e=>{if(e.isConfirmed){lpUtils.lpSetLoadingEl(l,1);const e={success:e=>{const{message:t,status:s}=e;showToast(t,s),"success"===s&&l.remove()},error:e=>{showToast(e,"error")},completed:()=>{lpUtils.lpSetLoadingEl(l,0),updateCountItems(o)}},t={course_id:courseId,action:"delete_item_from_section",section_id:i,item_id:c,args:{id_url:idUrlHandle}};window.lpAJAXG.fetchAJAX(t,e)}})},sortAbleItem=()=>{const e=elCurriculumSections.querySelectorAll(`${className.elSectionListItems}`);let t,s=0,l=0,c=0;e.forEach(e=>{new Sortable(e,{handle:".drag",animation:150,group:{name:"shared"},onEnd:e=>{const o=[],i=e.item;c=i.closest(`${className.elSection}`).dataset.sectionId;const a={course_id:courseId,args:{id_url:idUrlHandle}};l===c?(a.action="update_items_position",a.section_id=c):(a.action="update_item_section_and_position",a.item_id_change=s,a.section_id_new_of_item=c,a.section_id_old_of_item=l);const n=elCurriculumSections.querySelector(`.section[data-section-id="${c}"]`);n.querySelectorAll(`${className.elSectionItem}`).forEach(e=>{const t=parseInt(e.dataset.itemId||0);0!==t&&o.push(t)}),a.items_position=o;const d={success:e=>{const{message:t,status:s}=e;showToast(t,s)},error:e=>{showToast(e,"error")},completed:()=>{lpUtils.lpSetLoadingEl(i,0),updateCountItems(n),l!==c&&updateCountItems(t)}};lpUtils.lpSetLoadingEl(i,1),window.lpAJAXG.fetchAJAX(a,d)},onMove:e=>{},onChoose:e=>{const c=e.item;s=c.dataset.itemId,t=c.closest(`${className.elSection}`),l=t.dataset.sectionId},onUpdate:e=>{}})})};let sectionIdSelected,elPopupSelectItems;const showPopupItemsToSelect=(e,t)=>{const s=t.closest(`${className.elBtnShowPopupItemsToSelect}`);if(!s)return;const l=s.closest(`${className.elSection}`);sectionIdSelected=l.dataset.sectionId;const c=document.querySelector(`${className.elPopupItemsToSelectClone}`);elPopupSelectItems=c.cloneNode(!0),elPopupSelectItems.classList.remove("clone"),lpUtils.lpShowHideEl(elPopupSelectItems,1),SweetAlert.fire({html:elPopupSelectItems,showConfirmButton:!1,showCloseButton:!0,width:"60%",customClass:{popup:"lp-select-items-popup",htmlContainer:"lp-select-items-html-container",container:"lp-select-items-container"},willOpen:()=>{elPopupSelectItems.querySelector('li[data-type="lp_lesson"]').click()}}).then(e=>{e.isDismissed})},chooseTabItemsType=(e,t)=>{const s=t.closest(".tab");if(!s)return;e.preventDefault();const l=s.closest(".tabs");if(!l)return;const c=l.closest(`${className.elPopupItemsToSelect}`),o=c.querySelector(".lp-search-title-item"),i=s.dataset.type;l.querySelectorAll(".tab").forEach(e=>{e.classList.contains("active")&&e.classList.remove("active")}),s.classList.add("active"),o.value="";const a=c.querySelector(`${className.LPTarget}`),n=window.lpAJAXG.getDataSetCurrent(a);n.args.item_type=i,n.args.paged=1,n.args.item_selecting=itemsSelectedData||[],window.lpAJAXG.setDataSetCurrent(a,n),window.lpAJAXG.showHideLoading(a,1),window.lpAJAXG.fetchAJAX(n,{success:e=>{const{data:t}=e;a.innerHTML=t.content||""},error:e=>{showToast(e,"error")},completed:()=>{window.lpAJAXG.showHideLoading(a,0),watchItemsSelectedDataChange()}})};let itemsSelectedData=[];const selectItemsFromList=(e,t)=>{const s=t.closest(`${className.elSelectItem}`);if(!s)return;const l=s.querySelector('input[type="checkbox"]');if("INPUT"!==t.tagName)return void l.click();if(!s.closest(`${className.elListItems}`))return;const c={item_id:l.value,item_type:l.dataset.type||"",item_title:l.dataset.title||"",item_edit_link:l.dataset.editLink||""};if(l.checked){itemsSelectedData.some(e=>e.item_id===c.item_id)||itemsSelectedData.push(c)}else{const e=itemsSelectedData.findIndex(e=>e.item_id===c.item_id);-1!==e&&itemsSelectedData.splice(e,1)}watchItemsSelectedDataChange()};let timeSearchTitleItem;const searchTitleItemToSelect=(e,t)=>{const s=t.closest(".lp-search-title-item");if(!s)return;const l=s.closest(`${className.elPopupItemsToSelect}`);if(!l)return;const c=l.querySelector(`${className.LPTarget}`);clearTimeout(timeSearchTitleItem),timeSearchTitleItem=setTimeout(()=>{const e=window.lpAJAXG.getDataSetCurrent(c);e.args.search_title=s.value.trim(),e.args.item_selecting=itemsSelectedData,window.lpAJAXG.showHideLoading(c,1),window.lpAJAXG.fetchAJAX(e,{success:e=>{const{data:t}=e;c.innerHTML=t.content||""},error:e=>{showToast(e,"error")},completed:()=>{window.lpAJAXG.showHideLoading(c,0)}})},1e3)},showItemsSelected=(e,t)=>{const s=t.closest(`${className.elBtnCountItemsSelected}`);if(!s)return;const l=s.closest(`${className.elPopupItemsToSelect}`);if(!l)return;const c=l.querySelector(`${className.elBtnBackListItems}`),o=l.querySelector(".tabs"),i=l.querySelector(`${className.elListItemsWrap}`),a=l.querySelector(`${className.elHeaderCountItemSelected}`),n=l.querySelector(`${className.elListItemsSelected}`),d=n.querySelector(`${className.elItemSelectedClone}`);a.innerHTML=s.innerHTML,lpUtils.lpShowHideEl(i,0),lpUtils.lpShowHideEl(s,0),lpUtils.lpShowHideEl(o,0),lpUtils.lpShowHideEl(c,1),lpUtils.lpShowHideEl(a,1),lpUtils.lpShowHideEl(n,1),n.querySelectorAll(`${className.elItemSelected}:not(.clone)`).forEach(e=>{e.remove()}),itemsSelectedData.forEach(e=>{const t=d.cloneNode(!0);t.classList.remove("clone"),t.dataset.id=e.item_id,t.dataset.type=e.item_type||"",t.querySelector(".item-title").textContent=e.item_title||"",t.querySelector(".item-id").textContent=e.item_id||"",t.querySelector(".item-type").textContent=e.item_type||"",lpUtils.lpShowHideEl(t,1),d.insertAdjacentElement("beforebegin",t)})},backToSelectItems=(e,t)=>{const s=t.closest(`${className.elBtnBackListItems}`);if(!s)return;const l=s.closest(`${className.elPopupItemsToSelect}`),c=l.querySelector(`${className.elBtnCountItemsSelected}`),o=l.querySelector(".tabs"),i=l.querySelector(`${className.elListItemsWrap}`),a=l.querySelector(`${className.elHeaderCountItemSelected}`),n=l.querySelector(`${className.elListItemsSelected}`);lpUtils.lpShowHideEl(c,1),lpUtils.lpShowHideEl(i,1),lpUtils.lpShowHideEl(o,1),lpUtils.lpShowHideEl(s,0),lpUtils.lpShowHideEl(a,0),lpUtils.lpShowHideEl(n,0)},removeItemSelected=(e,t)=>{const s=t.closest(`${className.elItemSelected}`);if(!s)return;const l=s.dataset.id,c=(s.dataset.type,itemsSelectedData.findIndex(e=>e.item_id===l));-1!==c&&itemsSelectedData.splice(c,1),s.remove(),watchItemsSelectedDataChange()},watchItemsSelectedDataChange=()=>{if(!elPopupSelectItems)return;const e=elPopupSelectItems.querySelector(`${className.elBtnAddItemsSelected}`),t=elPopupSelectItems.querySelector(`${className.elBtnCountItemsSelected}`),s=t.querySelector("span"),l=elPopupSelectItems.querySelector(`${className.elHeaderCountItemSelected}`);0!==itemsSelectedData.length?(t.disabled=!1,e.disabled=!1,s.textContent=`(${itemsSelectedData.length})`,l.innerHTML=t.innerHTML):(t.disabled=!0,e.disabled=!0,s.textContent="",l.textContent="");elPopupSelectItems.querySelector(`${className.elListItems}`).querySelectorAll('input[type="checkbox"]').forEach(e=>{const t=e.value,s=(e.dataset.type,e.dataset.title,itemsSelectedData.some(e=>e.item_id===t));e.checked=s})},addItemsSelectedToSection=(e,t)=>{const s=t.closest(`${className.elBtnAddItemsSelected}`);if(!s)return;if(!s.closest(`${className.elPopupItemsToSelect}`))return;const l=document.querySelector(`.section[data-section-id="${sectionIdSelected}"]`),c=l.querySelector(`${className.elItemClone}`);itemsSelectedData.forEach(e=>{const t=c.cloneNode(!0),s=t.querySelector(`${className.elItemTitleInput}`);t.dataset.itemId=e.item_id,t.classList.add(e.item_type),t.classList.remove("clone"),t.dataset.itemType=e.item_type,t.querySelector(".edit-link").setAttribute("href",e.item_edit_link||""),s.value=e.item_title||"",lpUtils.lpSetLoadingEl(t,1),lpUtils.lpShowHideEl(t,1),c.insertAdjacentElement("beforebegin",t)}),SweetAlert.close();const o={course_id:courseId,action:"add_items_to_section",section_id:sectionIdSelected,items:itemsSelectedData,args:{id_url:idUrlHandle}};window.lpAJAXG.fetchAJAX(o,{success:e=>{const{message:t,status:s}=e;showToast(t,s),"error"===s&&itemsSelectedData.forEach(e=>{const t=l.querySelector(`${className.elSectionItem}[data-item-id="${e.item_id}"]`);t&&t.remove()})},error:e=>{showToast(e,"error")},completed:()=>{itemsSelectedData.forEach(e=>{const t=l.querySelector(`${className.elSectionItem}[data-item-id="${e.item_id}"]`);lpUtils.lpSetLoadingEl(t,0)}),itemsSelectedData=[],updateCountItems(l)}})},updatePreviewItem=(e,t)=>{const s=t.closest(`${className.elBtnSetPreviewItem}`);if(!s)return;const l=s.closest(`${className.elSectionItem}`);if(!l)return;const c=s.querySelector("a");c.classList.toggle("lp-icon-eye"),c.classList.toggle("lp-icon-eye-slash");const o=!c.classList.contains("lp-icon-eye-slash"),i=l.dataset.itemId,a=l.dataset.itemType;lpUtils.lpSetLoadingEl(l,1);const n={success:e=>{const{message:t,status:s}=e;showToast(t,s),"error"===s&&(c.classList.toggle("lp-icon-eye"),c.classList.toggle("lp-icon-eye-slash"))},error:e=>{showToast(e,"error"),c.classList.toggle("lp-icon-eye"),c.classList.toggle("lp-icon-eye-slash")},completed:()=>{lpUtils.lpSetLoadingEl(l,0)}},d={course_id:courseId,action:"update_item_preview",item_id:i,item_type:a,enable_preview:o?1:0,args:{id_url:idUrlHandle}};window.lpAJAXG.fetchAJAX(d,n)};export{init,addItemType,cancelAddItemType,addItemToSection,changeTitle,focusTitleInput,changeTitleAddNew,updateTitle,cancelUpdateTitle,deleteItem,sortAbleItem,showPopupItemsToSelect,chooseTabItemsType,selectItemsFromList,showItemsSelected,backToSelectItems,removeItemSelected,searchTitleItemToSelect,addItemsSelectedToSection,updatePreviewItem};