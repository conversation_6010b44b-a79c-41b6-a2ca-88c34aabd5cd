(()=>{"use strict";const e=window.React,t=window.wp.i18n,n=window.wp.components,l=window.wp.blockEditor,r=r=>{const{attributes:o={},setAttributes:s}=r,a=(0,l.useBlockProps)({style:{textAlign:o.textAlign,width:"100%"}});let i=a.className;return i=i.split(" ").filter(e=>e.startsWith("align")).join(" "),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.BlockControls,null,(0,e.createElement)(l.AlignmentToolbar,{value:o.textAlign,onChange:e=>s({textAlign:e})}),(0,e.createElement)(l.JustifyToolbar,{value:o.justifyContent,onChange:e=>s({justifyContent:e})}),(0,e.createElement)(l.<PERSON>ert<PERSON>AlignmentToolbar,{value:o.alignItems,onChange:e=>s({alignItems:e})})),(0,e.createElement)(l.<PERSON>,null,(0,e.createElement)(n.PanelBody,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(n.__experimentalToggleGroupControl,{label:(0,t.__)("Width","learnpress"),value:o.width||"100",onChange:e=>{s({width:e||"100"})},isBlock:!0},(0,e.createElement)(n.__experimentalToggleGroupControlOption,{value:"25",label:"25%"}),(0,e.createElement)(n.__experimentalToggleGroupControlOption,{value:"50",label:"50%"}),(0,e.createElement)(n.__experimentalToggleGroupControlOption,{value:"75",label:"75%"}),(0,e.createElement)(n.__experimentalToggleGroupControlOption,{value:"100",label:"100%"})))),(0,e.createElement)("div",{className:i,style:{display:"flex",textAlign:o.textAlign,alignItems:{top:"flex-start",center:"center",bottom:"flex-end"}[o.alignItems]||"flex-start",justifyContent:o.justifyContent}},(0,e.createElement)("a",{style:{width:o.width?`${o.width}%`:void 0}},(0,e.createElement)("button",{...a},(0,t.__)("Buy Now","learnpress")))))},o=e=>null,s=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-button","title":"Course Button","category":"learnpress-course-elements","icon":"button","description":"Renders template Button Course PHP templates.","textdomain":"learnpress","keywords":["button single course","learnpress"],"ancestor":["learnpress/single-course"],"usesContext":["lpCourseData"],"attributes":{"textAlign":{"type":"string","default":"center"},"justifyContent":{"type":"string","default":"center"},"alignItems":{"type":"string","default":"top"},"width":{"type":"string","default":"100"}},"supports":{"multiple":true,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":true,"text":true,"__experimentalDefaultControls":{"background":true,"text":true}},"__experimentalBorder":{"color":true,"radius":true,"width":true,"__experimentalDefaultControls":{"width":false,"color":false,"radius":false}},"spacing":{"margin":true,"padding":true,"content":true,"__experimentalDefaultControls":{"margin":false,"padding":false,"content":true}}}}'),a=window.wp.blocks,i=window.wp.data;let u=null;var p,c,g;p=["learnpress/learnpress//single-lp_course","learnpress/learnpress//single-lp_course-offline"],c=s,g=e=>{(0,a.registerBlockType)(e.name,{...e,edit:r,save:o})},(0,i.subscribe)(()=>{const e={...c},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const n=t.getCurrentPostId();null!==n&&u!==n&&(u=n,(0,a.getBlockType)(e.name)&&((0,a.unregisterBlockType)(e.name),p.includes(n)?(e.ancestor=null,g(e)):(e.ancestor||(e.ancestor=[]),g(e))))}),(0,a.registerBlockType)(s.name,{...s,edit:r,save:o})})();