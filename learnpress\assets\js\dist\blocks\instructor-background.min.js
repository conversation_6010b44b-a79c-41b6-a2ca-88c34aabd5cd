(()=>{"use strict";const e=window.React,t=window.wp.i18n,r=window.wp.blockEditor,n=window.wp.components,l=l=>{const s=(0,r.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(r.<PERSON>,null,(0,e.createElement)(n.Panel<PERSON>ody,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(n.SelectControl,{label:(0,t.__)("Background Position","learnpress"),value:l.attributes.position,options:[{label:"Center",value:"center"},{label:"Left",value:"left"},{label:"Right",value:"right"},{label:"Top",value:"top"},{label:"Bottom",value:"bottom"}],onChange:e=>l.setAttributes({position:e||""})}),(0,e.createElement)(n.<PERSON>,{label:(0,t.__)("Background Size","learnpress"),value:l.attributes.size,options:[{label:"Auto",value:"auto"},{label:"Cover",value:"cover"},{label:"Contain",value:"contain"},{label:"Unset",value:"unset"}],onChange:e=>l.setAttributes({size:e||""})}),(0,e.createElement)(n.ToggleControl,{label:(0,t.__)("Background Repeat","learnpress"),checked:!!l.attributes.repeat,onChange:e=>{l.setAttributes({repeat:!!e})}}))),(0,e.createElement)("div",{...s},(0,e.createElement)("div",{className:"lp-user-cover-image_background"},(0,e.createElement)("img",{src:"https://placehold.co/1280x285?text=Background"}))))},s=e=>null,o=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/instructor-background","title":"Instructor Background","category":"learnpress-course-elements","icon":"format-image","description":"Renders template Instructor Background PHP templates.","textdomain":"learnpress","keywords":["instructor background single","learnpress"],"ancestor":["learnpress/single-instructor"],"usesContext":[],"attributes":{"position":{"type":"string","default":"center"},"size":{"type":"string","default":"cover"},"repeat":{"type":"boolean","default":false}},"supports":{"inserter":true,"reusable":true,"reorder":true,"html":false,"multiple":false}}'),a=window.wp.blocks,i=window.wp.data;let c=null;const u=[Number(lpDataAdmin?.single_instructor_id)];var p,d,g;p=u,d=o,g=e=>{(0,a.registerBlockType)(e.name,{...e,edit:l,save:s})},(0,i.subscribe)(()=>{const e={...d},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&c!==r&&(c=r,(0,a.getBlockType)(e.name)&&((0,a.unregisterBlockType)(e.name),p.includes(r)?(e.ancestor=null,g(e)):(e.ancestor||(e.ancestor=[]),g(e))))}),(0,a.registerBlockType)(o.name,{...o,edit:l,save:s})})();