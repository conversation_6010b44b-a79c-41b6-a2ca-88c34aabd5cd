<?php
/**
 * Custom Template for displaying user profile content.
 *
 * This template overrides the default LearnPress profile content
 * to implement a custom design with progress bars, certificates, and course information.
 *
 * <AUTHOR> Theme
 * @package  Blocksy-Child/LearnPress
 * @version  1.0.0
 */

defined( 'ABSPATH' ) || exit();

/**
 * @var LP_Profile_Tab $profile_tab
 */
if ( ! isset( $user ) || ! isset( $tab_key ) || ! isset( $profile ) || ! isset( $profile_tab ) ) {
	return;
}

$current_tab = $profile->get_current_tab();
?>

<article id="profile-content" class="lp-profile-content custom-profile-content">
	<div id="profile-content-<?php echo esc_attr( $tab_key ); ?>" class="custom-tab-content">
		
		<?php do_action( 'learn-press/before-profile-content', $tab_key, $profile_tab, $user ); ?>

		<?php if ( $current_tab === 'dashboard' || empty( $current_tab ) ) : ?>
			
			<!-- Custom Dashboard Content -->
			<div class="custom-dashboard-layout">
				
				<!-- Progress Overview Section -->
				<div class="dashboard-section progress-overview">
					<h3 class="dashboard-section-title">
						<i class="lp-icon-chart-bar"></i>
						<?php _e( 'Learning Progress', 'learnpress' ); ?>
					</h3>
					<div class="progress-overview-content">
						<?php do_action( 'learn-press/custom-profile-progress', $profile, $user ); ?>
					</div>
				</div>

				<!-- Certificates Section -->
				<div class="dashboard-section certificates-overview">
					<h3 class="dashboard-section-title">
						<i class="lp-icon-certificate"></i>
						<?php _e( 'My Certificates', 'learnpress' ); ?>
					</h3>
					<div class="certificates-overview-content">
						<?php do_action( 'learn-press/custom-profile-certificates', $profile, $user ); ?>
					</div>
				</div>

				<!-- Recent Courses Section -->
				<div class="dashboard-section courses-overview">
					<h3 class="dashboard-section-title">
						<i class="lp-icon-book"></i>
						<?php _e( 'Recent Courses', 'learnpress' ); ?>
					</h3>
					<div class="courses-overview-content">
						<?php do_action( 'learn-press/custom-profile-courses', $profile, $user ); ?>
					</div>
				</div>

				<!-- User Statistics Section -->
				<div class="dashboard-section user-stats-overview">
					<h3 class="dashboard-section-title">
						<i class="lp-icon-user"></i>
						<?php _e( 'Profile Information', 'learnpress' ); ?>
					</h3>
					<div class="user-stats-overview-content">
						<?php do_action( 'learn-press/custom-profile-user-info', $profile, $user ); ?>
					</div>
				</div>

			</div>

		<?php else : ?>
			
			<!-- Original Tab Content -->
			<div class="original-tab-content">
				<?php
				if ( empty( $profile_tab->get( 'sections' ) ) ) {
					if ( $profile_tab->get( 'callback' ) && is_callable( $profile_tab->get( 'callback' ) ) ) {
						echo call_user_func_array(
							$profile_tab->get( 'callback' ),
							[
								$tab_key,
								$profile_tab,
								$user,
							]
						);
					} else {
						do_action( 'learn-press/profile-content', $tab_key, $profile_tab, $user );
					}
				} else {
					foreach ( $profile_tab->get( 'sections' ) as $key => $section ) {
						if ( $profile->get_current_section( '', false, false ) === $section['slug'] ) {
							if ( isset( $section['callback'] ) && is_callable( $section['callback'] ) ) {
								echo call_user_func_array( $section['callback'], array( $key, $section, $user ) );
							} else {
								do_action( 'learn-press/profile-section-content', $key, $section, $user );
							}
						}
					}
				}
				?>
			</div>

		<?php endif; ?>

		<?php do_action( 'learn-press/after-profile-content' ); ?>
		
	</div>
</article>

<style>
/* Custom Profile Content Styles */
.custom-profile-content {
	background: transparent;
}

.custom-tab-content {
	min-height: 400px;
}

.custom-dashboard-layout {
	display: grid;
	grid-template-columns: 1fr;
	gap: 30px;
}

.dashboard-section {
	background: white;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	padding: 25px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dashboard-section-title {
	display: flex;
	align-items: center;
	gap: 10px;
	margin: 0 0 20px 0;
	font-size: 20px;
	font-weight: 600;
	color: var(--primary-color, #003087);
	border-bottom: 2px solid var(--primary-color, #003087);
	padding-bottom: 10px;
}

.dashboard-section-title i {
	font-size: 22px;
	color: var(--secondary-color, #007BFF);
}

/* Progress Overview Styles */
.progress-overview-content .progress-bars-container {
	display: grid;
	gap: 15px;
}

.progress-overview-content .progress-item {
	background: #f8f9fa;
	border: 1px solid #e9ecef;
	border-radius: 8px;
	padding: 20px;
	transition: box-shadow 0.3s ease;
}

.progress-overview-content .progress-item:hover {
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.progress-overview-content .progress-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12px;
}

.progress-overview-content .course-title {
	font-weight: 600;
	color: var(--text-color, #000000);
	font-size: 16px;
}

.progress-overview-content .progress-percentage {
	font-weight: 700;
	font-size: 18px;
	color: var(--primary-color, #003087);
}

.progress-overview-content .progress-bar {
	height: 10px;
	background-color: #e9ecef;
	border-radius: 5px;
	overflow: hidden;
	margin-bottom: 8px;
}

.progress-overview-content .progress-fill {
	height: 100%;
	border-radius: 5px;
	transition: width 0.5s ease;
}

.progress-overview-content .progress-date {
	text-align: right;
	font-size: 12px;
	color: var(--light-text-color, #6C757D);
}

/* Certificates Overview Styles */
.certificates-overview-content .certificates-container {
	display: grid;
	gap: 20px;
}

.certificates-overview-content .certificate-item {
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border: 2px solid var(--primary-color, #003087);
	border-radius: 12px;
	padding: 25px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	transition: transform 0.3s ease;
}

.certificates-overview-content .certificate-item:hover {
	transform: translateY(-2px);
	box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.certificates-overview-content .certificate-title {
	font-size: 18px;
	font-weight: 700;
	color: var(--primary-color, #003087);
	margin-bottom: 8px;
}

.certificates-overview-content .certificate-name {
	font-size: 16px;
	font-weight: 600;
	color: var(--text-color, #000000);
	margin-bottom: 5px;
}

.certificates-overview-content .certificate-date,
.certificates-overview-content .certificate-id {
	font-size: 14px;
	color: var(--light-text-color, #6C757D);
	margin-bottom: 3px;
}

.certificates-overview-content .download-certificate-btn {
	background: linear-gradient(135deg, var(--button-color, #003087) 0%, var(--secondary-color, #007BFF) 100%);
	color: white;
	padding: 12px 24px;
	border: none;
	border-radius: 6px;
	font-weight: 600;
	cursor: pointer;
	text-decoration: none;
	display: inline-flex;
	align-items: center;
	gap: 8px;
	transition: all 0.3s ease;
}

.certificates-overview-content .download-certificate-btn:hover {
	transform: translateY(-1px);
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Courses Overview Styles */
.courses-overview-content .courses-container {
	display: grid;
	gap: 20px;
}

.courses-overview-content .course-item {
	background: white;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	padding: 20px;
	display: flex;
	gap: 20px;
	transition: box-shadow 0.3s ease;
}

.courses-overview-content .course-item:hover {
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.courses-overview-content .course-thumbnail {
	flex-shrink: 0;
	width: 120px;
	height: 80px;
	border-radius: 6px;
	overflow: hidden;
}

.courses-overview-content .course-thumbnail img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.courses-overview-content .course-info {
	flex: 1;
}

.courses-overview-content .course-title {
	font-size: 18px;
	font-weight: 600;
	color: var(--primary-color, #003087);
	margin-bottom: 8px;
}

.courses-overview-content .course-instructor {
	font-size: 14px;
	color: var(--light-text-color, #6C757D);
	margin-bottom: 10px;
}

.courses-overview-content .course-meta {
	display: flex;
	gap: 20px;
	font-size: 14px;
}

.courses-overview-content .course-rating {
	color: #ffc107;
	font-weight: 600;
}

.courses-overview-content .course-price {
	color: var(--secondary-color, #007BFF);
	font-weight: 600;
}

.courses-overview-content .course-duration {
	color: var(--light-text-color, #6C757D);
}

/* User Stats Overview Styles */
.user-stats-overview-content .user-info-container {
	background: #f8f9fa;
	border-radius: 8px;
	padding: 20px;
}

.user-stats-overview-content .user-info-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	gap: 20px;
}

.user-stats-overview-content .info-item {
	background: white;
	padding: 15px;
	border-radius: 6px;
	border-left: 4px solid var(--primary-color, #003087);
}

.user-stats-overview-content .info-item label {
	display: block;
	font-weight: 600;
	color: var(--primary-color, #003087);
	margin-bottom: 5px;
	font-size: 14px;
}

.user-stats-overview-content .info-item span {
	font-size: 16px;
	color: var(--text-color, #000000);
}

/* Original Tab Content Styles */
.original-tab-content {
	background: white;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	padding: 25px;
	min-height: 400px;
}

/* Responsive Design */
@media (max-width: 768px) {
	.dashboard-section {
		padding: 20px;
	}
	
	.dashboard-section-title {
		font-size: 18px;
	}
	
	.certificates-overview-content .certificate-item {
		flex-direction: column;
		gap: 15px;
		text-align: center;
	}
	
	.courses-overview-content .course-item {
		flex-direction: column;
		gap: 15px;
	}
	
	.courses-overview-content .course-thumbnail {
		width: 100%;
		height: 150px;
	}
	
	.courses-overview-content .course-meta {
		flex-direction: column;
		gap: 8px;
	}
	
	.user-stats-overview-content .user-info-grid {
		grid-template-columns: 1fr;
	}
}

/* RTL Support */
[dir="rtl"] .dashboard-section-title {
	flex-direction: row-reverse;
}

[dir="rtl"] .progress-overview-content .progress-info {
	flex-direction: row-reverse;
}

[dir="rtl"] .certificates-overview-content .certificate-item {
	flex-direction: row-reverse;
}

[dir="rtl"] .courses-overview-content .course-item {
	flex-direction: row-reverse;
}
</style>
