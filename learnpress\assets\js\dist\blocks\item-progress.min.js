(()=>{"use strict";const e=window.React,t=(window.wp.i18n,window.wp.components,window.wp.blockEditor),r=r=>{const s=(0,t.useBlockProps)();return(0,e.createElement)("div",{...s},(0,e.createElement)("div",{class:"items-progress"},(0,e.createElement)("span",{class:"number"},(0,e.createElement)("span",{class:"items-completed"},"43")," of 86 items"),(0,e.createElement)("div",{class:"learn-press-progress"},(0,e.createElement)("div",{class:"learn-press-progress__active","data-value":"50%",style:{left:"-50%"}}))))},s=e=>null,n=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/item-progress","title":"Item Progress","category":"learnpress-category","icon":"chart-line","description":"Renders template Single Course Legacy PHP templates.","textdomain":"learnpress","keywords":["item progress course","learnpress"],"usesContext":[],"supports":{"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),a=window.wp.blocks,l=window.wp.data;let o=null;var i,p,c;i=["learnpress/learnpress//single-lp_course_item"],p=n,c=e=>{(0,a.registerBlockType)(e.name,{...e,edit:r,save:s})},(0,l.subscribe)(()=>{const e={...p},t=(0,l.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&o!==r&&(o=r,(0,a.getBlockType)(e.name)&&((0,a.unregisterBlockType)(e.name),i.includes(r)?(e.ancestor=null,c(e)):(e.ancestor||(e.ancestor=[]),c(e))))}),(0,a.registerBlockType)(n.name,{...n,edit:r,save:s})})();