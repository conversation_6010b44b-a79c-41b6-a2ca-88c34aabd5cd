export default function export_invoice(){let e,t;document.addEventListener("click",t=>{const o=t.target;if("lp-invoice__export"===o.id)e.save();else if("lp-invoice__update"===o.id){const e=document.querySelector(".export-options__content").querySelectorAll("input"),t=[];e.forEach(e=>{e.checked||t.push(e.name)}),window.localStorage.setItem("lp_invoice_un_fields",JSON.stringify(t)),window.localStorage.setItem("lp_invoice_show",1),window.location.reload()}});document.addEventListener("DOMContentLoaded",()=>{if(!document.querySelector("#order-export__section").length){document.querySelectorAll(".tabs");const n=document.querySelectorAll(".tab"),l=document.querySelectorAll(".panel");function o(e){for(let e=0;e<n.length;e++)n[e].classList.remove("active");for(let e=0;e<l.length;e++)l[e].classList.remove("active");e.target.classList.add("active");const t=e.target.getAttribute("data-target");document.getElementById("panels").getElementsByClassName(t)[0].classList.add("active")}for(let s=0;s<n.length;s++)n[s].addEventListener("click",o,!1);t=document.getElementById("myModal");const c=document.getElementById("order-export__button"),i=document.getElementsByClassName("close")[0];c.onclick=function(){t.style.display="block"},i.onclick=function(){t.style.display="none",window.localStorage.setItem("lp_invoice_show",0)},window.onclick=function(e){e.target===t&&(t.style.display="none",window.localStorage.setItem("lp_invoice_show",0))},(()=>{const e=window.localStorage.getItem("lp_invoice_un_fields"),o=document.querySelector(".export-options__content");document.querySelectorAll(".invoice-field").forEach(t=>{const n=t.classList[1];if(e&&e.includes(n)){t.remove();const e=o.querySelector(`[name=${n}]`);e&&(e.checked=!1)}}),1===parseInt(window.localStorage.getItem("lp_invoice_show"))&&(t.style.display="block")})(),(()=>{const t={margin:[0,0,0,5],filename:document.title,image:{type:"webp"},html2canvas:{scale:2.5},jsPDF:{format:"a4",orientation:"p"}},o=document.querySelector("#lp-invoice__content");e=html2pdf().set(t).from(o)})()}})}