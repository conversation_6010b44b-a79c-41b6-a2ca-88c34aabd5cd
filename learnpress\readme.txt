=== LearnPress - WordPress LMS Plugin ===
Contributors: thimpress, tungnx89, nhamdv, ng<PERSON><PERSON><PERSON><PERSON><PERSON>, tunnhn, phonglq.foobla, thong<PERSON>, kendy73, leehld
Donate link:
Tags: elearning, education, course, lms, learning management system
Tested up to: 6.8
Stable tag: *******.3
License: GPLv3
License URI: https://www.gnu.org/licenses/gpl-3.0.html

A WordPress LMS Plugin to create WordPress Learning Management System. Turn your WordPress to LMS WordPress Website with Courses, Lessons, Quizzes & more.

== Description ==

LearnPress is a comprehensive *WordPress LMS Plugin* for WordPress. This is one of the best WordPress LMS Plugins which can be used to easily create & sell courses online. You can create a course curriculum with lessons & quizzes included which is managed with an easy-to-use interface for users. With our newest features, you can also sell your in-person classes and workshops using online payment gateways. Having this WordPress LMS Plugin, now you have a chance to quickly and easily create education, online school, online-course, offline-course websites with no coding knowledge required.

<a href="https://learnpresslms.com/?utm_source=WPORG&utm_medium=LP&utm_campaign=Content" target="_blank">Official Website - Explore more about LearnPress</a>
<a href="https://edupress.thimpress.com" target="_blank">Live Demo</a> | <a href="https://docs.thimpress.com/learnpress/?utm_source=WPORG&utm_medium=LP&utm_campaign=Content" target="_blank">Documentation</a> | <a href="https://help.thimpress.com" target="_blank">Support</a>
<a href="https://thimpress.com/product/education-wordpress-starter-theme-for-learnpress/?utm_source=WPORG&utm_medium=LP&utm_campaign=Content">Free LearnPress Theme</a>
<a href="https://1.envato.market/G5Ook" target="_blank">Premium LearnPress Themes</a>
<a href="https://thimpress.com/eduma-mobile-app/?utm_source=WPORG&utm_medium=LP&utm_campaign=Content" target="_blank">LearnPress Mobile App</a>
<a href="https://thimpress.com/product/learnpress-pro/?utm_source=WPORG&utm_medium=LP&utm_campaign=Content" target="_blank">LearnPress PRO Bundle</a>
<a href="https://www.figma.com/community/file/1444252888298116868/education-website-design-system-learnpress" target="_blank">Figma LearnPress UI Kit - Free Download</a>

LearnPress is free and always will be, but it is still a premium high-quality WordPress Plugin that definitely helps you with making money from your **WordPress Based LMS**. Also, LearnPress is highly recommended by WPML as the <a href="https://wpml.org/plugin-functionality/lms/" target="_blank">Learning Management System Plugin for Multilingual Sites</a>. Just try and see how amazing it is. LearnPress WordPress Online Course plugin is lightweight and super powerful with lots of Add-ons to empower its core system.

Highly recommended by LearnPress users, we are pleased to introduce you to our best Education WordPress Themes which have Premium LearnPress Add-Ons included already.

- [Eduma | Education WordPress Theme](https://1.envato.market/G5Ook) (#1 Selling Education WordPress Theme).
- [Coaching | Life & Fitness Coaching WordPress Theme](https://1.envato.market/Xq2Ra) (Rising Star).
- [Course Builder | Online Course WordPress Theme](https://1.envato.market/13Zkd) (Potential).

###Online LMS & Education WordPress Themes###

Take a look at **[Premium Online LMS & Education WordPress Themes](https://1.envato.market/AoKx3D)** that are developed to work perfectly with LearnPress LMS WordPress Plugin. These incredible WordPress Themes are the best for your Online LMS & Education websites using LearnPress with the clean and modern design. From more than 50 demos, you can easily choose the most suitable one which fits your purpose and customize it as your style.

- [Create a WordPress Udemy with Eduma](https://eduma.thimpress.com/demo-udemy/?utm_source=WPORG&utm_medium=LP&utm_campaign=Content)
- [Create a WordPress Coursera with Eduma](https://eduma.thimpress.com/demo-coursera/?utm_source=WPORG&utm_medium=LP&utm_campaign=Content)

###LearnPress Mobile App###

**[LearnPress Mobile App](https://thimpress.com/eduma-mobile-app/?utm_source=WPORG&utm_medium=LP&utm_campaign=LPMA)**  is an LMS Mobile App for iOS & Android built with React Native Technology. If you have Online Education & LMS websites using WordPress Themes or LearnPress Plugin, LearnPress Mobile App supports you to convert your current websites to LMS Mobile App easily. Your LMS Mobile App will be on both App Store and Google Play Store. All courses will be synced immediately between your Online Education & LMS Website and your LMS Mobile App. This LMS Mobile App brings to your students a new learning experience when they can manage and track their courses as well as instructors.

###LearnPress Tutorials###

- [LearnPress Tutorials](https://docs.thimpress.com/learnpress/)
- How to create an Udemy or Coursera clone easily with LearnPress [Click here >>](https://thimpress.com/create-udemy-coursera-clone-easily-learnpress/?utm_source=WPORG&utm_medium=LP&utm_campaign=Content)
- Step-by-step YouTube guide on setting up your own Online Course site. [Click here >>](https://www.youtube.com/watch?v=0mJBC9IpiS0)
[youtube https://www.youtube.com/watch?v=0mJBC9IpiS0]

Are you looking for *the best LMS WordPress Plugin*?

###LearnPress features###

**LearnPress - WordPress LMS Plugin works with your Theme**
We create LearnPress LMS Plugin to work with any WordPress Themes.

**LearnPress supports WordPress Multisite**
Create WordPress based LMS as a multi-site.

**Create course**
LearnPress LMS Plugin provides an excellent user interface for online courses creating with any options you need. You can find it easy to make a full curriculum layout as well as edit and maintain it.
You can also export and import your courses to another website using LearnPress.

**Manage course**
With the course you've created, you can share it, manage it, watch statistic about the number of students, trends, etc.

**Sell course**
LearnPress is free, but it still allows you to sell your courses with many billing methods supported such as PayPal, WooCommerce, Stripe, etc.

**Offline course features**
LearnPress comes with extensive offline course features. If offline courses are active, some online course features like Curriculum, End Button, Retake Course, Content Blocking, and Repurchase will be disabled by default.
Instead, LearnPress adds offline course-specific features, including a custom course details page with information like Delivery Type, Location, Schedule, Start Date, and more.
These features are perfect for educators, training centers, and institutions offering blended learning or complete offline programmes, providing proper management.
With LearnPress offline course features, you also have an easily customizable in built page for offline courses that comes with a responsive and search engine friendly modern UI/UX, that is designed specifically to attract and retain local learners.

**Communicate with your students**
BuddyPress makes it easier for you to communicate with your students or instructors via the WordPress forum. Studying, making friends and having fun.

**LearnPress provides a bunch of add-ons**
Add-ons are used to provide extra features for LearnPress and you can also write your own add-on for more purposes.

**LearnPress is free and always will be**
Education should be free, and we want you to bring it to as many people as you can. Therefore, we create LearnPress as a tool for you to create online course and share it. We'll continue to develop it as long as we can and make it better and better.

**LearnPress is actively developed**
We are developing and improving LearnPress day by day and bringing you more new features cos we want LearnPress to become the **best WordPress LMS plugin**.

<a href="https://docs.thimpress.com/learnpress/" target="_blank">LearnPress WordPress LMS Plugin Documentation</a>

###Free Add-ons for LearnPress WordPress LMS Plugin###

- [LearnPress Wishlist](https://wordpress.org/plugins/learnpress-wishlist/) - Add courses to a wishlist for students.
- [LearnPress Course Review](https://wordpress.org/plugins/learnpress-course-review/) - Review courses for enrolled students.
- [LearnPress Import/Export](https://wordpress.org/plugins/learnpress-import-export/) - Export or import courses out-of-box.
- [LearnPress Prerequisites Courses](https://wordpress.org/plugins/learnpress-prerequisites-courses/) - Require students need to pass the prerequisite course items to access the next ones.
- [LearnPress bbPress Integration](https://wordpress.org/plugins/learnpress-bbpress/) -  Interact with students and instructors in a course and discuss with each other about the course.
- [LearnPress BuddyPress Integration](https://wordpress.org/plugins/learnpress-buddypress/) - Add BuddyPress support for LearnPress WordPress LMS Plugin, turn your WordPress BuddyPress website into *BuddyPress LMS* (*BuddyPress Learning Management System*)".
- [LearnPress Offline Payment](https://wordpress.org/plugins/learnpress-offline-payment/) - Help you to manually create order for offline payment instead of paying via any payment gateways to sell course.
- [LearnPress Fill In Blank](https://wordpress.org/plugins/learnpress-fill-in-blank/) - Fill in Blank question type for LearnPress WordPress LMS Plugin.

###Premium add-ons for LearnPress WordPress LMS Plugin###

- [WPML Add-on for LearnPress](https://thimpress.com/product/wpml-add-on-for-learnpress/) - With the help from WPML, WPML add-on for LearnPress is the perfect solution for LearnPress users to translate anything flashily. Now, LearnPress users can focus on creating amazing contents without the worries about language restrictions.
- [Live Course Add-on for LearnPress](https://thimpress.com/product/live-course-add-on-for-learnpress/) - Create courses and live video meetings using Zoom or Google Meet Use shortcode to show the meeting content.
- [Assignments add-on for LearnPress](https://thimpress.com/product/assignments-add-on-for-learnpress/) - Allow instructors to give assignments, homework for students and they can grade the students' work later on. The Assignments add-on is a great supplement to LearnPress and will make your courses more interactive.
- [myCRED Add-on for LearnPress](https://thimpress.com/product/mycred-add-on-for-learnpress/) - add myCRED support for LearnPress WordPress LMS plugin, you can add point system to your eLearning WordPress site using LearnPress + myCRED add-on.
- [Certificates Add-on for LearnPress](https://thimpress.com/product/certificates-add-on-for-learnpress/) -  add drag & drop certificates builder as well as select designed certificate for each LMS course, your students will get particular certificates when they finished their courses.
- [Co-instructors Add-on for LearnPress](https://thimpress.com/product/co-instructors-add-on-for-learnpress/) - multiple instructors support for each LMS course.
- [Collections Add-on for LearnPress](https://thimpress.com/product/collections-add-on-for-learnpress/) – create LMS courses collection, this is helpful if you want to combine multiple LMS courses into a collection for a group of skills.
- [Stripe Payment Method for LearnPress](https://thimpress.com/product/stripe-add-on-for-learnpress/) - Stripe payment method for LearnPress WordPress LMS Plugin.
- [2Checkout Add-on for LearnPress](https://thimpress.com/product/2checkout-add-learnpress/) - Make LearnPress plugin ready to use 2Checkout payment gateway to pay for courses.
- [Authorize.Net Add-ons for LearnPress](https://thimpress.com/product/authorize-net-add-ons-learnpress/) - Authorize.Net payment method for LearnPress WordPress LMS Plugin.
- [WooCommerce Add-on for LearnPress](https://thimpress.com/product/woocommerce-add-on-for-learnpress/) - Use WooCommerce as payment gateway for LearnPress WordPress LMS Plugin.
- [Content Drip Add-on for LearnPress](https://thimpress.com/product/content-drip-add-on-for-learnpress/) - Restrict the access of students to each lesson by progress, or time.
- [Sorting Choices Question Type for LearnPress](https://thimpress.com/product/sorting-choice-add-on-for-learnpress/) - Another interactive question type for LearnPress.
- [Commission Add-on for LearnPress](https://thimpress.com/product/commission-add-on-for-learnpress/) - Provide a Commission Management system for LearnPress.
- [Gradebook Add-on for LearnPress](https://thimpress.com/product/gradebook-add-on-for-learnpress/) - View your class result, export these result to CSV and post to frontend.
- [Random Quiz Add-on for LearnPress](https://thimpress.com/product/random-quiz-add-on-for-learnpress/) - Allow you to create a random questions quiz. Each student will get different quiz within a same course.
- [Paid Membership Pro Add-on for LearnPress](https://thimpress.com/product/paid-membership-pro-add-learnpress/) - Integrate Paid Membership Pro into LearnPress, bring membership feature to your powerful WordPress based LMS system.
- [Announcement Add-on for LearnPress](https://thimpress.com/product/announcement-add-on-for-learnpress/) - Announcement is a great way to promote your courses and update new features + contents of your courses, email notification included.
- [Frontend Editor Add-on for LearnPress](https://thimpress.com/product/frontend-editor-add-on-for-learnpress/) - Frontend Editor add-on for LearnPress enable your courses to be edited from the frontend. Your instructors don't need to go to the WP Dashboard to create and edit courses anymore with Frontend Editor add-on.
- [Instamojo Add-on for LearnPress](https://thimpress.com/product/instamojo-add-on-for-learnpress/) - Instamojo add-on for LearnPress gives you another Payment Gateway which is powered by Instamojo for your online courses in India.

###LearnPress has been translated into the following languages###

1. English
2. French
3. Indonesian
4. Italian
5. German
6. Polish
7. Russian
8. Dutch (Netherlands)
9. Spanish (Costa Rica)
10. Spanish
11. Spanish (Mexico)
12. Spanish (Venezuela)
13. Indonesian
14. Chinese

##WordPress LMS Plugin - LearnPress ROADMAP##

- Payment Method support (first priority)
	- Google Checkout
	- Amazon Payments
	- Dwolla
	- Braintree
	- Samurai by FeeFighters
	- WePay
- Events
- Share Grade
- BadgeOS
- Create quiz from random questions in question bank (DONE)
- Attach restriction to lesson
- Presentation support (maybe support SlideShare)
- Student ranks
- REST API for mobile app (DONE)
- No distraction mode (when doing quiz)
- Report/feedback about a question/quiz/lesson
- Commission for payment method (DONE)
- Instructor's note
- Private message from Admin to teacher
- Group payment

Any suggestions for this WordPress LMS Plugin? Send us via email: <EMAIL>

== Installation ==

**From your WordPress dashboard**
1. Visit 'Plugin > Add new'.
2. Search for 'LearnPress'.
3. Activate LearnPress from your Plugins page.

**From WordPress.org**
1. Search, select and download LearnPress.
2. Activate the plugin through the 'Plugins' menu in WordPress Dashboard.

== Frequently Asked Questions ==

= What is LearnPress? =
LearnPress is a plugin for LMS website to spread out courses and sell courses online.

= Where can I find LearnPress documentation and user guides? =
If you want to use LearnPress to build a Learning Management System website, please refer to our user guides in <a href="https://docs.thimpress.com/learnpress/" target="_blank">LearnPress official site.</a>
And if you want to extend or use LearnPress, see our <a href="https://github.com/LearnPress/LearnPress/wiki" target="_blank">Wiki.</a>

= Where can I get support or talk to other users? =
If you get troubles when using LearnPress you can ask for help on the <a href="https://wordpress.org/support/plugin/learnpress/">LearnPress Support Forum</a> or <a href="https://www.facebook.com/groups/learnpress/" target="_blank">join the private Facebook group</a>. You could share your feedback about LearnPress. And let us know which feature you want us to build next.

For help with premium add-ons, use our <a href="https://thimpress.com/forums/forum/learnpress-premium-add-ons/">helpdesk</a>.

= Where can I ask for new features, suggest new ideas or new themes for LearnPress? =
You can send us your thoughts through via email: <EMAIL>

= Where can I report bugs or contribute to the project? =
You can also report bugs on LearnPress Support Forum or LearnPress <a href="https://github.com/LearnPress/LearnPress/" target="_blank">Github Repository</a>.

= Where can I find the REST API documentation? =
You can find the REST API documentation <a href="https://docs.thimpress.com/learnpress/developer-guide/">here</a>.

= LearnPress is great, can I contribute to it? =
Yes, you can and we appreciate it. Join in our <a href="https://github.com/LearnPress/LearnPress/" target="_blank">Github Repository</a>.

**LearnPress Translation project**
https://www.transifex.com/projects/p/learnpress/

== Screenshots ==

1. Course Layout: A visually engaging way to explore your courses. Easily scan multiple courses by filters and sorts, with vibrant images, brief descriptions and CTA button
2. Online Course Detail: Clean & thoughtfully structured layout features a dynamic course overview, detailed sections, curriculum, instructor information and more to help make decision quicker
3. In-person Course Detail: Promote and sell your in-person courses with linear presentation, customizable CTA buttons and smooth check-out process
4. Payment Methods: By free LearnPress plugin, you can sell your course at no cost using our default payment methods and easily enhance your options by adding premium payment gateways as needed
5. Learning Experience: Dive into engaging lessons, interactive media rich & streamlined text-focused content and keep track on your progress from start to finish
6. Quiz: Challenge your learners with dynamic format quizzes having instant feedback to reinforce learning while keeping learners motivated
7. Assignment: Put your knowledge to the test with hands-on assignment
8. Certificate: Provide professional and customizable certificate to showcase your student's achievement .jpg
9. Dashboard: Stay organized with a personalized overview of your progress, quiz & assignment achievement, and completed courses
10. Upsell: Offer different pricing strategies to help you monetize your course more effectively
11. Mobile Responsive: Immersive learning experience anywhere, anytime on multiple devices
12. Create Course: Simple, efficient and quick way to build your course and share your knowledge to the world
13. Add-ons: Unlock endless possibilities with our add-ons from additional payment gateways to more managing course options. Tailor to your needs and easily scale as your site grow

== Changelog ==

= *******.3 (2025-06-30) =
~ Tweak: tab material.
~ Edit curriculum: fix error remove section not unassign item from that section.

= *******.1 (2025-06-25) =
~ Fixed: get courses assign to co-instructor on the single instructor page.
~ Fixed: unset value of some key auto increment when insert data to tables lp_section_items, lp_sections.
~ Fixed: error sort by on list courses has a mask.
~ Fixed: permission for instructor when edit curriculum's course.
~ Update: show 'featured' label on Single Course, List Courses page.
~ Tweak: image course block Gutenberg.

= ******* (2025-06-17) =
~ Fixed: error 404 with link profile tab, link lesson with Polylang v3.7 and higher.
~ Fixed: error enroll many courses can make server hang by send mail on background.
~ Fixed: translate on format_human_time_diff method.
~ Update: UI/UX for edit curriculum course.

= 4.2.8.6.1 (2025-06-09) =
~ Fixed: error widget courses can't interact.

= 4.2.8.6 (2025-06-09) =
~ Refactor: edit curriculum.
~ Update: UI/UX for edit curriculum.
~ Allow: drag/drop item to another section.
~ Fixed error Deprecated crypt(), when update password on profile.
~ Fixed: get_instructor_info method.

= 4.2.8.5 (2025-05-21) =
~ Fixed: missing user email in order summary table.
~ Fixed: get related courses, set distinct, random list.
~ Fixed: error avatar author for App API.
~ Fixed: minor bug Block Gutenberg.

= 4.2.8.4 (2025-05-08) =
~ Fixed: security.
~ Fixed: error Add Media not working on the edit lesson screen.
~ Move setting quiz to tab course.
~ Tweak: list course block Gutenberg.
~ Tweak: condition check blocks load on template.
~ Tweak: styles.

= 4.2.8.3 (2025-04-26) =
~ Provide: blocks for Gutenberg, build page Single Course, Archive Course, List Course.
~ Compatible: with WP 6.8.

= 4.2.8.2 (2025-04-03) =
~ Feature: add filter course by type online/offline.
~ Tweak: add pagination list quizzes on Profile Page, instead of only default display 5 quizzes.
~ Tweak: avatar Profile function, new UI/UX.
~ Tweak page checkout: set link to profile page of User when logged, instead of user link default of WordPress.
~ Tweak: set priority of hook "pre_get_document_title" to 10, for case plugin SEO can override.
~ Tweak: layout review order.
~ Fixed: error get courses status draft on widget list courses for Elementor.

= 4.2.8.1 (2025-03-12) =
~ Hot fix: error Curriculum with old data.
~ Update: currencies list.

= 4.2.8 (2025-03-06) =
~ Apply the new curriculum layout for a single course, screen item learning, and apply it to the premium themes.
~ Optimize: some duplicate query.
~ Fixed: course protected on single course layout Modern.
~ Fixed: continue button has character "?" when redirect to item.
~ Fixed: error get option "evaluate final quiz".
~ Tweak: logic search item content for the course on screen item learning.
~ Tweak: sort the newest item learning first on the screen tab "My courses" - Profile.
~ Tweak: crop image course by "Thumbnail dimensions".

= ******* (2025-02-24) =
~ Fixed: error 404 single course with struct permalink is "%course_category%".
~ Show error message response from PayPal if exists.
~ Tweak: UserModel class, get_display_name method, with case translate.
~ Added: hook "learn-press/user-lesson/completed" when completed lesson.
~ Added: option Store IP Guest to handle checkout with case can't read $_COOKIE from server.
~ LP_Gateways: tweak init method, allow load gateway instanceof LP_Gateway_Abstract.
~ Update some text missing text domain.
~ Tweak: isRestApiLP method REST prefix.

= ******* (2025-02-17) =
~ Fixed: send email when user finished course.
~ Fixed: error PayPal payment can't verify transaction.
~ Fixed: error change author when edit course.
~ Tweak: Guest buy/enroll course, login via checkout.
~ Tweak: course section, question CURD.

= ******* (2025-02-08) =
~ Fixed: some hosting error 403 when load list courses, complete lesson request.
~ Fixed: sitemap 404 with items course.

= ******* (2025-02-05) =
~ Fixed: security.
~ Fixed: error 404-Page Order received after checkout.
~ Fixed: error 404-Page Profile sub-page.
~ Optimize: speed load list courses.
~ Added: option choose "Layout single course (Modern/Classic)".
~ Added: option "Curriculum display" (currently apply for layout Modern).
~ Added: new Modern layout for Single Course.
~ Added: exclude from search via ?s= for lesson, quiz, order.
~ Refactor code handle Quiz.
~ Tweak: layout Single Instructor (change struct layout, display cover image, link edit if exists).
~ Tweak: courses filter for mobile.
~ Tweak: layout course offline for mobile.
~ Allow: display html content on Offline Payment.
~ Allow: change multiple instructors via Bulk Edit.

= *******.1 (2025-01-22) =
~ Fixed: security.
~ Fixed: minor bugs.

<a href="https://raw.githubusercontent.com/LearnPress/learnpress/develop/changelog.txt" target="_blank">See changelog for all versions.</a>
