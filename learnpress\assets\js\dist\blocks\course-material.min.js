(()=>{"use strict";const e=window.React,t=window.wp.i18n,l=window.wp.blockEditor,a=a=>{const{attributes:r,setAttributes:s,context:n}=a,i=(0,l.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...i},(0,e.createElement)("div",{class:"course-material"},(0,e.createElement)("h3",{class:"course-material__title"},(0,t.__)("Course Material","learnpress")),(0,e.createElement)("div",{class:"lp-list-material"},(0,e.createElement)("div",{class:"lp-material-skeleton"},(0,e.createElement)("table",{class:"course-material-table"},(0,e.createElement)("thead",null,(0,e.createElement)("tr",null,(0,e.createElement)("th",{class:"lp-material-th-file-name"},(0,t.__)("Name","learnpress")),(0,e.createElement)("th",{class:"lp-material-th-file-type"},(0,t.__)("Type","learnpress")),(0,e.createElement)("th",{class:"lp-material-th-file-size"},(0,t.__)("Size","learnpress")),(0,e.createElement)("th",{class:"lp-material-th-file-link"},(0,t.__)("Download","learnpress")))),(0,e.createElement)("tbody",{id:"material-file-list"},(0,e.createElement)("tr",{class:"lp-material-item"},(0,e.createElement)("td",{class:"lp-material-file-name"},"Course Materials"),(0,e.createElement)("td",{class:"lp-material-file-type"},"txt"),(0,e.createElement)("td",{class:"lp-material-file-size"},"1KB"),(0,e.createElement)("td",{class:"lp-material-file-link"},(0,e.createElement)("a",null,(0,e.createElement)("i",{class:"lp-icon-file-download btn-download-material"})))))),(0,e.createElement)("button",{class:"lp-button lp-loadmore-material"},(0,t.__)("Load more","learnpress")))))))},r=e=>null,s=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-material","title":"Course Material","category":"learnpress-course-elements","icon":"download","description":"Renders template Material Course PHP templates.","textdomain":"learnpress","keywords":["material single course","learnpress"],"ancestor":["learnpress/single-course"],"usesContext":["lpCourseData"],"supports":{"multiple":true,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":false}},"color":{"background":false,"text":true,"link":false,"heading":true,"gradients":false,"__experimentalDefaultControls":{"text":true,"h3":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),n=window.wp.blocks,i=window.wp.data;let o=null;var c,m,p;c=["learnpress/learnpress//single-lp_course"],m=s,p=e=>{(0,n.registerBlockType)(e.name,{...e,edit:a,save:r})},(0,i.subscribe)(()=>{const e={...m},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const l=t.getCurrentPostId();null!==l&&o!==l&&(o=l,(0,n.getBlockType)(e.name)&&((0,n.unregisterBlockType)(e.name),c.includes(l)?(e.ancestor=null,p(e)):(e.ancestor||(e.ancestor=[]),p(e))))}),(0,n.registerBlockType)(s.name,{...s,edit:a,save:r})})();