(()=>{"use strict";const t={status:function(t){return t.status},pagination:function(t){return t.pagination},items:function(t,e){return t.items.map(function(t){var n=e.addedItems.find(function(e){return t.id===e.id});return t.added=!!n,t})},addedItems:function(t){return t.addedItems},isOpen:function(t){return t.open},types:function(t){return t.types},section:function(t){return t.sectionId}},e={TOGGLE:function(t){t.open=!t.open},SET_SECTION:function(t,e){t.sectionId=e},SET_LIST_ITEMS:function(t,e){t.items=e},ADD_ITEM:function(t,e){t.addedItems.push(e)},REMOVE_ADDED_ITEM:function(t,e){t.addedItems.forEach(function(n,i){n.id===e.id&&t.addedItems.splice(i,1)})},RESET:function(t){t.addedItems=[],t.items=[]},UPDATE_PAGINATION:function(t,e){t.pagination=e},SEARCH_ITEMS_REQUEST:function(t){t.status="loading"},SEARCH_ITEMS_SUCCESS:function(t){t.status="successful"},SEARCH_ITEMS_FAILURE:function(t){t.status="failed"}},n={toggle:function(t){t.commit("TOGGLE")},open:function(t,e){t.commit("SET_SECTION",e),t.commit("RESET"),t.commit("TOGGLE")},searchItems:function(t,e){t.commit("SEARCH_ITEMS_REQUEST"),LP.Request({type:"search-items",query:e.query,item_type:e.type,page:e.page,exclude:JSON.stringify([])}).then(function(e){var n=e.body;if(n.success){var i=n.data;t.commit("SET_LIST_ITEMS",i.items),t.commit("UPDATE_PAGINATION",i.pagination),t.commit("SEARCH_ITEMS_SUCCESS")}},function(e){t.commit("SEARCH_ITEMS_FAILURE"),console.error(e)})},addItem:function(t,e){t.commit("ADD_ITEM",e)},removeItem:function(t,e){t.commit("REMOVE_ADDED_ITEM",e)},addItemsToSection:function(t){var e=t.getters.addedItems;e.length>0&&LP.Request({type:"add-items-to-section",section_id:t.getters.section,items:JSON.stringify(e)}).then(function(e){var n=e.body;if(n.success){t.commit("TOGGLE");var i=n.data;t.commit("ss/UPDATE_SECTION_ITEMS",{section_id:t.getters.section,items:i},{root:!0})}},function(t){console.error(t)})}},i=window.jQuery||jQuery;function o(o){var s=i.extend({},o.chooseItems);return s.sectionId=!1,s.pagination="",s.status="",{namespaced:!0,state:s,getters:t,mutations:e,actions:n}}const s=window.jQuery||jQuery,c={toggleAllSections(t){t.getters.isHiddenAllSections?t.commit("OPEN_ALL_SECTIONS"):t.commit("CLOSE_ALL_SECTIONS"),LP.Request({type:"hidden-sections",hidden:t.getters.hiddenSections})},updateSectionsOrder(t,e){LP.Request({type:"sort-sections",order:JSON.stringify(e)}).then(function(e){const n=e.body.data;t.commit("SORT_SECTION",n)},function(t){console.error(t)})},toggleSection(t,e){e.open?t.commit("CLOSE_SECTION",e):t.commit("OPEN_SECTION",e),LP.Request({type:"hidden-sections",hidden:t.getters.hiddenSections})},updateSection(t,e){t.commit("UPDATE_SECTION_REQUEST",e.id),LP.Request({type:"update-section",section:JSON.stringify(e)}).then(function(){t.commit("UPDATE_SECTION_SUCCESS",e.id)}).catch(function(){t.commit("UPDATE_SECTION_FAILURE",e.id)})},removeSection(t,e){t.commit("REMOVE_SECTION",e.index),LP.Request({type:"remove-section",section_id:e.section.id}).then(function(t){t.body},function(t){console.error(t)})},newSection(t,e){const n={type:"new-section",section_name:e,temp_id:LP.uniqueId()};t.commit("ADD_NEW_SECTION",{id:n.temp_id,items:[],open:!1,title:n.section_name}),LP.Request(n).then(function(e){const n=e.body;if(n.success){const e=s.extend({},n.data,{open:!0});t.commit("ADD_NEW_SECTION",e)}},function(t){console.error(t)})},updateSectionItem(t,e){t.commit("UPDATE_SECTION_ITEM_REQUEST",e.item.id),LP.Request({type:"update-section-item",section_id:e.section_id,item:JSON.stringify(e.item)}).then(function(n){t.commit("UPDATE_SECTION_ITEM_SUCCESS",e.item.id);const i=n.body;if(i.success){const n=i.data;t.commit("UPDATE_SECTION_ITEM",{section_id:e.section_id,item:n})}},function(n){t.commit("UPDATE_SECTION_ITEM_FAILURE",e.item.id),console.error(n)})},removeSectionItem(t,e){const n=e.item.id;t.commit("REMOVE_SECTION_ITEM",e),e.item.temp_id=0,LP.Request({type:"remove-section-item",section_id:e.section_id,item_id:n}).then(function(i){const{data:o,success:s}=i.body;s||(alert(o),e.oldId=n),t.commit("REMOVE_SECTION_ITEM",e),t.commit("REMOVE_SECTION_ITEM",e)})},deleteSectionItem(t,e){const n=e.item.id;t.commit("REMOVE_SECTION_ITEM",e),e.item.temp_id=0,LP.Request({type:"delete-section-item",section_id:e.section_id,item_id:n}).then(function(i){const{data:o,success:s}=i.body;s||(alert(o),e.oldId=n),t.commit("REMOVE_SECTION_ITEM",e)})},newSectionItem(t,e){t.commit("APPEND_EMPTY_ITEM_TO_SECTION",e),LP.Request({type:"new-section-item",section_id:e.section_id,item:JSON.stringify(e.item)}).then(function(n){const i=n.body;if(i.success){const n={};s.each(i.data,function(t,e){n[e.old_id?e.old_id:e.id]=e}),t.commit("UPDATE_ITEM_SECTION_BY_ID",{section_id:e.section_id,items:n})}},function(t){console.error(t)})},updateSectionItems({state:t},e){LP.Request({type:"update-section-items",section_id:e.section_id,items:JSON.stringify(e.items),last_section:t.sections[t.sections.length-1]===e.section_id}).then(function(t){t.body.success},function(t){console.error(t)})}},u={SORT_SECTION(t,e){t.sections=t.sections.map(function(t){return t.order=e[t.id],t})},SET_SECTIONS(t,e){t.sections=e},ADD_NEW_SECTION(t,e){let n;void 0===e.open&&(e.open=!0),e.temp_id&&t.sections.map(function(t,i){if(e.temp_id==t.id)return n=i,!1}),void 0!==n?$Vue.set(t.sections,n,e):t.sections.push(e)},ADD_EMPTY_SECTION(t,e){e.open=!0,t.sections.push(e)},REMOVE_SECTION(t,e){t.sections.splice(e,1)},REMOVE_SECTION_ITEM(t,e){let n=t.sections.find(function(t){return t.id===e.section_id}).items||[],i=e.item,o=-1;if(n.forEach(function(t,e){t.id===i.id&&(o=e)}),-1!==o){if(void 0!==e.oldId)return void(n[o].id=e.oldId);i.temp_id?n[o].id=i.temp_id:n.splice(o,1)}},UPDATE_SECTION_ITEMS(t,e){const n=t.sections.find(function(t){return parseInt(t.id)===parseInt(e.section_id)});n&&(n.items=e.items)},UPDATE_SECTION_ITEM(t,e){},CLOSE_SECTION(t,e){t.sections.forEach(function(n,i){e.id===n.id&&(t.sections[i].open=!1)})},OPEN_SECTION(t,e){t.sections.forEach(function(n,i){e.id===n.id&&(t.sections[i].open=!0)})},OPEN_ALL_SECTIONS(t){t.sections=t.sections.map(function(t){return t.open=!0,t})},CLOSE_ALL_SECTIONS(t){t.sections=t.sections.map(function(t){return t.open=!1,t})},UPDATE_SECTION_REQUEST(t,e){$Vue.set(t.statusUpdateSection,e,"updating")},UPDATE_SECTION_SUCCESS(t,e){$Vue.set(t.statusUpdateSection,e,"successful")},UPDATE_SECTION_FAILURE(t,e){$Vue.set(t.statusUpdateSection,e,"failed")},UPDATE_SECTION_ITEM_REQUEST(t,e){$Vue.set(t.statusUpdateSectionItem,e,"updating")},UPDATE_SECTION_ITEM_SUCCESS(t,e){$Vue.set(t.statusUpdateSectionItem,e,"successful")},UPDATE_SECTION_ITEM_FAILURE(t,e){$Vue.set(t.statusUpdateSectionItem,e,"failed")},APPEND_EMPTY_ITEM_TO_SECTION(t,e){const n=t.sections.find(function(t){return parseInt(t.id)===parseInt(e.section_id)});n&&n.items.push({id:e.item.id,title:e.item.title,type:"empty-item"})},UPDATE_ITEM_SECTION_BY_ID(t,e){const n=t.sections.find(function(t){return parseInt(t.id)===parseInt(e.section_id)});if(n)for(let t=0;t<n.items.length;t++)try{if(!n.items[t])continue;const i=n.items[t].id;i&&e.items[i]&&$Vue.set(n.items,t,e.items[i])}catch(t){console.log(t)}}},d={sections:function(t){return t.sections||[]},urlEdit:function(t){return t.urlEdit},hiddenSections:function(t){return t.sections.filter(function(t){return!t.open}).map(function(t){return parseInt(t.id)})},isHiddenAllSections:function(t,e){var n=e.sections;return e.hiddenSections.length===n.length},statusUpdateSection:function(t){return t.statusUpdateSection},statusUpdateSectionItem:function(t){return t.statusUpdateSectionItem}},r=window.jQuery;function E(t){var e=r.extend({},t.sections);return e.statusUpdateSection={},e.statusUpdateSectionItem={},e.sections=e.sections.map(function(t){var n=e.hidden_sections.find(function(e){return parseInt(t.id)===parseInt(e)});return t.open=!n,t}),{namespaced:!0,state:e,getters:d,mutations:u,actions:c}}const a=window.jQuery||jQuery,m={heartbeat:function(t){return t.heartbeat},action:function(t){return t.action},id:function(t){return t.course_id},autoDraft:function(t){return t.auto_draft},disable_curriculum:function(t){return t.disable_curriculum},status:function(t){return t.status||"error"},currentRequest:function(t){return t.countCurrentRequest||0},urlAjax:function(t){return t.ajax},nonce:function(t){return t.nonce}},_={UPDATE_HEART_BEAT:function(t,e){t.heartbeat=!!e},UPDATE_AUTO_DRAFT_STATUS:function(t,e){t.auto_draft=e},UPDATE_STATUS:function(t,e){t.status=e},INCREASE_NUMBER_REQUEST:function(t){t.countCurrentRequest++},DECREASE_NUMBER_REQUEST:function(t){t.countCurrentRequest--}},S={heartbeat:function(t){LP.Request({type:"heartbeat"}).then(function(e){var n=e.body;t.commit("UPDATE_HEART_BEAT",!!n.success)},function(e){t.commit("UPDATE_HEART_BEAT",!1)})},draftCourse:function(t,e){t.getters.autoDraft&&LP.Request({type:"draft-course",course:JSON.stringify(e)}).then(function(e){e.body.success&&t.commit("UPDATE_AUTO_DRAFT_STATUS",!1)})},newRequest:function(t){t.commit("INCREASE_NUMBER_REQUEST"),t.commit("UPDATE_STATUS","loading"),window.onbeforeunload=function(){return""}},requestCompleted:function(t,e){t.commit("DECREASE_NUMBER_REQUEST"),0===t.getters.currentRequest&&(t.commit("UPDATE_STATUS",e),window.onbeforeunload=null)}},T=window.jQuery;window.$Vue=window.$Vue||Vue,window.$Vuex=window.$Vuex||Vuex,(0,window.jQuery)(document).ready(function(){var t,e,n;window.LP_Curriculum_Store=new $Vuex.Store((t=lpAdminCourseEditorSettings,(n=T.extend({},t.root)).status="success",n.heartbeat=!0,n.countCurrentRequest=0,{state:n,getters:m,mutations:_,actions:S,modules:{ci:o(t),i18n:(e=t.i18n,{namespaced:!0,state:a.extend({},e),getters:{all:function(t){return t}}}),ss:E(t)}})),function(t){const e=window.jQuery||jQuery,n=Vue.http;t=e.extend({ns:"LPRequest",store:!1},t||{});let i=null;LP.Request=function(o){return i=e("#publishing-action"),o.id=t.store.getters.id,o.nonce=t.store.getters.nonce,o["lp-ajax"]=t.store.getters.action,i.find("#publish").addClass("disabled"),i.find(".spinner").addClass("is-active"),i.addClass("code-"+o.code),n.post(t.store.getters.urlAjax,o,{emulateJSON:!0,params:{namespace:t.ns,code:o.code}})},n.interceptors.push(function(e,n){e.params.namespace===t.ns?(t.store.dispatch("newRequest"),n(function(n){jQuery.isPlainObject(n.body)||(n.body=LP.parseJSON(n.body)),n.body.success?t.store.dispatch("requestCompleted","successful"):t.store.dispatch("requestCompleted","failed"),i.removeClass("code-"+e.params.code),i.attr("class")||(i.find("#publish").removeClass("disabled"),i.find(".spinner").removeClass("is-active"))})):n()})}({ns:"LPCurriculumRequest",store:LP_Curriculum_Store}),setTimeout(()=>{window.LP_Course_Editor=new $Vue({el:"#admin-editor-lp_course",template:"<lp-course-editor></lp-course-editor>"})},100)})})();