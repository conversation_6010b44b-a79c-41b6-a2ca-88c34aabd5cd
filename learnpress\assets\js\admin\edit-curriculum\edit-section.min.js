import*as lpEditCurriculumShare from"./share.js";import <PERSON><PERSON>lert from"sweetalert2";import Sortable from"sortablejs";const className={...lpEditCurriculumShare.className,elDivAddNewSection:".add-new-section",elSectionClone:".section.clone",elSectionTitleNewInput:".lp-section-title-new-input",elSectionTitleInput:".lp-section-title-input",etBtnEditTitle:".lp-btn-edit-section-title",elSectionDesInput:".lp-section-description-input",elBtnAddSection:".lp-btn-add-section",elBtnUpdateTitle:".lp-btn-update-section-title",elBtnUpdateDes:".lp-btn-update-section-description",elBtnCancelUpdateTitle:".lp-btn-cancel-update-section-title",elBtnCancelUpdateDes:".lp-btn-cancel-update-section-description",elBtnDeleteSection:".lp-btn-delete-section",elSectionDesc:".section-description",elSectionToggle:".section-toggle",elCountSections:".count-sections"};let{courseId:courseId,elEditCurriculum:elEditCurriculum,elCurriculumSections:elCurriculumSections,showToast:showToast,lpUtils:lpUtils,updateCountItems:updateCountItems}=lpEditCurriculumShare;const idUrlHandle="edit-course-curriculum",init=()=>{({courseId:courseId,elEditCurriculum:elEditCurriculum,elCurriculumSections:elCurriculumSections,showToast:showToast,lpUtils:lpUtils,updateCountItems:updateCountItems}=lpEditCurriculumShare)},changeTitleBeforeAdd=(e,t)=>{const s=t.closest(`${className.elSectionTitleNewInput}`);if(!s)return;const l=s.closest(`${className.elDivAddNewSection}`);if(!l)return;const c=l.querySelector(`${className.elBtnAddSection}`);0===s.value.trim().length?(c.classList.remove("active"),delete lpEditCurriculumShare.hasChange.titleNew):(c.classList.add("active"),lpEditCurriculumShare.hasChange.titleNew=1)},focusTitleNewInput=(e,t,s=!0)=>{const l=t.closest(`${className.elSectionTitleNewInput}`);if(!l)return;const c=l.closest(`${className.elDivAddNewSection}`);c&&(s?c.classList.add("focus"):c.classList.remove("focus"))},addSection=(e,t)=>{let s=!1;if((t.closest(`${className.elBtnAddSection}`)||t.closest(`${className.elSectionTitleNewInput}`)&&"Enter"===e.key)&&(s=!0),!s)return;const l=t.closest(`${className.elDivAddNewSection}`);if(!l)return;e.preventDefault();const c=l.querySelector(`${className.elSectionTitleNewInput}`),o=c.value.trim(),i=c.dataset.messEmptyTitle;if(0===o.length)return void showToast(i,"error");c.value="",c.blur();const n=elCurriculumSections.querySelector(`${className.elSectionClone}`).cloneNode(!0);n.classList.remove("clone"),lpUtils.lpShowHideEl(n,1),lpUtils.lpSetLoadingEl(n,1);n.querySelector(`${className.elSectionTitleInput}`).value=o,elCurriculumSections.insertAdjacentElement("beforeend",n);const a={success:e=>{const{message:t,status:s,data:l}=e;if("error"===s)n.remove();else if("success"===s){const{section:e}=l;n.dataset.sectionId=e.section_id||"",lpEditCurriculumShare.sortAbleItem&&lpEditCurriculumShare.sortAbleItem()}showToast(t,s)},error:e=>{n.remove(),showToast(e,"error")},completed:()=>{lpUtils.lpSetLoadingEl(n,0),n.classList.remove(`${className.elCollapse}`);n.querySelector(`${className.elSectionDesInput}`).focus(),updateCountSections(),delete lpEditCurriculumShare.hasChange.titleNew}},r={action:"add_section",course_id:courseId,section_name:o,args:{id_url:idUrlHandle}};window.lpAJAXG.fetchAJAX(r,a)},deleteSection=(e,t)=>{const s=t.closest(`${className.elBtnDeleteSection}`);s&&SweetAlert.fire({title:s.dataset.title,text:s.dataset.content,icon:"warning",showCloseButton:!0,showCancelButton:!0,cancelButtonText:lpDataAdmin.i18n.cancel,confirmButtonText:lpDataAdmin.i18n.yes,reverseButtons:!0}).then(e=>{if(e.isConfirmed){const e=s.closest(".section"),t=e.dataset.sectionId;lpUtils.lpSetLoadingEl(e,1);const l={success:e=>{const{message:t,status:s}=e,{content:l}=e.data;showToast(t,s)},error:e=>{showToast(e,"error")},completed:()=>{lpUtils.lpSetLoadingEl(e,0),e.remove(),updateCountItems(e),updateCountSections()}},c={action:"delete_section",course_id:courseId,section_id:t,args:{id_url:idUrlHandle}};window.lpAJAXG.fetchAJAX(c,l)}})},focusTitleInput=(e,t,s=!0)=>{const l=t.closest(`${className.elSectionTitleInput}`);if(!l)return;const c=l.closest(`${className.elSection}`);c&&(s?c.classList.add("focus"):c.classList.remove("focus"))},setFocusTitleInput=(e,t)=>{const s=t.closest(`${className.etBtnEditTitle}`);if(!s)return;const l=s.closest(`${className.elSection}`);if(!l)return;const c=l.querySelector(`${className.elSectionTitleInput}`);c.setSelectionRange(c.value.length,c.value.length),c.focus()},changeTitle=(e,t)=>{const s=t.closest(`${className.elSectionTitleInput}`);if(!s)return;const l=s.closest(`${className.elSection}`);s.value.trim()===(s.dataset.old||"")?(l.classList.remove("editing"),delete lpEditCurriculumShare.hasChange.title):(l.classList.add("editing"),lpEditCurriculumShare.hasChange.title=1)},updateSectionTitle=(e,t)=>{let s=!1;if((t.closest(`${className.elBtnUpdateTitle}`)||t.closest(`${className.elSectionTitleInput}`)&&"Enter"===e.key)&&(s=!0),!s)return;e.preventDefault();const l=t.closest(`${className.elSection}`);if(!l)return;const c=l.querySelector(`${className.elSectionTitleInput}`);if(!c)return;const o=l.dataset.sectionId,i=c.value.trim(),n=c.dataset.old||"",a=c.dataset.messEmptyTitle;if(0===i.length)return void showToast(a,"error");if(i===n)return;c.blur(),lpUtils.lpSetLoadingEl(l,1);const r={success:e=>{const{message:t,status:s}=e;showToast(t,s),"success"===s&&(c.dataset.old=i)},error:e=>{showToast(e,"error")},completed:()=>{lpUtils.lpSetLoadingEl(l,0),l.classList.remove("editing"),delete lpEditCurriculumShare.hasChange.title}},u={action:"update_section",course_id:courseId,section_id:o,section_name:i,args:{id_url:idUrlHandle}};window.lpAJAXG.fetchAJAX(u,r)},cancelSectionTitle=(e,t)=>{const s=t.closest(`${className.elBtnCancelUpdateTitle}`);if(!s)return;const l=s.closest(`${className.elSection}`),c=l.querySelector(`${className.elSectionTitleInput}`);c.value=c.dataset.old||"",l.classList.remove("editing"),delete lpEditCurriculumShare.hasChange.title},updateSectionDescription=(e,t)=>{let s=!1;if((t.closest(`${className.elBtnUpdateDes}`)||t.closest(`${className.elSectionDesInput}`)&&"Enter"===e.key)&&(s=!0),!s)return;e.preventDefault();const l=t.closest(`${className.elSectionDesc}`);if(!l)return;const c=l.querySelector(`${className.elSectionDesInput}`);if(!c)return;const o=c.closest(`${className.elSection}`),i=o.dataset.sectionId,n=c.value.trim(),a=c.dataset.old||"";if(n===a)return;lpUtils.lpSetLoadingEl(o,1);const r={success:e=>{const{message:t,status:s}=e;showToast(t,s)},error:e=>{showToast(e,"error")},completed:()=>{lpUtils.lpSetLoadingEl(o,0);c.closest(`${className.elSectionDesc}`).classList.remove("editing"),c.dataset.old=n}},u={action:"update_section",course_id:courseId,section_id:i,section_description:n,args:{id_url:idUrlHandle}};window.lpAJAXG.fetchAJAX(u,r)},cancelSectionDescription=(e,t)=>{const s=t.closest(`${className.elBtnCancelUpdateDes}`);if(!s)return;const l=s.closest(`${className.elSectionDesc}`),c=l.querySelector(`${className.elSectionDesInput}`);c.value=c.dataset.old||"",l.classList.remove("editing")},changeDescription=(e,t)=>{const s=t.closest(`${className.elSectionDesInput}`);if(!s)return;const l=s.closest(`${className.elSectionDesc}`);s.value.trim()===(s.dataset.old||"")?l.classList.remove("editing"):l.classList.add("editing")},toggleSection=(e,t)=>{const s=t.closest(`${className.elSectionToggle}`);if(!s)return;const l=s.closest(`${className.elSection}`);l.closest(`${className.elCurriculumSections}`)&&(l.classList.toggle(`${className.elCollapse}`),checkAllSectionsCollapsed())},checkAllSectionsCollapsed=()=>{const e=elEditCurriculum.querySelectorAll(`${className.elSection}:not(.clone)`),t=elEditCurriculum.querySelector(`${className.elToggleAllSections}`);let s=!0;e.forEach(e=>{if(e.classList.contains(`${className.elCollapse}`))return s=!1,!1}),s?t.classList.remove(`${className.elCollapse}`):t.classList.add(`${className.elCollapse}`)},sortAbleSection=()=>{let e,t=0;new Sortable(elCurriculumSections,{handle:".drag",animation:150,onEnd:s=>{const l=s.item;if(!t)return;const c=l.closest(`${className.elSection}`),o=elCurriculumSections.querySelectorAll(`${className.elSection}`),i=[];o.forEach((e,t)=>{const s=e.dataset.sectionId;i.push(s)});const n={success:e=>{const{message:t,status:s}=e;showToast(t,s)},error:e=>{showToast(e,"error")},completed:()=>{lpUtils.lpSetLoadingEl(c,0),t=0}},a={action:"update_section_position",course_id:courseId,new_position:i,args:{id_url:idUrlHandle}};clearTimeout(e),e=setTimeout(()=>{lpUtils.lpSetLoadingEl(c,1),window.lpAJAXG.fetchAJAX(a,n)},1e3)},onMove:t=>{clearTimeout(e)},onUpdate:e=>{t=1}})},updateCountSections=()=>{const e=elEditCurriculum.querySelector(`${className.elCountSections}`),t=elCurriculumSections.querySelectorAll(`${className.elSection}:not(.clone)`).length;e.dataset.count=t,e.querySelector(".count").textContent=t};export{init,changeTitleBeforeAdd,focusTitleNewInput,addSection,deleteSection,changeTitle,focusTitleInput,setFocusTitleInput,updateSectionTitle,cancelSectionTitle,updateSectionDescription,cancelSectionDescription,changeDescription,toggleSection,sortAbleSection};