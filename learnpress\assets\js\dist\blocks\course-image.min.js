(()=>{"use strict";const e=window.React,t=window.wp.i18n,s=window.wp.blockEditor,r=window.wp.components,n=n=>{const{attributes:l,setAttributes:a,context:o}=n,i=(0,s.useBlockProps)(),{lpCourseData:c}=o,u=c?.image||'<div className="course-img"><img src="https://placehold.co/500x300?text=Course+Image"/></div>';return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(s.InspectorControls,null,(0,e.createElement)(r.<PERSON>,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(r.<PERSON><PERSON>,{label:(0,t.__)("Make the image a link","learnpress"),checked:!!n.attributes.isLink,onChange:e=>{n.setAttributes({isLink:!!e})}}),n.attributes.isLink?(0,e.createElement)(r.<PERSON>,{label:(0,t.__)("Open is new tab","learnpress"),checked:!!n.attributes.target,onChange:e=>{n.setAttributes({target:!!e})}}):"")),(0,e.createElement)("a",null,(0,e.createElement)("div",{...i,dangerouslySetInnerHTML:{__html:u}})))},l=e=>null,a=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-image","title":"Course Image","category":"learnpress-course-elements","icon":"format-image","description":"Renders template Image Course PHP templates.","textdomain":"learnpress","keywords":["image single course","learnpress"],"ancestor":["learnpress/single-course","learnpress/course-item-template"],"usesContext":["lpCourseData"],"attributes":{"isLink":{"type":"boolean","default":true},"target":{"type":"boolean","default":false}},"supports":{"multiple":true,"html":false,"shadow":true,"__experimentalBorder":{"color":true,"radius":true,"width":true,"__experimentalDefaultControls":{"width":false,"color":false,"radius":false}}}}'),o=window.wp.blocks,i=window.wp.data;let c=null;var u,p,m;u=["learnpress/learnpress//single-lp_course","learnpress/learnpress//single-lp_course-offline"],p=a,m=e=>{(0,o.registerBlockType)(e.name,{...e,edit:n,save:l})},(0,i.subscribe)(()=>{const e={...p},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const s=t.getCurrentPostId();null!==s&&c!==s&&(c=s,(0,o.getBlockType)(e.name)&&((0,o.unregisterBlockType)(e.name),u.includes(s)?(e.ancestor=null,m(e)):(e.ancestor||(e.ancestor=[]),m(e))))}),(0,o.registerBlockType)(a.name,{...a,edit:n,save:l})})();