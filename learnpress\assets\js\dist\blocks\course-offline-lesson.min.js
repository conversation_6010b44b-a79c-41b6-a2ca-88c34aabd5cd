(()=>{"use strict";const e=window.React,t=window.wp.i18n,n=window.wp.blockEditor,s=window.wp.components,l=(window.wp.element,l=>{const o=(0,n.useBlockProps)(),{attributes:r,setAttributes:a,context:i}=l,{lpCourseData:c}=i;return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(n.InspectorControls,null,(0,e.createElement)(s.PanelBody,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(s.<PERSON><PERSON>ont<PERSON>,{label:(0,t.__)("Show Label","learnpress"),checked:r.showLabel,onChange:e=>{a({showLabel:e})}}),(0,e.createElement)(s.<PERSON>ggle<PERSON>ontrol,{label:(0,t.__)("Show Icon","learnpress"),checked:r.showIcon,onChange:e=>{a({showIcon:e})}}))),(0,e.createElement)("div",{...o},(0,e.createElement)("div",{className:"info-meta-item"},(0,e.createElement)("span",{className:"info-meta-left"},r.showIcon&&(0,e.createElement)("span",{dangerouslySetInnerHTML:{__html:'<i class="lp-icon-file-o"></i>'}}),r.showLabel?(0,t.__)("Lesson:","learnpress"):""),(0,e.createElement)("span",{className:"info-meta-right",dangerouslySetInnerHTML:{__html:'<div class="course-count-lesson"><div class="course-count-item lp_lesson">5</div></div>'}}))))}),o=e=>null,r=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-offline-lesson","title":"Course Offline Lesson","category":"learnpress-course-elements","description":"Show number lesson of course offline.","textdomain":"learnpress","keywords":["lesson","offline","count","learnpress"],"icon":"media-text","ancestor":["learnpress/single-course"],"usesContext":["lpCourseData"],"attributes":{"showIcon":{"type":"boolean","default":true},"showLabel":{"type":"boolean","default":true}},"supports":{"multiple":true,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),a=window.wp.blocks,i=window.wp.data;let c=null;var u,p,m;u=["learnpress/learnpress//single-lp_course-offline"],p=r,m=e=>{(0,a.registerBlockType)(e.name,{...e,edit:l,save:o})},(0,i.subscribe)(()=>{const e={...p},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const n=t.getCurrentPostId();null!==n&&c!==n&&(c=n,(0,a.getBlockType)(e.name)&&((0,a.unregisterBlockType)(e.name),u.includes(n)?(e.ancestor=null,m(e)):(e.ancestor||(e.ancestor=[]),m(e))))}),(0,a.registerBlockType)(r.name,{...r,edit:l,save:o})})();