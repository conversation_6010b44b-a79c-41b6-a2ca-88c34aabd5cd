(()=>{"use strict";const e=window.React,r=(window.wp.i18n,window.wp.blockEditor),l=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-order-by","title":"Course Sorting","category":"learnpress-course-elements","icon":"editor-alignleft","description":"Show sorting select box for courses.","textdomain":"learnpress","keywords":["sort","learnpress"],"usesContext":["lpCourseQuery"],"ancestor":["learnpress/list-courses"],"supports":{"inserter":true,"reusable":true,"reorder":true,"html":false,"multiple":true}}');(0,window.wp.blocks.registerBlockType)(l.name,{...l,edit:l=>{var t;const{attributes:s,setAttributes:o,context:a}=l,i=(0,r.useBlockProps)(),n=a.lpCourseQuery?.order_by||"post_date",u=null!==(t=[{label:"Newly published",value:"post_date"},{label:"Title a-z",value:"post_title"},{label:"Title z-a",value:"post_title_desc"},{label:"Price high to low",value:"price"},{label:"Price low to high",value:"price_low"},{label:"Popular",value:"popular"},{label:"Average Ratings",value:"rating"}].find(e=>e.value===n)?.label)&&void 0!==t?t:"Newly published";return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...i},(0,e.createElement)("div",{className:"courses-order-by-wrapper"},(0,e.createElement)("select",{name:"order_by",className:"block-courses-order-by"},(0,e.createElement)("option",{selected:"selected"},u)))))},save:e=>null})})();