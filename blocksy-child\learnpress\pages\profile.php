<?php
/**
 * Custom Template for displaying main user profile page.
 * 
 * This template overrides the default LearnPress profile layout
 * to implement a custom design with progress bars, certificates, and user info.
 *
 * <AUTHOR> Theme
 * @package  Blocksy-Child/LearnPress
 * @version  1.0.0
 */

defined( 'ABSPATH' ) || exit();

if ( ! isset( $profile ) ) {
	return;
}

$user = $profile->get_user();
if ( ! $user ) {
	return;
}
?>

<div id="learn-press-profile" <?php $profile->main_class(); ?> class="custom-profile-layout">
	<div class="lp-content-area custom-profile-content">
		
		<!-- Custom Profile Header Section -->
		<div class="custom-profile-header">
			<div class="profile-header-container">
				<?php do_action( 'learn-press/custom-profile-header', $profile, $user ); ?>
			</div>
		</div>

		<!-- Main Profile Content Grid -->
		<div class="custom-profile-grid">
			
			<!-- Left Sidebar -->
			<div class="custom-profile-sidebar">
				<?php do_action( 'learn-press/custom-profile-sidebar', $profile, $user ); ?>
			</div>

			<!-- Main Content Area -->
			<div class="custom-profile-main">
				
				<!-- Progress Section -->
				<div class="custom-progress-section">
					<h3 class="section-title"><?php _e( 'Course Progress', 'learnpress' ); ?></h3>
					<?php do_action( 'learn-press/custom-profile-progress', $profile, $user ); ?>
				</div>

				<!-- Certificates Section -->
				<div class="custom-certificates-section">
					<h3 class="section-title"><?php _e( 'Certificates', 'learnpress' ); ?></h3>
					<?php do_action( 'learn-press/custom-profile-certificates', $profile, $user ); ?>
				</div>

				<!-- Courses Section -->
				<div class="custom-courses-section">
					<h3 class="section-title"><?php _e( 'My Courses', 'learnpress' ); ?></h3>
					<?php do_action( 'learn-press/custom-profile-courses', $profile, $user ); ?>
				</div>

				<!-- User Info Section -->
				<div class="custom-user-info-section">
					<h3 class="section-title"><?php _e( 'User Information', 'learnpress' ); ?></h3>
					<?php do_action( 'learn-press/custom-profile-user-info', $profile, $user ); ?>
				</div>

				<!-- Original Profile Content (for tabs functionality) -->
				<div class="custom-profile-tabs-wrapper">
					<?php do_action( 'learn-press/user-profile-tabs', $profile ); ?>
					<?php do_action( 'learn-press/user-profile', $profile ); ?>
				</div>

			</div>

		</div>

	</div>
</div>

<style>
/* Custom Profile Layout Styles */
.custom-profile-layout {
	--primary-color: #003087;
	--secondary-color: #007BFF;
	--background-color: #FFFFFF;
	--text-color: #000000;
	--light-text-color: #6C757D;
	--button-color: #003087;
	--button-hover-color: #002766;
	--progress-green: #28A745;
	--progress-red: #DC3545;
	--progress-blue: #007BFF;
}

.custom-profile-content {
	max-width: 1200px;
	margin: 0 auto;
	padding: 20px;
}

.custom-profile-header {
	background: var(--primary-color);
	color: white;
	padding: 20px;
	margin-bottom: 30px;
	border-radius: 8px;
}

.custom-profile-grid {
	display: grid;
	grid-template-columns: 300px 1fr;
	gap: 30px;
	margin-bottom: 30px;
}

.custom-profile-sidebar {
	background: var(--background-color);
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	padding: 20px;
	height: fit-content;
}

.custom-profile-main {
	display: flex;
	flex-direction: column;
	gap: 30px;
}

.custom-progress-section,
.custom-certificates-section,
.custom-courses-section,
.custom-user-info-section {
	background: var(--background-color);
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	padding: 20px;
}

.section-title {
	color: var(--primary-color);
	font-size: 18px;
	font-weight: 600;
	margin-bottom: 15px;
	border-bottom: 2px solid var(--primary-color);
	padding-bottom: 8px;
}

.custom-profile-tabs-wrapper {
	background: var(--background-color);
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	padding: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
	.custom-profile-grid {
		grid-template-columns: 1fr;
		gap: 20px;
	}
	
	.custom-profile-content {
		padding: 10px;
	}
}

/* RTL Support */
[dir="rtl"] .custom-profile-grid {
	direction: rtl;
}

[dir="rtl"] .section-title {
	text-align: right;
}
</style>

<?php
