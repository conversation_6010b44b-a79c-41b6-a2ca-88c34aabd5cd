<?php
if (! defined('WP_DEBUG')) {
	die( 'Direct access forbidden.' );
}
add_action( 'wp_enqueue_scripts', function () {
	wp_enqueue_style( 'parent-style', get_template_directory_uri() . '/style.css' );

	// Enqueue custom LearnPress profile styles
	wp_enqueue_style(
		'learnpress-profile-custom',
		get_stylesheet_directory_uri() . '/learnpress-profile-custom.css',
		array( 'parent-style' ),
		'1.0.0'
	);
});

/**
 * Custom LearnPress Profile Hooks
 */

// Add Arabic language support and RTL detection
add_action( 'init', 'custom_profile_language_support' );
function custom_profile_language_support() {
	// Load text domain for translations
	load_child_theme_textdomain( 'learnpress-custom', get_stylesheet_directory() . '/languages' );

	// Add RTL support detection
	if ( is_rtl() ) {
		add_action( 'wp_head', 'custom_profile_rtl_styles' );
	}
}

// Add RTL-specific styles
function custom_profile_rtl_styles() {
	?>
	<style>
	/* RTL-specific styles for Arabic support */
	[dir="rtl"] .custom-profile-layout {
		direction: rtl;
		text-align: right;
	}

	[dir="rtl"] .custom-profile-grid {
		grid-template-columns: 1fr 300px;
	}

	[dir="rtl"] .progress-info {
		flex-direction: row-reverse;
	}

	[dir="rtl"] .certificate-item {
		flex-direction: row-reverse;
	}

	[dir="rtl"] .course-item {
		grid-template-columns: 1fr 200px;
	}

	[dir="rtl"] .course-header {
		flex-direction: row-reverse;
	}

	[dir="rtl"] .course-instructor-info {
		flex-direction: row-reverse;
	}

	[dir="rtl"] .course-pricing {
		flex-direction: row-reverse;
	}

	[dir="rtl"] .action-buttons {
		text-align: left;
	}

	[dir="rtl"] .section-title::before {
		margin-left: 10px;
		margin-right: 0;
	}

	/* Arabic font support */
	[lang="ar"], [dir="rtl"] {
		font-family: 'Noto Sans Arabic', 'Amiri', 'Scheherazade', 'Lateef', 'Noto Naskh Arabic', sans-serif;
	}

	/* Improve Arabic text rendering */
	[dir="rtl"] .certificate-title,
	[dir="rtl"] .course-title,
	[dir="rtl"] .section-title {
		font-weight: 600;
		line-height: 1.4;
	}
	</style>
	<?php
}

// Helper function to get localized text
function custom_profile_get_text( $key, $default = '' ) {
	$texts = array(
		// Arabic translations as shown in design
		'progress' => is_rtl() ? 'التقدم' : __( 'Progress', 'learnpress' ),
		'certificate_achievement' => is_rtl() ? 'شهادة الإنجاز' : __( 'Certificate of Achievement', 'learnpress' ),
		'download_certificate' => is_rtl() ? 'تحميل الشهادة' : __( 'Download Certificate', 'learnpress' ),
		'name' => is_rtl() ? 'الاسم' : __( 'Name', 'learnpress' ),
		'email' => is_rtl() ? 'البريد الإلكتروني' : __( 'Email', 'learnpress' ),
		'followers' => is_rtl() ? 'المتابعون' : __( 'Followers', 'learnpress' ),
		'following' => is_rtl() ? 'المتابعة' : __( 'Following', 'learnpress' ),
		'completed' => is_rtl() ? 'مكتمل' : __( 'Completed', 'learnpress' ),
		'in_progress' => is_rtl() ? 'قيد التقدم' : __( 'In Progress', 'learnpress' ),
		'not_started' => is_rtl() ? 'لم يبدأ' : __( 'Not Started', 'learnpress' ),
		'courses' => is_rtl() ? 'الدورات' : __( 'Courses', 'learnpress' ),
		'instructor' => is_rtl() ? 'المدرب' : __( 'Instructor', 'learnpress' ),
		'rating' => is_rtl() ? 'التقييم' : __( 'Rating', 'learnpress' ),
		'students' => is_rtl() ? 'الطلاب' : __( 'Students', 'learnpress' ),
		'lessons' => is_rtl() ? 'الدروس' : __( 'Lessons', 'learnpress' ),
		'duration' => is_rtl() ? 'المدة' : __( 'Duration', 'learnpress' ),
		'start_course' => is_rtl() ? 'ابدأ الدورة' : __( 'Start Course', 'learnpress' ),
		'continue' => is_rtl() ? 'متابعة' : __( 'Continue', 'learnpress' ),
		'statistics' => is_rtl() ? 'الإحصائيات' : __( 'Statistics', 'learnpress' ),
		'social' => is_rtl() ? 'التواصل الاجتماعي' : __( 'Social', 'learnpress' ),
		'information' => is_rtl() ? 'المعلومات' : __( 'Information', 'learnpress' ),
		'member_since' => is_rtl() ? 'عضو منذ' : __( 'Member Since', 'learnpress' ),
		'quick_actions' => is_rtl() ? 'إجراءات سريعة' : __( 'Quick Actions', 'learnpress' ),
		'settings' => is_rtl() ? 'الإعدادات' : __( 'Settings', 'learnpress' ),
		'my_courses' => is_rtl() ? 'دوراتي' : __( 'My Courses', 'learnpress' ),
		'orders' => is_rtl() ? 'الطلبات' : __( 'Orders', 'learnpress' ),
		'learning_progress' => is_rtl() ? 'تقدم التعلم' : __( 'Learning Progress', 'learnpress' ),
		'my_certificates' => is_rtl() ? 'شهاداتي' : __( 'My Certificates', 'learnpress' ),
		'recent_courses' => is_rtl() ? 'الدورات الحديثة' : __( 'Recent Courses', 'learnpress' ),
		'profile_information' => is_rtl() ? 'معلومات الملف الشخصي' : __( 'Profile Information', 'learnpress' ),
		'preview' => is_rtl() ? 'معاينة' : __( 'Preview', 'learnpress' ),
		'share' => is_rtl() ? 'مشاركة' : __( 'Share', 'learnpress' ),
		'issued_on' => is_rtl() ? 'صدر في' : __( 'Issued on', 'learnpress' ),
		'verified_certificate' => is_rtl() ? 'شهادة موثقة' : __( 'Verified Certificate', 'learnpress' ),
		'total_courses' => is_rtl() ? 'إجمالي الدورات' : __( 'Total Courses', 'learnpress' ),
		'grade' => is_rtl() ? 'الدرجة' : __( 'Grade', 'learnpress' ),
		'date' => is_rtl() ? 'التاريخ' : __( 'Date', 'learnpress' ),
		'id' => is_rtl() ? 'الرقم التعريفي' : __( 'ID', 'learnpress' ),
		'beginner' => is_rtl() ? 'مبتدئ' : __( 'Beginner', 'learnpress' ),
		'intermediate' => is_rtl() ? 'متوسط' : __( 'Intermediate', 'learnpress' ),
		'advanced' => is_rtl() ? 'متقدم' : __( 'Advanced', 'learnpress' ),
	);

	return isset( $texts[ $key ] ) ? $texts[ $key ] : ( $default ?: $key );
}

// Custom Profile Header
add_action( 'learn-press/custom-profile-header', 'custom_profile_header_content', 10, 2 );
function custom_profile_header_content( $profile, $user ) {
	$user_name = $user->get_display_name();
	$user_email = $user->get_email();
	$avatar_url = get_avatar_url( $user->get_id(), array( 'size' => 80 ) );
	?>
	<div class="custom-header-content">
		<div class="user-avatar">
			<img src="<?php echo esc_url( $avatar_url ); ?>" alt="<?php echo esc_attr( $user_name ); ?>" />
		</div>
		<div class="user-basic-info">
			<h2 class="user-name"><?php echo esc_html( $user_name ); ?></h2>
			<p class="user-email"><?php echo esc_html( $user_email ); ?></p>
		</div>
	</div>
	<?php
}

// Custom Profile Sidebar
add_action( 'learn-press/custom-profile-sidebar', 'custom_profile_sidebar_content', 10, 2 );
function custom_profile_sidebar_content( $profile, $user ) {
	$user_name = $user->get_display_name();
	$user_email = $user->get_email();
	$followers = get_user_meta( $user->get_id(), '_lp_followers', true ) ?: 0;
	$following = get_user_meta( $user->get_id(), '_lp_following', true ) ?: 0;
	?>
	<div class="custom-sidebar-content">
		<div class="user-info-item">
			<label><?php _e( 'Name', 'learnpress' ); ?>:</label>
			<span><?php echo esc_html( $user_name ); ?></span>
		</div>
		<div class="user-info-item">
			<label><?php _e( 'Email', 'learnpress' ); ?>:</label>
			<span><?php echo esc_html( $user_email ); ?></span>
		</div>
		<div class="user-info-item">
			<label><?php _e( 'Followers', 'learnpress' ); ?>:</label>
			<span><?php echo esc_html( $followers ); ?></span>
		</div>
		<div class="user-info-item">
			<label><?php _e( 'Following', 'learnpress' ); ?>:</label>
			<span><?php echo esc_html( $following ); ?></span>
		</div>
	</div>
	<?php
}

// Custom Profile Progress
add_action( 'learn-press/custom-profile-progress', 'custom_profile_progress_content', 10, 2 );
function custom_profile_progress_content( $profile, $user ) {
	// Get user's enrolled courses and their progress
	$courses = learn_press_get_enrolled_courses( $user->get_id() );

	if ( empty( $courses ) ) {
		// Show demo progress bars for design purposes
		echo '<div class="progress-bars-container">';

		// Demo progress items based on design
		$demo_progress = [
			[
				'title' => 'Wireless Course (Mobile Package) EN TO END',
				'progress' => 96,
				'date' => '2025-07-25',
				'status' => 'completed'
			],
			[
				'title' => 'Advanced Web Development Course',
				'progress' => 66,
				'date' => '2025-07-25',
				'status' => 'in-progress'
			],
			[
				'title' => 'Digital Marketing Fundamentals',
				'progress' => 0,
				'date' => '2025-07-25',
				'status' => 'not-started'
			],
			[
				'title' => 'UI/UX Design Principles',
				'progress' => 96,
				'date' => '2025-07-25',
				'status' => 'completed'
			]
		];

		foreach ( $demo_progress as $item ) {
			custom_render_progress_item( $item['title'], $item['progress'], $item['date'], $item['status'] );
		}

		echo '</div>';
		return;
	}

	echo '<div class="progress-bars-container">';

	foreach ( $courses as $course_id ) {
		$course = learn_press_get_course( $course_id );
		if ( ! $course ) continue;

		$progress = learn_press_get_user_course_progress( $user->get_id(), $course_id );
		$progress_percent = $progress ? round( $progress, 0 ) : 0;

		// Determine status based on progress
		$status = 'not-started';
		if ( $progress_percent >= 100 ) {
			$status = 'completed';
		} elseif ( $progress_percent > 0 ) {
			$status = 'in-progress';
		}

		// Get course enrollment date
		$enrollment_date = get_user_meta( $user->get_id(), '_lp_course_' . $course_id . '_enrollment_date', true );
		$date = $enrollment_date ? date( 'Y-m-d', strtotime( $enrollment_date ) ) : date( 'Y-m-d' );

		custom_render_progress_item( $course->get_title(), $progress_percent, $date, $status );
	}

	echo '</div>';
}

// Helper function to render progress items
function custom_render_progress_item( $title, $progress_percent, $date, $status ) {
	// Determine progress color based on status and percentage
	$progress_color = '#007BFF'; // Default blue
	$status_class = 'status-' . $status;

	switch ( $status ) {
		case 'completed':
			$progress_color = '#28A745'; // Green
			break;
		case 'in-progress':
			if ( $progress_percent >= 70 ) {
				$progress_color = '#28A745'; // Green for high progress
			} elseif ( $progress_percent >= 30 ) {
				$progress_color = '#007BFF'; // Blue for medium progress
			} else {
				$progress_color = '#DC3545'; // Red for low progress
			}
			break;
		case 'not-started':
			$progress_color = '#007BFF'; // Blue for not started
			break;
	}

	// Status labels in Arabic (as shown in design)
	$status_labels = [
		'completed' => 'مكتمل',
		'in-progress' => 'قيد التقدم',
		'not-started' => 'لم يبدأ'
	];

	$status_label = isset( $status_labels[ $status ] ) ? $status_labels[ $status ] : __( 'Progress', 'learnpress' );
	?>
	<div class="progress-item <?php echo esc_attr( $status_class ); ?>" data-progress="<?php echo $progress_percent; ?>">
		<div class="progress-header">
			<div class="progress-info">
				<span class="course-title"><?php echo esc_html( $title ); ?></span>
				<span class="progress-status"><?php echo esc_html( $status_label ); ?></span>
			</div>
			<div class="progress-percentage-wrapper">
				<span class="progress-percentage" style="color: <?php echo $progress_color; ?>;"><?php echo $progress_percent; ?>%</span>
			</div>
		</div>

		<div class="progress-bar-wrapper">
			<div class="progress-bar">
				<div class="progress-fill"
					 style="width: <?php echo $progress_percent; ?>%; background-color: <?php echo $progress_color; ?>;"
					 data-percentage="<?php echo $progress_percent; ?>">
				</div>
			</div>
			<div class="progress-bar-labels">
				<span class="progress-start">0%</span>
				<span class="progress-end">100%</span>
			</div>
		</div>

		<div class="progress-footer">
			<div class="progress-date">
				<i class="lp-icon-calendar"></i>
				<span><?php echo esc_html( $date ); ?></span>
			</div>
			<div class="progress-actions">
				<?php if ( $status === 'completed' ) : ?>
					<span class="completion-badge">
						<i class="lp-icon-check"></i>
						<?php _e( 'Completed', 'learnpress' ); ?>
					</span>
				<?php elseif ( $status === 'in-progress' ) : ?>
					<span class="progress-badge">
						<i class="lp-icon-clock"></i>
						<?php _e( 'In Progress', 'learnpress' ); ?>
					</span>
				<?php else : ?>
					<span class="start-badge">
						<i class="lp-icon-play"></i>
						<?php _e( 'Start Course', 'learnpress' ); ?>
					</span>
				<?php endif; ?>
			</div>
		</div>
	</div>
	<?php
}

// Custom Profile Certificates
add_action( 'learn-press/custom-profile-certificates', 'custom_profile_certificates_content', 10, 2 );
function custom_profile_certificates_content( $profile, $user ) {
	// Get user's certificates
	$certificates = get_user_meta( $user->get_id(), '_lp_certificates', true );

	if ( empty( $certificates ) ) {
		// Show sample certificates for demo based on design
		$demo_certificates = [
			[
				'title' => 'شهادة الإنجاز', // Certificate of Achievement in Arabic
				'title_en' => 'Certificate of Achievement',
				'name' => $user->get_display_name(),
				'date' => '2025-07-10',
				'id' => '659512',
				'course' => 'Wireless Course (Mobile Package) EN TO END',
				'grade' => 'A+',
				'status' => 'verified'
			]
		];

		echo '<div class="certificates-container">';
		foreach ( $demo_certificates as $cert ) {
			custom_render_certificate_item( $cert, $user );
		}
		echo '</div>';
		return;
	}

	echo '<div class="certificates-container">';
	foreach ( $certificates as $certificate ) {
		custom_render_certificate_item( $certificate, $user );
	}
	echo '</div>';
}

// Helper function to render certificate items
function custom_render_certificate_item( $certificate, $user ) {
	$title = $certificate['title'] ?? $certificate['title_en'] ?? __( 'Certificate of Achievement', 'learnpress' );
	$name = $certificate['name'] ?? $user->get_display_name();
	$date = $certificate['date'] ?? date( 'Y-m-d' );
	$cert_id = $certificate['id'] ?? '000000';
	$course = $certificate['course'] ?? '';
	$grade = $certificate['grade'] ?? '';
	$status = $certificate['status'] ?? 'verified';
	$download_url = $certificate['download_url'] ?? '#';

	// Generate certificate preview URL (placeholder)
	$preview_url = $certificate['preview_url'] ?? '#';
	?>
	<div class="certificate-item" data-certificate-id="<?php echo esc_attr( $cert_id ); ?>">
		<div class="certificate-visual">
			<div class="certificate-preview">
				<div class="certificate-border">
					<div class="certificate-content">
						<div class="certificate-header">
							<div class="certificate-logo">
								<i class="lp-icon-certificate"></i>
							</div>
							<h4 class="certificate-title"><?php echo esc_html( $title ); ?></h4>
						</div>

						<div class="certificate-body">
							<p class="certificate-text"><?php _e( 'This is to certify that', 'learnpress' ); ?></p>
							<h3 class="certificate-name"><?php echo esc_html( $name ); ?></h3>
							<?php if ( $course ) : ?>
								<p class="certificate-course"><?php _e( 'has successfully completed', 'learnpress' ); ?></p>
								<h4 class="course-name"><?php echo esc_html( $course ); ?></h4>
							<?php endif; ?>
						</div>

						<div class="certificate-footer">
							<div class="certificate-details">
								<div class="detail-item">
									<span class="detail-label"><?php _e( 'Date', 'learnpress' ); ?>:</span>
									<span class="detail-value"><?php echo esc_html( $date ); ?></span>
								</div>
								<div class="detail-item">
									<span class="detail-label"><?php _e( 'ID', 'learnpress' ); ?>:</span>
									<span class="detail-value"><?php echo esc_html( $cert_id ); ?></span>
								</div>
								<?php if ( $grade ) : ?>
								<div class="detail-item">
									<span class="detail-label"><?php _e( 'Grade', 'learnpress' ); ?>:</span>
									<span class="detail-value"><?php echo esc_html( $grade ); ?></span>
								</div>
								<?php endif; ?>
							</div>

							<div class="certificate-status">
								<span class="status-badge status-<?php echo esc_attr( $status ); ?>">
									<i class="lp-icon-check-circle"></i>
									<?php echo ucfirst( $status ); ?>
								</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="certificate-actions">
			<div class="action-buttons">
				<a href="<?php echo esc_url( $download_url ); ?>" class="download-certificate-btn primary-btn" target="_blank">
					<i class="lp-icon-download"></i>
					<?php _e( 'Download Certificate', 'learnpress' ); ?>
				</a>
				<a href="<?php echo esc_url( $preview_url ); ?>" class="preview-certificate-btn secondary-btn" target="_blank">
					<i class="lp-icon-eye"></i>
					<?php _e( 'Preview', 'learnpress' ); ?>
				</a>
				<button class="share-certificate-btn tertiary-btn" onclick="shareCertificate('<?php echo esc_js( $cert_id ); ?>')">
					<i class="lp-icon-share"></i>
					<?php _e( 'Share', 'learnpress' ); ?>
				</button>
			</div>

			<div class="certificate-meta">
				<div class="meta-item">
					<i class="lp-icon-calendar"></i>
					<span><?php _e( 'Issued on', 'learnpress' ); ?> <?php echo esc_html( date( 'F j, Y', strtotime( $date ) ) ); ?></span>
				</div>
				<div class="meta-item">
					<i class="lp-icon-shield"></i>
					<span><?php _e( 'Verified Certificate', 'learnpress' ); ?></span>
				</div>
			</div>
		</div>
	</div>
	<?php
}

// Custom Profile Courses
add_action( 'learn-press/custom-profile-courses', 'custom_profile_courses_content', 10, 2 );
function custom_profile_courses_content( $profile, $user ) {
	$courses = learn_press_get_enrolled_courses( $user->get_id() );

	if ( empty( $courses ) ) {
		// Show demo course for design purposes
		$demo_courses = [
			[
				'title' => 'Wireless Course (Mobile Package) EN TO END',
				'instructor' => 'Ibrahim Ibrahim',
				'rating' => 4.8,
				'price' => '45.40 AED',
				'duration' => '20 week',
				'enrollment' => 'EGP20:181.42',
				'progress' => 96,
				'thumbnail' => '',
				'students' => 1250,
				'lessons' => 45,
				'level' => 'Intermediate'
			]
		];

		echo '<div class="courses-container">';
		foreach ( $demo_courses as $course_data ) {
			custom_render_course_item( $course_data, $user );
		}
		echo '</div>';
		return;
	}

	echo '<div class="courses-container">';

	foreach ( $courses as $course_id ) {
		$course = learn_press_get_course( $course_id );
		if ( ! $course ) continue;

		$instructor = learn_press_get_course_instructor( $course_id );
		$rating = learn_press_get_course_rate( $course_id );
		$price = $course->get_price_html();
		$duration = $course->get_duration();
		$progress = learn_press_get_user_course_progress( $user->get_id(), $course_id );
		$students = learn_press_get_course_students( $course_id );
		$lessons = $course->count_items( LP_LESSON_CPT );

		$course_data = [
			'title' => $course->get_title(),
			'instructor' => $instructor->get_display_name(),
			'rating' => $rating,
			'price' => $price,
			'duration' => $duration,
			'progress' => $progress,
			'thumbnail' => $course->get_image( 'medium' ),
			'students' => is_array( $students ) ? count( $students ) : 0,
			'lessons' => $lessons,
			'level' => get_post_meta( $course_id, '_lp_level', true ) ?: 'Beginner',
			'course_url' => get_permalink( $course_id )
		];

		custom_render_course_item( $course_data, $user );
	}

	echo '</div>';
}

// Helper function to render course items
function custom_render_course_item( $course_data, $user ) {
	$title = $course_data['title'];
	$instructor = $course_data['instructor'];
	$rating = $course_data['rating'];
	$price = $course_data['price'];
	$duration = $course_data['duration'];
	$progress = $course_data['progress'] ?? 0;
	$thumbnail = $course_data['thumbnail'] ?? '';
	$students = $course_data['students'] ?? 0;
	$lessons = $course_data['lessons'] ?? 0;
	$level = $course_data['level'] ?? 'Beginner';
	$course_url = $course_data['course_url'] ?? '#';
	$enrollment = $course_data['enrollment'] ?? '';

	// Determine progress status
	$progress_status = 'not-started';
	if ( $progress >= 100 ) {
		$progress_status = 'completed';
	} elseif ( $progress > 0 ) {
		$progress_status = 'in-progress';
	}

	// Progress color
	$progress_color = '#007BFF';
	if ( $progress >= 90 ) {
		$progress_color = '#28A745';
	} elseif ( $progress >= 50 ) {
		$progress_color = '#007BFF';
	} else {
		$progress_color = '#DC3545';
	}
	?>
	<div class="course-item <?php echo esc_attr( 'status-' . $progress_status ); ?>" data-course-progress="<?php echo $progress; ?>">
		<div class="course-thumbnail-wrapper">
			<?php if ( $thumbnail ) : ?>
				<?php echo $thumbnail; ?>
			<?php else : ?>
				<div class="course-placeholder">
					<i class="lp-icon-book"></i>
				</div>
			<?php endif; ?>

			<div class="course-overlay">
				<div class="course-progress-circle">
					<svg class="progress-ring" width="60" height="60">
						<circle class="progress-ring-circle"
								stroke="<?php echo $progress_color; ?>"
								stroke-width="4"
								fill="transparent"
								r="26"
								cx="30"
								cy="30"
								style="stroke-dasharray: <?php echo 2 * 3.14159 * 26; ?>; stroke-dashoffset: <?php echo 2 * 3.14159 * 26 * (1 - $progress / 100); ?>;" />
					</svg>
					<div class="progress-text"><?php echo round( $progress ); ?>%</div>
				</div>
			</div>
		</div>

		<div class="course-content">
			<div class="course-header">
				<h4 class="course-title">
					<a href="<?php echo esc_url( $course_url ); ?>"><?php echo esc_html( $title ); ?></a>
				</h4>
				<div class="course-level">
					<span class="level-badge level-<?php echo esc_attr( strtolower( $level ) ); ?>">
						<?php echo esc_html( $level ); ?>
					</span>
				</div>
			</div>

			<div class="course-instructor-info">
				<div class="instructor-avatar">
					<i class="lp-icon-user"></i>
				</div>
				<div class="instructor-details">
					<span class="instructor-name"><?php echo esc_html( $instructor ); ?></span>
					<span class="instructor-title"><?php _e( 'Instructor', 'learnpress' ); ?></span>
				</div>
			</div>

			<div class="course-stats">
				<div class="stat-group">
					<div class="stat-item">
						<i class="lp-icon-star"></i>
						<span class="stat-value"><?php echo number_format( $rating, 1 ); ?></span>
						<span class="stat-label"><?php _e( 'Rating', 'learnpress' ); ?></span>
					</div>
					<div class="stat-item">
						<i class="lp-icon-users"></i>
						<span class="stat-value"><?php echo number_format( $students ); ?></span>
						<span class="stat-label"><?php _e( 'Students', 'learnpress' ); ?></span>
					</div>
					<div class="stat-item">
						<i class="lp-icon-file-text"></i>
						<span class="stat-value"><?php echo $lessons; ?></span>
						<span class="stat-label"><?php _e( 'Lessons', 'learnpress' ); ?></span>
					</div>
					<div class="stat-item">
						<i class="lp-icon-clock"></i>
						<span class="stat-value"><?php echo esc_html( $duration ); ?></span>
						<span class="stat-label"><?php _e( 'Duration', 'learnpress' ); ?></span>
					</div>
				</div>
			</div>

			<div class="course-pricing">
				<div class="price-info">
					<span class="current-price"><?php echo $price; ?></span>
					<?php if ( $enrollment ) : ?>
						<span class="enrollment-info"><?php echo esc_html( $enrollment ); ?></span>
					<?php endif; ?>
				</div>
				<div class="course-actions">
					<a href="<?php echo esc_url( $course_url ); ?>" class="continue-course-btn">
						<?php if ( $progress_status === 'completed' ) : ?>
							<i class="lp-icon-check"></i>
							<?php _e( 'Completed', 'learnpress' ); ?>
						<?php elseif ( $progress_status === 'in-progress' ) : ?>
							<i class="lp-icon-play"></i>
							<?php _e( 'Continue', 'learnpress' ); ?>
						<?php else : ?>
							<i class="lp-icon-play"></i>
							<?php _e( 'Start Course', 'learnpress' ); ?>
						<?php endif; ?>
					</a>
				</div>
			</div>
		</div>
	</div>
	<?php
}

// Custom Profile User Info
add_action( 'learn-press/custom-profile-user-info', 'custom_profile_user_info_content', 10, 2 );
function custom_profile_user_info_content( $profile, $user ) {
	$user_name = $user->get_display_name();
	$user_email = $user->get_email();
	$followers = get_user_meta( $user->get_id(), '_lp_followers', true ) ?: 6; // Default for demo
	$following = get_user_meta( $user->get_id(), '_lp_following', true ) ?: 0;
	?>
	<div class="user-info-container">
		<div class="user-info-grid">
			<div class="info-item">
				<label><?php _e( 'Name', 'learnpress' ); ?>:</label>
				<span><?php echo esc_html( $user_name ); ?></span>
			</div>
			<div class="info-item">
				<label><?php _e( 'Email', 'learnpress' ); ?>:</label>
				<span><?php echo esc_html( $user_email ); ?></span>
			</div>
			<div class="info-item">
				<label><?php _e( 'Followers', 'learnpress' ); ?>:</label>
				<span><?php echo esc_html( $followers ); ?></span>
			</div>
			<div class="info-item">
				<label><?php _e( 'Following', 'learnpress' ); ?>:</label>
				<span><?php echo esc_html( $following ); ?></span>
			</div>
		</div>
	</div>
	<?php
}

// Enqueue custom profile styles
add_action( 'wp_enqueue_scripts', 'custom_profile_styles' );
function custom_profile_styles() {
	if ( is_page() && strpos( get_the_content(), 'learn-press-profile' ) !== false ) {
		wp_add_inline_style( 'parent-style', '
			/* Additional Custom Profile Styles */
			.custom-header-content {
				display: flex;
				align-items: center;
				gap: 20px;
			}

			.user-avatar img {
				border-radius: 50%;
				border: 3px solid white;
			}

			.user-name {
				margin: 0;
				font-size: 24px;
				font-weight: 600;
			}

			.user-email {
				margin: 5px 0 0 0;
				opacity: 0.9;
			}

			.custom-sidebar-content .user-info-item {
				display: flex;
				justify-content: space-between;
				padding: 10px 0;
				border-bottom: 1px solid #eee;
			}

			.custom-sidebar-content .user-info-item label {
				font-weight: 600;
				color: var(--primary-color);
			}

			.progress-item {
				margin-bottom: 25px;
				padding: 20px;
				border: 1px solid #e0e0e0;
				border-radius: 12px;
				background: white;
				box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
				transition: all 0.3s ease;
				position: relative;
				overflow: hidden;
			}

			.progress-item:hover {
				box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
				transform: translateY(-2px);
			}

			.progress-item.status-completed {
				border-left: 4px solid #28A745;
				background: linear-gradient(135deg, #f8fff9 0%, #ffffff 100%);
			}

			.progress-item.status-in-progress {
				border-left: 4px solid #007BFF;
				background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
			}

			.progress-item.status-not-started {
				border-left: 4px solid #6C757D;
				background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
			}

			.progress-header {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;
				margin-bottom: 15px;
			}

			.progress-info {
				flex: 1;
			}

			.course-title {
				font-size: 16px;
				font-weight: 600;
				color: var(--text-color);
				margin-bottom: 5px;
				display: block;
				line-height: 1.4;
			}

			.progress-status {
				font-size: 12px;
				color: var(--light-text-color);
				text-transform: uppercase;
				letter-spacing: 0.5px;
				font-weight: 500;
			}

			.progress-percentage-wrapper {
				text-align: right;
			}

			.progress-percentage {
				font-size: 24px;
				font-weight: 700;
				line-height: 1;
			}

			.progress-bar-wrapper {
				margin-bottom: 15px;
			}

			.progress-bar {
				height: 12px;
				background-color: #e9ecef;
				border-radius: 6px;
				overflow: hidden;
				position: relative;
				margin-bottom: 8px;
			}

			.progress-fill {
				height: 100%;
				border-radius: 6px;
				transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
				position: relative;
				overflow: hidden;
			}

			.progress-fill::after {
				content: "";
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
				animation: shimmer 2s infinite;
			}

			@keyframes shimmer {
				0% { transform: translateX(-100%); }
				100% { transform: translateX(100%); }
			}

			.progress-bar-labels {
				display: flex;
				justify-content: space-between;
				font-size: 11px;
				color: var(--light-text-color);
			}

			.progress-footer {
				display: flex;
				justify-content: space-between;
				align-items: center;
			}

			.progress-date {
				display: flex;
				align-items: center;
				gap: 5px;
				font-size: 12px;
				color: var(--light-text-color);
			}

			.progress-date i {
				font-size: 14px;
			}

			.progress-actions {
				display: flex;
				align-items: center;
			}

			.completion-badge,
			.progress-badge,
			.start-badge {
				display: flex;
				align-items: center;
				gap: 5px;
				padding: 4px 8px;
				border-radius: 12px;
				font-size: 11px;
				font-weight: 600;
				text-transform: uppercase;
				letter-spacing: 0.3px;
			}

			.completion-badge {
				background: #d4edda;
				color: #155724;
			}

			.progress-badge {
				background: #cce7ff;
				color: #004085;
			}

			.start-badge {
				background: #f8f9fa;
				color: #6c757d;
			}

			.certificate-item {
				display: grid;
				grid-template-columns: 2fr 1fr;
				gap: 25px;
				padding: 25px;
				border: 2px solid var(--primary-color);
				border-radius: 12px;
				margin-bottom: 25px;
				background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
				box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
				transition: all 0.3s ease;
			}

			.certificate-item:hover {
				transform: translateY(-3px);
				box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
			}

			.certificate-visual {
				position: relative;
			}

			.certificate-preview {
				background: white;
				border: 3px solid var(--primary-color);
				border-radius: 8px;
				padding: 20px;
				box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
				position: relative;
				overflow: hidden;
			}

			.certificate-preview::before {
				content: "";
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
				pointer-events: none;
			}

			.certificate-border {
				border: 2px solid var(--secondary-color);
				border-radius: 4px;
				padding: 15px;
				position: relative;
			}

			.certificate-header {
				text-align: center;
				margin-bottom: 15px;
				border-bottom: 1px solid #e0e0e0;
				padding-bottom: 10px;
			}

			.certificate-logo i {
				font-size: 32px;
				color: var(--secondary-color);
				margin-bottom: 8px;
			}

			.certificate-title {
				margin: 0;
				font-size: 18px;
				font-weight: 700;
				color: var(--primary-color);
				text-transform: uppercase;
				letter-spacing: 1px;
			}

			.certificate-body {
				text-align: center;
				margin-bottom: 15px;
			}

			.certificate-text {
				font-size: 12px;
				color: var(--light-text-color);
				margin-bottom: 8px;
			}

			.certificate-name {
				font-size: 20px;
				font-weight: 700;
				color: var(--primary-color);
				margin: 8px 0;
				text-decoration: underline;
			}

			.certificate-course {
				font-size: 12px;
				color: var(--light-text-color);
				margin-bottom: 5px;
			}

			.course-name {
				font-size: 14px;
				font-weight: 600;
				color: var(--text-color);
				margin: 5px 0;
			}

			.certificate-footer {
				display: flex;
				justify-content: space-between;
				align-items: center;
				border-top: 1px solid #e0e0e0;
				padding-top: 10px;
			}

			.certificate-details {
				display: flex;
				flex-direction: column;
				gap: 3px;
			}

			.detail-item {
				display: flex;
				gap: 5px;
				font-size: 10px;
			}

			.detail-label {
				font-weight: 600;
				color: var(--light-text-color);
			}

			.detail-value {
				color: var(--text-color);
			}

			.certificate-status {
				text-align: right;
			}

			.status-badge {
				display: inline-flex;
				align-items: center;
				gap: 4px;
				padding: 4px 8px;
				border-radius: 12px;
				font-size: 10px;
				font-weight: 600;
				text-transform: uppercase;
			}

			.status-verified {
				background: #d4edda;
				color: #155724;
			}

			.certificate-actions {
				display: flex;
				flex-direction: column;
				gap: 20px;
			}

			.action-buttons {
				display: flex;
				flex-direction: column;
				gap: 10px;
			}

			.primary-btn,
			.secondary-btn,
			.tertiary-btn {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 8px;
				padding: 12px 16px;
				border: none;
				border-radius: 6px;
				font-weight: 600;
				text-decoration: none;
				cursor: pointer;
				transition: all 0.3s ease;
				font-size: 14px;
			}

			.primary-btn {
				background: linear-gradient(135deg, var(--button-color) 0%, var(--secondary-color) 100%);
				color: white;
			}

			.primary-btn:hover {
				transform: translateY(-1px);
				box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
			}

			.secondary-btn {
				background: white;
				color: var(--primary-color);
				border: 2px solid var(--primary-color);
			}

			.secondary-btn:hover {
				background: var(--primary-color);
				color: white;
			}

			.tertiary-btn {
				background: #f8f9fa;
				color: var(--text-color);
				border: 1px solid #e0e0e0;
			}

			.tertiary-btn:hover {
				background: #e9ecef;
			}

			.certificate-meta {
				display: flex;
				flex-direction: column;
				gap: 8px;
			}

			.meta-item {
				display: flex;
				align-items: center;
				gap: 6px;
				font-size: 12px;
				color: var(--light-text-color);
			}

			.meta-item i {
				font-size: 14px;
				color: var(--secondary-color);
			}

			.course-item {
				display: grid;
				grid-template-columns: 200px 1fr;
				gap: 20px;
				padding: 20px;
				border: 1px solid #e0e0e0;
				border-radius: 12px;
				margin-bottom: 20px;
				background: white;
				box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
				transition: all 0.3s ease;
				position: relative;
				overflow: hidden;
			}

			.course-item:hover {
				box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
				transform: translateY(-2px);
			}

			.course-item.status-completed {
				border-left: 4px solid #28A745;
			}

			.course-item.status-in-progress {
				border-left: 4px solid #007BFF;
			}

			.course-item.status-not-started {
				border-left: 4px solid #6C757D;
			}

			.course-thumbnail-wrapper {
				position: relative;
				border-radius: 8px;
				overflow: hidden;
				height: 120px;
			}

			.course-thumbnail-wrapper img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}

			.course-placeholder {
				width: 100%;
				height: 100%;
				background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
				display: flex;
				align-items: center;
				justify-content: center;
				color: white;
				font-size: 32px;
			}

			.course-overlay {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: rgba(0, 0, 0, 0.7);
				display: flex;
				align-items: center;
				justify-content: center;
				opacity: 0;
				transition: opacity 0.3s ease;
			}

			.course-item:hover .course-overlay {
				opacity: 1;
			}

			.course-progress-circle {
				position: relative;
				width: 60px;
				height: 60px;
			}

			.progress-ring {
				transform: rotate(-90deg);
			}

			.progress-ring-circle {
				transition: stroke-dashoffset 0.5s ease;
			}

			.progress-text {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				color: white;
				font-weight: 700;
				font-size: 12px;
			}

			.course-content {
				display: flex;
				flex-direction: column;
				gap: 15px;
			}

			.course-header {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;
			}

			.course-title {
				margin: 0;
				flex: 1;
			}

			.course-title a {
				color: var(--primary-color);
				text-decoration: none;
				font-size: 18px;
				font-weight: 600;
				line-height: 1.3;
			}

			.course-title a:hover {
				color: var(--secondary-color);
			}

			.course-level {
				margin-left: 10px;
			}

			.level-badge {
				padding: 4px 8px;
				border-radius: 12px;
				font-size: 11px;
				font-weight: 600;
				text-transform: uppercase;
				letter-spacing: 0.3px;
			}

			.level-beginner {
				background: #d4edda;
				color: #155724;
			}

			.level-intermediate {
				background: #cce7ff;
				color: #004085;
			}

			.level-advanced {
				background: #f8d7da;
				color: #721c24;
			}

			.course-instructor-info {
				display: flex;
				align-items: center;
				gap: 10px;
			}

			.instructor-avatar {
				width: 32px;
				height: 32px;
				border-radius: 50%;
				background: var(--primary-color);
				display: flex;
				align-items: center;
				justify-content: center;
				color: white;
				font-size: 14px;
			}

			.instructor-details {
				display: flex;
				flex-direction: column;
			}

			.instructor-name {
				font-weight: 600;
				color: var(--text-color);
				font-size: 14px;
			}

			.instructor-title {
				font-size: 12px;
				color: var(--light-text-color);
			}

			.course-stats {
				border: 1px solid #f0f0f0;
				border-radius: 6px;
				padding: 12px;
				background: #f8f9fa;
			}

			.stat-group {
				display: grid;
				grid-template-columns: repeat(4, 1fr);
				gap: 15px;
			}

			.stat-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				text-align: center;
				gap: 4px;
			}

			.stat-item i {
				font-size: 16px;
				color: var(--secondary-color);
			}

			.stat-value {
				font-weight: 700;
				color: var(--primary-color);
				font-size: 14px;
			}

			.stat-label {
				font-size: 10px;
				color: var(--light-text-color);
				text-transform: uppercase;
				letter-spacing: 0.3px;
			}

			.course-pricing {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding-top: 15px;
				border-top: 1px solid #f0f0f0;
			}

			.price-info {
				display: flex;
				flex-direction: column;
				gap: 3px;
			}

			.current-price {
				font-size: 18px;
				font-weight: 700;
				color: var(--secondary-color);
			}

			.enrollment-info {
				font-size: 12px;
				color: var(--light-text-color);
			}

			.continue-course-btn {
				display: flex;
				align-items: center;
				gap: 6px;
				padding: 10px 16px;
				background: var(--primary-color);
				color: white;
				text-decoration: none;
				border-radius: 6px;
				font-weight: 600;
				font-size: 14px;
				transition: all 0.3s ease;
			}

			.continue-course-btn:hover {
				background: var(--button-hover-color);
				transform: translateY(-1px);
			}

			.course-item.status-completed .continue-course-btn {
				background: #28A745;
			}

			.course-item.status-in-progress .continue-course-btn {
				background: var(--secondary-color);
			}

			.user-info-grid {
				display: grid;
				grid-template-columns: 1fr 1fr;
				gap: 15px;
			}

			.info-item {
				display: flex;
				flex-direction: column;
				gap: 5px;
			}

			.info-item label {
				font-weight: 600;
				color: var(--primary-color);
			}
		' );
	}
}

// Add JavaScript for certificate sharing
add_action( 'wp_footer', 'custom_profile_scripts' );
function custom_profile_scripts() {
	if ( is_page() && strpos( get_the_content(), 'learn-press-profile' ) !== false ) {
		?>
		<script>
		function shareCertificate(certificateId) {
			if (navigator.share) {
				navigator.share({
					title: '<?php _e( "My Certificate", "learnpress" ); ?>',
					text: '<?php _e( "Check out my certificate!", "learnpress" ); ?>',
					url: window.location.href + '?certificate=' + certificateId
				}).catch(console.error);
			} else {
				// Fallback for browsers that don't support Web Share API
				const url = window.location.href + '?certificate=' + certificateId;
				if (navigator.clipboard) {
					navigator.clipboard.writeText(url).then(() => {
						alert('<?php _e( "Certificate link copied to clipboard!", "learnpress" ); ?>');
					});
				} else {
					// Further fallback
					const textArea = document.createElement('textarea');
					textArea.value = url;
					document.body.appendChild(textArea);
					textArea.select();
					document.execCommand('copy');
					document.body.removeChild(textArea);
					alert('<?php _e( "Certificate link copied to clipboard!", "learnpress" ); ?>');
				}
			}
		}

		// Add smooth animations to progress bars
		document.addEventListener('DOMContentLoaded', function() {
			const progressBars = document.querySelectorAll('.progress-fill');

			const observerOptions = {
				threshold: 0.1,
				rootMargin: '0px 0px -50px 0px'
			};

			const observer = new IntersectionObserver((entries) => {
				entries.forEach(entry => {
					if (entry.isIntersecting) {
						const progressBar = entry.target;
						const percentage = progressBar.dataset.percentage;

						// Animate the progress bar
						progressBar.style.width = '0%';
						setTimeout(() => {
							progressBar.style.width = percentage + '%';
						}, 100);

						observer.unobserve(progressBar);
					}
				});
			}, observerOptions);

			progressBars.forEach(bar => {
				observer.observe(bar);
			});
		});
		</script>
		<?php
	}
}
