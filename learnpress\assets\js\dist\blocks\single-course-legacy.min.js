(()=>{"use strict";const e=window.React,s=window.wp.i18n,n=window.wp.components,r=window.wp.blockEditor,t=t=>{const l=(0,r.useBlockProps)();return(0,e.createElement)("div",{...l},(0,e.createElement)(n.Placeholder,{label:(0,s.__)("Single Course (Legacy)","learnpress")},(0,e.createElement)("div",null,(0,s.__)("Display full content of Single Course, can not edit.","learnpress"))))},l=e=>null,o=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/single-course-legacy","title":"Single Course (Legacy)","category":"learnpress-legacy","description":"Renders template Single Course Legacy PHP templates.","textdomain":"learnpress","keywords":["legacy","learnpress"],"ancestor":[],"usesContext":[],"supports":{"align":true}}'),a=window.wp.blocks,c=window.wp.data;let i=null;var p,u,d;p=["learnpress/learnpress//single-lp_course","learnpress/learnpress//single-lp_course-offline"],u=o,d=e=>{(0,a.registerBlockType)(e.name,{...e,edit:t,save:l})},(0,c.subscribe)(()=>{const e={...u},s=(0,c.select)("core/editor")||null;if(!s||"function"!=typeof s.getCurrentPostId||!s.getCurrentPostId())return;const n=s.getCurrentPostId();null!==n&&i!==n&&(i=n,(0,a.getBlockType)(e.name)&&((0,a.unregisterBlockType)(e.name),p.includes(n)?(e.ancestor=null,d(e)):(e.ancestor||(e.ancestor=[]),d(e))))}),(0,a.registerBlockType)(o.name,{...o,edit:t,save:l})})();