= 4.2.7.5 (2024-12-17) =
~ Added: option Price prefix, Price suffix for course.
~ Fixed: shortcode [learn_press_button_course].
~ Fixed: error status item's course, case option course set "no require enroll" (user enrolled).
~ Fixed: error lose tab on Profile when install SiteOrigin plugin.
~ Updated: library TomSelect, ChartJS to the latest.

= 4.2.7.4 (2024-12-03) =
~ Fixed: image cover profile with theme Twenty Five.
~ Fixed: error some sites not show notes(Waring) on the Admin Dashboard.
~ Fixed: security.
~ Tweak order show material on the item lesson.
~ Tweak load text domain compatible with WP 6.7 and later.
~ Tweak: format_human_time_diff method.
~ Tweak: UserItemModel, UserCourseModel, UserModel, CourseModel  classes.
~ Deprecated: get_info_extra_for_fast_query method.
~ Deprecated: _learn_press_usort_terms_by_ID, learn_press_course_item_format_exclude, learn_press_get_course_curriculum, learn_press_is_enrolled_course, learn_press_get_user_course_statuslearn_press_is_free_course, learn_press_course_enroll_required, learn_press_search_post_excerpt, learn_press_course_add_support_item_type, learn_press_course_add_support_item_type, learn_press_get_user_question_answer, need_to_updating, learn_press_get_course_sections, lean_press_get_course_sections, learn_press_get_course_item_url, learn_press_edit_item_link, learn_press_get_course_results_tooltip.
~ Set cache get instructors API for App.
~ Added: get_i18n_string_plural method on the class LP_Helper.

= ******* (2024-11-12) =
~ Fixed: error course private admin/author can't see on the frontend.
~ Fixed: only show label "Final" of Quiz when choose "Evaluate via results of the final quiz".
~ Fixed: send mail enroll for user Guest not exists user.
~ Tweak: enroll/purchase course buttons.
~ Tweak: condition can enroll/purchase course.
~ Tweak: API enroll/purchase course.
~ Tweak: when order completed.
~ Tweak: set upload Avatar to PNG, instead of JPG.
~ Deprecated: hooks learn-press/user/can-enroll-course, learn-press/user/can-purchase-course. Replace to learn-press/user/can-enroll/course, learn-press/user/can-purchase/course.

= ******* (2024-10-21) =
~ Fixed: security.
~ Fixed: set size for Gravatar user.
~ Fixed: slug item's course use urldecode when edited.
~ Fixed: display name courses has special character Mobile.
~ Fixed: error save Enable/Disable all emails.
~ Added: get_all_items method for LP_Order class, for fix case "Paid Memberships" assign more than 50 courses.
~ Added: cover image feature on the Profile page.
~ Tweak: handle tomSelect.
~ Tweak: class LP_Datetime.
~ Tweak: style page Archive Courses.
~ Tweak: style page Profile.
~ Tweak: layout Offline course.
~ Update: lp icon fonts.

= ******* (2024-09-11) =
~ Fixed: security.
~ Use TomSelect instead of Select2 on settings.
~ Fixed: minor bugs.

= 4.2.7 (2024-08-27) =
~ Added: feature course offline.
~ Added: duration info for App API list courses.
~ Tweak: API get instructor info.
~ Fixed: error empty email content reset password.
~ Fixed: error empty image header.
~ Fixed: error create new section not send section_order.
~ Fixed: case search LP orders by user.
~ Fixed: error sort by total on list orders.

= *******.4 (2024-08-07) =
~ Fixed: security.

= *******.3 (2024-08-02) =
~ Tweak: added courses to LP Order manual.
~ Tweak: list orders status trash.
~ Tweak: message out of stock, no enroll requirement.
~ Fixed: material lesson display with case no enroll requirement.
~ Fixed: error not show email on Order detail when buy with Guest.
~ Fixed: error can't remove empty fields on "Extra Information" options.
~ Fixed: error clean break row (\n) on "Featured review" option.
~ Fixed: query Statistics by role.
~ Fixed: error max student when user bought.
~ Fixed: case no enroll requirement with course price.
~ Remove: jquery-ui-timepicker-addon jquery library.

= *******.2 (2024-07-25) =
~ Fixed: error lose section when update section of course.

= *******.1 (2024-07-24) =
~ Fixed: error check paths override of theme.

= ******* (2024-07-24) =
~ Fixed: security.
~ Optimize courses.
~ Create table learnpress_courses from posts to query faster.
~ Tweak: choose users when create manual LP Order.
~ Tweak: filed search instructor on list courses, orders, lessons, quizzes,...
~ Tweak: get options list author via API when edit courses (make edit load screen edit courses faster).
~ Fixed: show tab material with course no require enroll.

= 4.2.6.8.2 (2024-06-28) =
~ Added: function retrieve_password_message override message reset password.
~ Fixed: security.
~ Fixed: style login/register for Divi theme.
~ Fixed: responsive curriculum.

= 4.2.6.8.1 (2024-06-03) =
~ Fixed: security.
~ Fixed: query by status on list LP Orders.
~ Fixed: minor bugs.
~ Added: translate text grid, list.

= 4.2.6.8 (2024-05-27) =
~ Fixed: error create answers question.
~ Fixed: minor bugs.

= 4.2.6.7 (2024-05-20) =
~ Fixed: security.
~ Fixed: error conflict with CookieYes plugin.
~ Fixed: sale course price with config timezone of WordPress.
~ Optimize: style.
~ Remove Font Awesome and replace it with Font LearnPress.
~ Tweak: query search LP Order.

= 4.2.6.6 (2024-05-09) =
~ Fixed: security.
~ Fixed: delete user item when remove order item.
~ Tweak: material files feature.
~ Excluded: questions from search via param ?s of WordPress.
~ Compare the sale dates of courses through WordPress's Timezone.

= 4.2.6.5 (2024-04-17) =
~ Fixed: security.
~ Added: option "Load courses of subcategory", get all the courses in the child category that are not selected in the parent category.
~ Added: option "Number level category want to show" for widget course filter.
~ Added: option "show/hide author Admin on list instructors".

= 4.2.6.4 (2024-04-01) =
~ Compatible with WordPress 6.5.
~ Fixed: security.
~ Fixed: error remove question_anwsermeta when create new post question.
~ Fixed: compatible PHP8 on get_materials_by_item method.
~ Fixed: minor bug save order on the backend.
~ Fixed: error wrong link continue item, reason by old data still save item assigned to course, but deleted on post.
~ Fixed: minor bug profile shortcode.
~ Fixed: error shortcode list courses when add on Elementor.
~ Deprecated: get_downloadable_material method.

= 4.2.6.3 (2024-02-29) =
~ Fixed: error wrong avatar's instructor.
~ Fixed: error page Profile with user not login.

= 4.2.6.2 (2024-02-28) =
~ Added: param lang on url when submit filter courses.
~ Added: ProfileOrdersTemplate show layout orders on the Profile page.
~ Tweak: get_first_item_id method.
~ Tweak: methods Profile, templates Profile.
~ Fixed: error js hiddenQuestions.find not a function.
~ Fixed: avatar url profile with old value.
~ Fixed: error payment PayPal with Currency EUR.
~ Check user current can view content of tab Profile.
~ Deprecated hook 'learn-press/profile/orders', dashboard_featured_courses, dashboard_latest_courses, tab_dashboard methods.

= 4.2.6.1 (2024-02-20) =
~ Fixed: missing translate on "html_count_student" function.
~ Fixed: missing translate on profile avatar function.
~ Fixed: error when only has a payment method, will be not show payment form.
~ Fixed: error widget list courses elementor with theme override content-course.php.
~ Fixed: course query filter with polylang.

= 4.2.6 (2024-01-29) =
~ Tweak: courses js.
~ Tweak: course filter js.
~ Added: some class has -no-css for purpose not set style on this class.
~ Added: fake student on count students show on list courses.
~ Added: get_tags function for single courses.
~ Tweak: checkout js, not use serializeJSON() of JQuery.
~ Added: defer js some script.
~ Fixed: add param lang if exist for loadAjax.
~ Added: option "Class of list courses want to filter" on the Widget Filter Courses.
~ Added: icon drag material.
~ Remove callback hook learn-press/before-courses-loop-item, learn-press/after-courses-loop-item.
~ Fixed: code searching post by taxonomies make error block Woocommerce.

= ******* (2024-01-09) =
~ Added: show notification addons bought near expire support.
~ Fixed: error "Fib in blank" question case duplicate, when submit quiz wrong result.
~ Fixed widget course filter: error WPML handle wrong if link send not param lang.

= ******* (2023-12-25) =
~ Fixed: security.
~ Fixed: translate some text.
~ Tweak: layout list courses, set hooks for override, will soon stop support override template file.
~ Fixed: clear cache quiz question_ids when add/remove/delete/clone question on the quiz.
~ Added: code use for flutter.

= ******* (2023-12-06) =
~ Added: new feature "Assign/Unassign Courses" on the Tools Backend.
~ Added: courses filter support multiple language.
~ Dev: widget elementor filter courses.
~ Dev: widget elementor list courses.
~ Fixed: save sale price.

= ******* (2023-11-24) =
~ Tweak: logic load Widget Filter Courses.
~ Dev: assign/unassign course to user.
~ Fixed: error still show price when user has bought course but not start course.
~ Fixed: search suggest courses on Widget Filter Courses.
~ Fixed: not call function calculate price when edit course with user is instructor.

= ******* (2023-11-16) =
~ Fixed: security issue.
~ Added: new feature Statistic on the Backend.
~ Fixed: minor bug material.
~ Added: strategy defer/async for some script (WP 6.3 or higher will run).
~ Fixed: label 'Free' not show on the single free course.
~ Fixed: error sanitize key, make certificate can't save.

= ******* (2023-11-07) =
~ Fixed: security issue.
~ Tweak: display Category for filter courses.
~ Added: attribute disable if filter count = 0.
~ Tweak: widgets.js use fetch instead of wp.apiFetch.
~ Added: page tool clear cache.

= ******* (2023-11-02) =
~ Fixed: security issue.
~ Tweak: only show 1 level categories right behind cate parent, on the widget "Course Filter".
~ Added: method count students enrolled of course.
~ Added: method count courses free.
~ Added: cache for count.

= ******* (2023-10-27) =
~ Fixed: security issue.
~ Fixed: miss hook start quiz, retake quiz.
~ Fixed: minor bug material.
~ Fixed: error same course when access any single course.
~ Fixed: error don't hide info price/free when user enrolled course.

= ******* (2023-10-24) =
~ Fixed: security issue.
~ Compatible with WP 6.4.

= 4.2.5 (2023-10-19) =
~ Tweak: start quiz, retake quiz.
~ Tweaks: icon social.
~ Fixed: error font Arabic when export invoice.
~ Fixed: minus point when skip question.
~ Fixed: error duplicate message "out of stock" on the single course.
~ Fixed: error not show material of lesson.
~ Fixed: error set 0 to not show material.

= 4.2.4 (2023-10-03) =
~ Fixed: error Paypal Standard (use IPN), replace to use API REST, client ID, client secret.
~ Clean resource.
~ Check load Widget Elementor on hooks plugins_loaded.
~ Tweak: load Curriculum when edit course, query on Frontend.
~ Tweak: layout single instructor.
~ Fixed: error set Section Per Page = 0 or < -1.
~ Fixed: error set load curriculum item = 0.
~ Tweak: logic create/delete user_item.

= ******* (2023-09-18) =
~ Tweak: display Date Time, time zone by format of WP.
~ Fixed: count Progress course on the Single Item.
~ Fixed: current item active on the Curriculum.
~ Fixed: error not show price course when user repurchase course.

= ******* (2023-09-14) =
~ Tweak: checkout.
~ Fixed: case start quiz but user_item_id = 0.
~ Added: dynamic tag Course for Elementor.
~ Tweak: breadcrumb.
~ Set 404 if user not login, view page single instructor/no-slug-instructor.
~ Fixed: expire date, end date format i18n on Profile Page.

= ******* (2023-08-24) =
~ Fixed: error when use same namespace class JWT library, with some plugin like Google Listings and Ads.

= 4.2.3.3 (2023-08-08) =
~ Added: feature choose type Pagination when load courses on page Archive: navigation Number, Load more, Infinite scroll.
~ Added: feature "Downloadable Materials", manager can upload files document for each course, lesson, so student can download it.
~ Fixed: error with WPML, show wrong list courses with lang.
~ Fixed: sidebar Course Filter show wrong with Firefox.
~ Fixed: error fonts tiny with theme OceanWP.
~ Fixed: error 404 when view item's course with Polylang.
~ Fixed: SEO, show title of Category & Tag of Courses.
~ Tweak: checkout function.

= 4.2.3.2 (2023-07-19) =
~ Added: widget filter courses, option enable fields want to filter, sort fields, option load widget on REST.
~ Added: Search course suggest (AJAX).
~ Added: shortcode filter courses [learn_press_filter_course].
~ Fixed: error install/update Addon.

= 4.2.3.1 (2023-07-05) =
~ Fixed: Security.
~ Item Course: completed, display Date Time by format WP.
~ Tweak: message of LearnPress.
~ Fixed: function “Add course to Order manual”

= 4.2.3 (2023-07-03) =
~ Added: page list instructors.
~ Added: page single instructor.
~ Added: option set number instructor per page.
~ Added: shortcode show list instructors [learn_press_instructors].
~ Added: shortcode show single instructor [learn_press_single_instructor].
~ Auto add shortcode if page is list instructors or single instructor, if enable Elementor will not auto add.
~ Added: Widgets For Elementor: list instructors, instructor title, instructor description.
~ Added: Template default for list instructors on path "config/elementor/template-default", can import via Elementor.
~ Tweak: Widgets For Elementor: list courses, become a teacher, login form, register form.
~ Profile: added tab My Courses, display course attend of User.
~ Profile: tab My Courses, display statistic: total course enrolled, course in-progress, course finish, course passed, course failed.
~ Profile: tweak tab Courses, display course created by User (Admin, Instructor).
~ Profile: tab Courses, display statistic: total course created, course publish, course pending, total student, student in-progress.
~ Tweak: set default section_per_page, course_item_per_page = -1 to load full.
~ Added: format_human_time_diff method.
~ Tweak: get cart from session.
~ Check: $course->get_instructor() is null.
~ Added: hook do_action( 'lp/order-completed/update/user-item', $item, $order ), for case buy item not course.
~ Added: hook do_action( 'lp/order-pending/update/user-item', $item, $order ), for case buy item not course.
~ Added: nest_elements, print_sections methods on class Template, for easy override template without override file.
~ Tweak: statistic student/instructor on profile page.
~ Added: method "count_courses_of_author" instead "count_courses_publish_of_author"
~ Tweak: method "count_status_by_items"
~ Profile: added text "upload" translate on localize script.
~ Apply: add internal scripts to head on page List Instructors.
~ Fixed: error buy item with Guest, order is no item.
~ Fixed: Security Broken Access Control.
~ Added: hook login/register of WP on page LP Checkout for plugin captcha display.
~ Fixed: error 404 function comment on the lesson.

= ******* (2023-04-04) =
~ Compatible theme Gutenberg.
~ Compatible WP 6.2.
~ Fixed: auto add shortcode Profile.
~ Show notification: Addons has new version.
~ Fixed: error buy with Guest display pending Order.
~ Tweak: shortcode learn_press_button_course.
~ Fixed: error display title on mobile app.
~ Lesson: stop support post-formats.

= ******* (2023-03-21) =
~ Fixed: some error with Profile, LP_Profile_Tabs class.
~ Fixed: make some page builder like: elementor, visual composer work incorrect.
~ Remove implements ArrayAccess of LP_Query_List_Table class (to compatible with PHP8.1).

= ******* (2023-03-09) =
~ Fixed: permalink item 404 when install "YoastSeo" plugin.
~ Fixed: error not show tab Avatar on Profile Page.
~ Modified: rewrite rules.

= 4.2.2.1 (2023-03-03) =
~ Fixed: error link course with '%course_category%', on screen list courses Backend.
~ Fixed: some theme load only course on Archive Course Page.
~ Fixed: error not save basic info on Profile page.
~ Fixed: url current has symbol like الاعدادات.
~ Fixed: error not show message when save info Profile.

= 4.2.2 (2023-03-01) =
~ Optimized performance.
~ Optimize: rewrite "add_rewrite_rules" method, only call on "admin_init" hook.
~ Optimize: flush_rewrite_rules only when save settings.
~ Optimize: not call get_available_payment_gateways on hook 'wp_loaded'.
~ Optimize: not call learn_press_get_current_profile_tab on hook 'init'.
~ Optimize: add thim_cache table for cache (apply for site not install plugin Object Cache - or cache not working).
~ Optimize: set/get/clear cache for count total students enrolled.
~ Optimize: set/get/clear cache user_course (user_items) data.
~ Optimize: only load class REST when call on url of API.
~ Optimize: modified set cookie session for user Guest(user not login).
~ Added: a slash to the end of the course item link.
~ Fixed: some functions make out of memory.
~ Fixed: some functions use static not correct make out of memory.
~ Deprecated "learn_press_get_the_course" function, replace with "learn_press_get_course" function.
~ Deprecated: "learn_press_get_current_url" function, replace with "LP_Helper::getUrlCurrent" method.
~ Temporary comment _get_theme_info, plugin info, because very low.
~ Comment: hook learn_press_lesson_comment_form_fields, learn_press_get_only_content_permalink, learn_press_lesson_before_delete_post

= 4.2.1.1 (2023-02-04) =
~ Fixed: error js when add user on LP Order manual.
~ Modified: Not translate course slug base (permalink).

= 4.2.1 (2023-02-03) =
~ Added: feature manager addons of LearnPress, download, update, activate, deactivate.
~ Fixed: style button.
~ Fixed: deprecated register widget elementor.
~ Added: 'get_evaluation_type' function.
~ Modified: 'get_passing_condition' function.
~ Added: 'get_evaluation_type' function.
~ Added: hook 'lp/course/extra-info/before-save'.
~ Added: hook 'learn-press/login-redirect'.
~ Added: hook 'learn-press/register-redirect'.
~ Added: "status" row on LP Order Receiver.
~ Fixed: error not set pagination page numbers questions when retake quiz.
~ Modified: "set_title_pages" function.
~ Modified: "get_order_status_html" function, now can translate status LP Order.
~ Fixed: error "load more" on Profile page.
~ Fixed: error can't set Sale price to 0.
~ Modified: step of input Passing Grade from 1 to 0.01.

= 4.2.0 (2022-12-20) =
~ Fixed: compatibility with WordPress PHP 8.1.
~ Not implements ArrayAccess, Iterator, Countable of PHP on classes: LP_Datetime, LP_Session_Handler, LP_User_Item_Course, LP_User_Item, LP_Quiz_Results, LP_Course_Item, LP_Quiz, LP_Array_Access, LP_Profile_Tabs.
~ Except class LP_Query_List_Table we still keep implements ArrayAccess (will notice the warning, but don't worry, you only need to disable WP_DEBUG mode), because addons: learnpress-h5p(version 4.0.1 and lower), learnpress-assignment(version 4.0.7 and lower) require it. So you need to update to the higher version of these plugins to make sure.
~ Modified: LP_Datetime, deprecated method: toLocal, toRFC822, toUnix, setGMT, getSqlNullDate, addDuration, getPeriod.
~ Deprecated: get_time_remaining, get_time of the class LP_User_Item, get_finishing_type of the class LP_User_Item_Course.
~ Deprecated: get_course_remaining_time on the class LP_Abstract_User.
~ Modified: get_expiration_time, deprecated param $format (<a href='https://github.com/LearnPress/learnpress/commit/17502a02d5328c399f33ea1219f0c9e71f1a257d'>17502a0</a>).
~ Modified: class LP_Question_True_Or_False, LP_Question_Single_Choice, LP_Question_Multi_Choice, LP_Question_Fill_In_Blanks (<a href='https://github.com/LearnPress/learnpress/commit/3af1d1c78d3097f3d3b053ceecaf4264840eb1dc'>3af1d1c</a>).
~ Modified: class LP_Question, deprecated method: _get_default_answers, _filter_meta_box_meta, update_answer_orders (<a href='https://github.com/LearnPress/learnpress/commit/3af1d1c78d3097f3d3b053ceecaf4264840eb1dc'>3af1d1c</a>)
~ Modified: method 'get_answers' of class LP_Question (<a href='https://github.com/LearnPress/learnpress/commit/3af1d1c78d3097f3d3b053ceecaf4264840eb1dc#diff-24bccff44021f19220835ca960719a15beb4bbc8d3ffea086cccd6d42054ec7cR633'>3af1d1c</a>)
~ Deprecated: 'learn_press_course_question_permalink_friendly' function.
~ Added: 'LP_Question_Cache', LP_Session_Filter class.
~ Commented: 'learn_press_mark_user_just_logged_in' function.
~ Not store key 'order_awaiting_payment' in session, you need update 'learnpress-certificate' addon to version 4.0.4 or higher.
~ Not call: 'learn_press_clear_cart_after_payment' on hook 'get_header'.
~ Not call: 'learn_press_custom_checkout_cart' on hook 'learn_press_checkout_cart'.
~ Deprecated: 'learn_press_add_order' function.
~ Deprecated: learn_press_generate_transaction_object function.
~ Modified: LP_Shortcode_Checkout class.
~ Fixed: shortcode [learn_press_checkout] working right.
~ Deprecated: 'LP_Request_Handler' class, your need to update addons 'Course Review' (version 4.0.4 or higher), 'Wishlist' (version 4.0.4 or higher).
~ Not call 'learn_press_set_user_cookie_for_guest' function on hook 'wp'.
~ Commented: 'set_cookie' method on class LP_Request. <a href=''>c12c4b54</a>
~ Modified: 'LP_Session_Handler' class (<a href='https://github.com/LearnPress/learnpress/commit/c12c4b546548b750c3d05cb108162b3bee458d9b'>c12c4b54</a>).
~ Optimized: handle session.
~ Only set the cookie when the user does not log in.
~ When a user login is successful, the previous session of the guest user is deleted.
~ When a user logs out, expired sessions are deleted.
~ Modified: 'save_data' method of class LP_Session_Handler (<a href='https://github.com/LearnPress/learnpress/commit/c12c4b546548b750c3d05cb108162b3bee458d9b'>c12c4b5</a>).
~ Modified: 'get_cart_from_session', 'get_session_data', 'save_data' methods on class LP_Session_Handler.
~ Change name 'get_session' method to 'get_session_by_customer_id'.
~ Modified: 'update_session_timestamp', 'delete_session' methods on class LP_Session_Handler.
~ Modified: 'LP_Cart', optimize how to set session for cart, get cart items when called, instance of set on hook 'wp_loaded' always call, not call 'set_cart_cookies' when 'add_to_cart' method called.
~ Modified: 'calculate_totals' on class LP_Cart.
~ Deprecated: method 'get_cart_for_session' on class LP_Cart.
~ Modified: 'LP_Profile_Tabs' class.
~ Replace all access via array to fields and methods of classes LP_Query_List_Table, LP_Profile, LP_Profile_Tab.
~ Deprecated: 'offsetExists', 'offsetGet', 'get_current_question', 'get_question_position', 'check_question', 'get_question_link' methods LP_Quiz class.
~ Modified: 'create_order' method on class 'LP_Checkout'.
~ Deprecated: 'instructions' method on 'LP_Gateway_Offline_Payment' class.
~ Moved: settings permalink Profile to tab Permalinks.
~ Fixed security: inject SQL (CVE-2022-45820).
~ Fixed security: XSS.
~ Fixed: styles Backend, Frontend.
~ Remove save lesson preview on list lessons on the Backend.
~ Remove sortable between multiple section.
~ Modified: method "list_courses", change how call the template.
~ Checked: price is valid.
~ Fixed: search course in term with Polylang.
~ Modified: statistic of LP Order.
~ Checked: MathJax.Hub undefined.
~ Fixed: error json syntax API search courses, curriculum.

= *******.2 (2022-11-18) - <a href='https://thimpress.com/learnpress-v4-1-7-3-2-update/' target='_blank' rel='noopener'>View detail</a> =
~ Added: tab permalink option.
~ Modified: remove border style on the tab single course.
~ Modified: style popup complete item.
~ Show: notice check wp_remote_get if the call fail.
~ Show: notice if LearnPress has beta version.
~ Changed: text 'Enable' to 'Sticky Quiz Paging' on the Advanced Settings.

= *******.1 (2022-11-03) =
~ Fixed: error build missing styles.

= ******* (2022-11-02) =
~ Updated: text grammar.
~ Modified: LP_DateTime class.
~ Added: Course category and tag to menu.
~ Changed: input type regular price, sale price to text.
~ Call quiz get_questions function to get_question_ids.
~ Fixed: upgrade LP3 to LP4 missing result of lesson.
~ Remove file class-lp-backward-plugins.php, class-lp-factory.php, class-lp-query-search.php, class-lp-course-utils.php, class-lp-rest-authentication.php
~ Remove code deprecated before.

= 4.1.7.2 (2022-10-03) =
~ Removed: delete file mu-plugin (Moved to the plugin Thim Optimize).
~ Fixed: CVE-2022-3360 security.
~ Fixed: error "Quiz is not auto submitted when the time is expired".
~ Fixed: error "Completed item is not redirected to the next item".
~ Modified: Shortcode button LP.
~ Removed: delete shortcode 'learn_press_button_enroll'. We recommend using the shortcode 'learn_press_button_course' instead.
~ Fixed: error "Email content on one line when you save".

= ******* (2022-09-16) =
~ Fixed: non-LP plugins causing errors.

= 4.1.7 (2022-09-13) =
~ Fixed: IPN paypal.
~ Added: autocomplete meta-box field.
~ Fixed: h5p error in curriculum editor.
~ Modified: duplicate course, sections, items.
~ Handle multiple button Continue course.
~ Modified: has_completed_item() function.
~ Fixed: custom fields register not show on the Profile page.
~ Fixed: save order status.
~ Added: hook 'learn-press/user-item/expiration-time'.
~ Added: function duplicate question answer-meta when duplicate question.
~ Fixed: error show content of quiz when user doing questions.
~ Fixed: error fetch api has x-wp-nonce with cache expire.
~ Fixed: sidebar toggle on mobile with theme Eduma.
~ Added: autocomplete meta-box field.
~ Fixed: error not send mail when register user on page checkout.
~ Fixed: error wrong status graduation when set Passing Grade is 100% on Quiz.
~ Fixed: header, footer email of LP not detect variable.
~ Fixed: error duplicated items, section when double click.

= *******.3 (2022-08-10) =
~ Fixed: show wrong shortcode on course, item's course.

= *******.2 (2022-08-09) =
~ Fixed: show wrong special character (Ex: ü) on Section title, description.
~ Fixed: error on duplicate course, item function not same content old course, item.
~ Fixed: button sidebar toggle not show on default theme WP.
~ Fixed: ESC make error with payment gateway.

= *******.1 (2022-08-04) =
~ Fixed: error ESC content of course, items' course make iframe, embed not working.
~ Fixed: error complete lesson, do quiz on API for App mobile.

= ******* (2022-08-03) =
~ Modified: read items, read sections of course. (optimize read speed).
~ Modified: 'set_viewing_item' method.
~ Modified: set thumbnail image size on the 'Profile Page' by width setting on 'Avatar Dimensions'.
~ Fixed: error search no courses but return all (Archive courses).
~ Fixed: error LP_Shortcode_Course_Curriculum with Elementor.
~ Sanitize, ESC.
~ Deleted: folder attributes, files: 'class-lp-gdpr.php', 'class-lp-hard-cache.php', 'class-lp-repair-database.php', 'class-lp-utils.php'.
~ Clear, optimize codes.
~ Added: hook do_action 'lp/background/course/save'.
~ Added: hooks for addon LP - WPML.
~ Deprecated: methods 'load_curriculum'.
~ Style: FAQs on single course, spacing items progress, space in single tab.
~ Change: 'Course Item Per Page' set default is 10.

= 4.1.6.8 (2022-07-05) =
~ Fixed: click pagination when enable 2 option Load Courses Ajax.
~ Added: loading, error message(if it has) when start quick.
~ Fixed: error sort by popular with theme Eduma.
~ Fixed: Wordfence denied param: "author", convert to "user" on API.
~ Fixed: query popular courses for app.
~ Added: return price format for app.
~ Added: API delete account for app.
~ Merged: addon Offline Payment.
~ Fixed: styles with themes: Avada, Divi, Flastsome, Bridget, Astra, The 7.
~ Fixed: security.

= ******* (2022-06-20) =
~ Fixed: errors quiz when call API LP remove action 'wp_loaded'.
~ Fixed: some minor bugs.
~ Fixed: install tables for multiple sites.
~ Fixed: save settings always return tab General.
~ Fixed: layout 'select items' popup when edit course.=

= ******* (2022-06-13) =
~ Fixed: security.
~ Added: option "Enable loading ajax Courses on the Archive Course page".
~ Added: option "No Page load Courses Ajax".

= ******* (2022-05-16) =
~ Fixed: error with block theme 2022.
~ Fixed: error with LP Addon Frontend Editor.

= ******* (2022-05-05) =
~ Modified: setup require tables for LearnPress when activate plugin.
~ Modified: setup wizard.
~ Optimized: code.
~ Fixed don't scroll to item of section > config "Section Per Page".

= ******* (2022-04-20) =
~ Fixed: sort section of course.
~ Fixed: sort item of course's section when edit course.
~ Fixed: sometime call get_curriculum empty.
~ Modified: fixed when set seconds big (about 100 hours), can't convert to format time right.
~ Fixed: duplicate course not duplicate sections, items.
~ Fixed: error wp.media on page Email setting.
~ Fixed: count wrong order on the backend.

= 4.1.6.2 (2022-04-04) =
~ Fixed: go to last item of section still show button "load more items".
~ Fixed: "Undefined" when click "load more sections" on theme Eduma.

= 4.1.6.1 (2022-04-01) =
~ Optimize.
~ Fixed: error show input when retake Fill In Blank - off option "Show correct answer".
~ Modified: filter query courses API.
~ Hide load more item button which section it hide.
~ Fixed: save course is sale when edit course.
~ Added: hook "learn-press/user/quiz-finished".
~ Fixed: error site use sub-folder domain will be error 301 when checkout.
~ Fixed: error show "The item is not assigned to this course" on Page builder.
~ Fixed: error style RTL item-actions.
~ Fixed: case user guest buy course and not login, back to this course will not show button "purchase course".
~ Added: hook do_action( 'login_form' ) on the page LP Checkout, form "Login".

= 4.1.6 (2022-03-14) =
~ Compatible: with "Polylang" plugin.
~ Compatible LP Shortcode with Elementor.
~ Compatible: with block theme (Gutenberg).
~ Added: set -1 value for no limit Retake Quiz.
~ Added: order by popular courses on API.
~ Fixed: case set duration quiz = 0.
~ Fixed: case set password for quiz.
~ Fixed: create new question didn't set type.
~ Fixed: when search global WP with ?s= will not search item's course and question not assign.
~ Fixed: "Active Courses" empty on the Profile page.
~ Fixed: security does not sanitise and escape the lp-dismiss-notice.
~ Fixed: error many times redirect when access link domain/type/not_found.
~ Fixed: compare string answer not set "Match case" type Fill In Blank Question.

= 4.1.5 (2022-01-24) =
~ Modified: optimize, handle cache for query courses.
~ Added: meta key '_lp_regular_price'.
~ Modified: get price of course.
~ Added: "get_user_courses" function.
~ Fixed: security avatar image (Arbitrary Image Renaming).
~ Modified: upload avatar image.
~ Fixed: RTL style.
~ Modified: optimize load Curriculum via API, fast for big data (larger thousands items).

= ******* (2021-12-22) =
~ Added: settings redirect when finish course.
~ Added: enqueue script by shortcode button purchase.
~ Fixed: error save value on the textarea format code HTML.
~ Fixed: error timezone is different UTC-0 will finish quiz soon.
~ Save info total items (quiz, question,...) of course when saving the course - improve performance.
~ Fixed: error change user Guest to another user on LP Order not save.
~ Fixed: Guest buy course with user exists.
~ Fixed: Guest buy course with create account.
~ Fixed: High CPU (many query) when user login view single course.
~ Fixed: No require enroll.
~ Changed: view course's user on the Backend to the profile's user.
~ Modified: submit quiz, get result course, quiz.
~ Fixed: query get orders on Profile page.
~ Fixed: query get orders by user (multiple user) on the Backend.
~ Modified: delete course will delete section, section_items, lp_user_items, user_itemmeta.
~ Saved: info total items of course when save course - to make call fast.
~ Rewrite: function count total items of the Course.
~ Fixed: get course id when click Get Passing Grade in Evaluate via results of the final quiz.
~ Fixed: count Unassigned questions.
~ Changed logic check "fill in blank" question: user answer right all fields will get point of question, one of answer wrong, the question is fail and get point = 0.

= 4.1.4 (2021-11-08) =
~ Fixed security: when user share profile, another user can see settings as change password, change info.
~ Modify: not scroll to title courses when load first.
~ Fixed: get incorrect key setting "archive course layout".
~ Fixed: get value "From name", "Footer text" setting Email.
~ Removed: option set "From address" email, removed hook "wp_mail_from" make some site can't send email when install LP.
~ Comment some functions deprecated: "_learn_press_restrict_view_items", "_learn_press_set_user_items".
~ Added: hook "learnpress/course/template/price/can-show".
~ Added: filter "before_show_lp_widget_content".
~ Show icons on the child Settings tab - Profile page.
~ Fixed: case answer's question Fill in blank = "0" always incorrect.
~ Fixed: case content of question answers will change by hook apply_filters( 'the_content') when reload page, change to do_shortcode().
~ Fixed: store answer of user when reload page will lose.
~ Added: hooks apply_filters('learnpress/profile/tab/enrolled/subtab-active'), apply_filters('learnpress/profile/tab-active').
~ Modified: functions course_external_button, can_enroll_course with case course is external and purchased course.
~ Sanitize: email_footer, email_header.
~ Removed: some functions not use on file class-lp-email.php: "get_variable", "get_object", "get_common_template_data", "data_to_variables"
~ Fix show explanation when reload site.
~ Fix show message: "Your order is waiting for processing" when LP Order status is processing.
~ Fixed: condition get link last item if all items completed - on button continue.
~ Fixed: show explanation.
~ Fixed: error user login with mail buy as Guest and LP Oder completed but can't view course.
~ Fixed: error, <a href="https://blog.szfszf.top/static/papers/LearnPress_4.1.3.2_sql_injection_1cf8665be17b7708a3f180067fd2d50b.html">sql injection</a>, sanitize feature duplicate post (Course, Lesson, Quiz v.v...)

= 4.1.3.2 (2021-10-15) =
~ Fixed: security CVE-2021-39348, set sanitize for some params missing.

= ******* (2021-09-15) =
~ Fixed: some shortcode of latex show incorrect on question answer.
~ Added: hooks for course_continue_button, course_purchase_button, course_enroll_button.
~ Fixed: Fatal error LP_Abstract_User::get_course_data() when upgrade from LP3 to LP4.
~ Moved: function "Email hook notify" to hook "plugin_loaded".
~ Added: hooks "learnpress/hook/before-addons-call-hook-learnpress-ready".
~ Fixed security: Cross Site Scripting (XSS) on fields: External Link, Requirements, Target Audience, Key Features, FAQs.
~ Fixed: click button continue redirect wrong item.
~ Added: functions get_user_ids_enrolled, get_total_user_enrolled.
~ Fixed: error WP_Filesystem_Direct::exist not exist.

= 4.1.3 (2021-09-07) =
~ Modified: Learnpress order status on Dashboard of Wordpress.
~ Fixed: Widgets: Popular course, Feature course, Recent course, Course Progress, Course Info, Course Extra. All widget compatible with Elementor.
~ Fixed: Info Order have courses deleted on page Profile .
~ Fixed: error user can't learn course set external link when Admin created Order manual Completed has this course.
~ Improve performance.
~ Modify function set cache.
~ Modify custom post type of LP.
~ Modify feature run progress on background.
~ Handle send all email of LP (New Order, Processing Order, Completed Order, Cancelled Order, Enrolled Course, Finished Course, Become An Instructor) on Background.
~ Remove child order when Admin created Order manual.
~ Modify enroll/buy course (user, guest).
~ Added wysiwyg field for metabox.
~ Compatible with MathJax-LateX plugin.

= 4.1.2 =
~ Modify, optimize Archive course page - call API - make faster
~ Added: back icon on item page
~ Fixed: some where call old function "get_profile_socials" not has param $user_id

= 4.1.1 =
~ Added: tool clean "table session" clean sessions expire after 1 days.
~ Fixed: not show button "continue" when course is blocked.
~ Fixed: course doesn't has any item will be not show button "continue", "finish", "retake".
~ Updated: file languages

= 4.1.0 =
~ Added: feature "Allow repurchase option". Manager can set fixed: Reset/Keep course progress of course or allow student choice.
~ Fixed: show incorrect "Active Courses" on page profile.
~ Fixed: show fake student.
~ Fixed: duplicate label on form Register custom fields.

= 4.0.9 =
~ Added: option enable sticky navigation pagination when user answer question on quiz.
~ Fixed: set again current item when manager remove item current when edit course.
~ Modified: page profile - load ajax call API, show more info.
~ Fixed: can't upgrade DB from 3 to 4 because user translate menu "LearnPress".
~ Modified: Navigation on page quiz - change icon next, previous to text.
~ Fixed: function "duplicate course" not duplicate "answer option"
~ Fixed: Social icons on page profile when user view another user.
~ Fixed: on multiple site enable option "Registration is disabled" will not show form register on page Profile.

= 4.0.8 =
~ Added: feature "Allow Repurchase course"
~ Added: feature "No require enroll"
~ Fixed: Custom register fields error with some name not utf8
~ Added: field "confirm password" on register form profile
~ Fixed: file config.js permission 403 on some sites, change name to lp-configs.js
~ Fixed: When edit course, Admin can add item of another user

= 4.0.7 =
~ Fixed: CPU runs high
~ Fixed: "start quiz" error with some cases have cached.
~ Fixed: make Elementor pro show wrong: header, footer... (theme builder)

= 4.0.6 =
~ Fixed: Page profile not show login/register form
~ Fixed: translated text on js function "Quiz"
~ Fixed: js scroll to item viewing
~ Fixed: show html title question
~ Fixed: Order change status from "completed" to "pending" => user can't learn courses in this order
~ Added: Option "Logout Redirect"
~ Fixed: Set default sidebar curriculum will be hide on mobile
~ Modify: Remove course on Overview tab when enable "Publish Profile"
~ Fixed: Elementor pro make archive course show wrong

= 4.0.5 =
~ Fixed: error "Duplicate entry" for case upgrade LP4, if user install LP4 and save setting before Upgrade Database
~ Fixed: get option 'avatar_dimensions' set default value if user not set
~ Fixed: get option 'course_thumbnail_dimensions' set default value if user not set
~ Modify template Profile
~ Fixed: tool "Reset course progress"
~ Fixed: get options "Courses per page"

= 4.0.4 =
~ Fixed: get option "archive_course_limit" default value if empty.
~ Fixed: Profile shortcode.
~ Fixed: error table "learnpress_user_itemmeta" doesn’t exist when install new.
~ Fixed: style comment function.
~ Added: form comment on course.
~ Fixed: js itemProgress undefined in Eduma theme

= 4.0.3 =
~ Fixed: tool "Reset course progress"
~ Fixed: tool "Reset user progress"
~ Added: tool "Create Database Indexes"
~ Fixed js button "Retake course" if you have more than one.
~ Added: tool "Re upgrade Database" ~ If DB upgrade not success
~ Fixed: show button finish when completed quiz - if assessment passed

= 4.0.2 =
~ Upgrade library chart.js to v3.2.1
~ Optimize query with cache get list questions on a quiz
~ Show content(description) on page LP Profile, LP Archive
~ Fixed: errors when activated "Metabox" plugin
~ Fixed: broken layout profile page when activated "All in one seo" plugin
~ Removed: tool "Remove current Data"
~ Removed: tool "Remove outdated Data"
~ Removed: tool "Repair database"

= 4.0.1 =
~ Fixed: upgrade LP error if mysql version < 5.7 - "Specified key was too long".
~ Fixed: minor bugs.

= 4.0.0 =
~ Added new UI/UX for Quiz screen in frontend.
~ Added enable Gutenberg for lesson and quiz and question.
~ Added extra meta for course: Requirements, Target Audience, Key Features.
~ Improved quiz settings that made it simpler and easy to use.
~ Improved single course page.
~ Improved archive course page.
~ Restructure database tables.
~ Added some hooks/filters.

= ******* =
~ Fix compatible PHP 8.0.3
~ Fix Yoat SEO course category title not working

= ******* =
~ Fix: start quiz 404
~ Optimize

= ******* =
~ Fix minor bug
~ Optimize enroll course
~ Add cache get items' course
~ Add reset progress by course_id
~ Fix "Enroll Button" is not appearing when disable "Auto Enroll" option

= ******* =
~ Fix header lesson style error on iphone
~ Fix scrollbar error some themes
~ Fix scroll js to item user viewing
~ Add filter 'lp/email/type-order/object', 'lp/email/order/support_variable'

= ******* =
~ Add function Scan database if have not index in table will create
~ Add function Repurchase course when course finished or block duration expire
~ Fix function Statistics
~ Fix error content lesson conflict when activated elementor & yoast seo
~ Fix order status on Dashboard screen
~ Remove js scrollbar jquery
~ Add option 'Enable Popup Confirm Finish Course, Complete Item
~ Add message block duration

= ******* =
~ Fix compatible PHP 8.0
~ Fix checkout message error
~ Optimize

= ******* =
~ Fix compatible WP 5.6
~ Fix error Yoast-Seo in course archive page
~ Error file global.js on 'Twenty Seventeen' theme
~ Fix error page course archive with YoastSeo
~ Fix count students enrolled course on list course backend
~ Fix toggle curriculum bar for right-to-left

= ******* =
~ Fix WPBakery load style inline on course's item page
~ Fix make co-instructor not show list courses on backend
~ Fix miss lib vue js on LearnPress / Tools / Course page

= 3.2.8 =
~ Fix query get posts (courses, items courses) on Backend - multiple site
~ Fix error not same param on hook of Yoast SEO vs Yoast SEO premium
~ Fix security XSS function view_log
~ Fix check if get user on function learn_press_course_purchase_button() is null
~ Fix create statics pages LP on 'lp setup' page
~ Show message duplicate class RWMB_Field with another plugins

= ******* =
~ Fix question not show description

= ******* =
~ Fix save author id when add item when edit course
~ Fix title, description item course when install yoast seo plugin
~ Fix Retake when not enable duration expire
~ Fix function 'Instructors Registration'
~ Add function Export order invoice PDF

= ******* =
~ Add Evaluate via questions
~ Add Evaluate via mark
~ Fixed duration expire course
~ Fixed link 'Preview change' button when edit item course
~ Fixed title of course archive page
~ Fixed if quiz has only one question will not show paginate

= ******* =
~ Fix missing file class-lp-course-database.php

= ******* =
~ Fixed run Elementor with question.
~ Fixed lesson preview not show button complete when user enrolled.
~ Add tag apply_filter 'learn-press/order-item-not-course-id' on received-order.
~ Add tag apply_filter 'learn-press/tmpl-button-purchase-course' before return button purchase course.
~ Optimize (permalink of items course).
~ Show finish course button when items of course completed although the course not passed.
~ Fixed explanation of question when user completed quiz.
~ Hide description of quiz when the quiz completed.

= ******* =
~ Fix can't load items when select on Order Backend

= ******* =
~ Fixed error get_image() return bool not string on the file \templates\loop\course\thumbnail.php
~ Modify description for the function 'External Link'
~ Fixed LP_Datetime error with date = '0000-00-00 00:00:00'
~ Fixed not show number Duration of Lesson when translate text
~ Fixed not send mail for instructor when have new order
~ Remove hook get avatar of Ultimate member plugin
~ Fixed get value with, height image_size on LP setting
~ Fixed security, clear sanitize
~ Wilfried, security ninja at Synacktiv
~ Fixed compatible with Elementor on items of course (lesson, quizz, question v.v...)

= ******* =
~ Fixed email setting not save tag html

= ******* =
~ Fix save settings

= 3.2.7 =
~ Fix sanitize

= ******** =
~ Fix add-on GradeBook

= ******* =
~ Revert library meta-box to v4.15.7

= ******* =
~ Fixed security: remove functions low security
~ Fixed some minor bugs
~ Fixed confusing "external link button" name default of course
~ Fixed filter items of course
~ Fixed create same name, slug page Checkout with Woo
~ Improve performance

= ******* (2020.03.16) =
~ Fixed security issues (CVE-2020-7916): uer logged can change role all users to Instructor
~ Fixed security issues (CVE-2020-7917): remove function low security
~ Fixed error preview Assignment

= 3.2.6.6 =
~ Fixed option Block Lessons not working

= 3.2.6.5 (20.11.2019) =
~ Fixed email doesn't send.
~ Fixed some js errors.
~ Fixed js call twice times.
~ Fixed question doesn't show after added to quiz.
~ Fixed ignore some metadata when copying course.
~ Fixed search orders in backend.

= 3.2.6.4 =
~ Fixed some errors.

= 3.2.6.3 =
~ Fixed css conflict with text block of vc.
~ Fixed show message 'Out of stock' for course reached limitation users.
~ Fixed show checked answers when review quiz.
~ Fixed review quiz option does not work properly.
~ Fixed update view after removing order's items.

= 3.2.6.2 =
~ Fixed cannt add items to course.

= 3.2.6 =
~ Added option to exclude js/css libraries unnecessary (used in theme or other plugins).
~ Added alt prop to user profile avatar.
~ Fixed can't next/prev questions when doing quiz.
~ Fixed wrong items navigation when learning course.
~ Fixed missing js of some pages in admin.
~ Fixed can't close admin notices.
~ Updated envato api to newer version.

= 3.2.5.6 =
~ Added new strings for translating.
~ Corrected currency of Rwandan franc.
~ Fixed missing utils library when adding manually the orders.
~ Fixed upgrade function that doesn't hide the message when it done.
~ Fixed can't create new page in settings.

= 3.2.5.5 =
~ Fixed guest can not start quiz with no require enroll course option.
~ Fixed sql to filter orders by user ID.
~ Fixed issue of sending email when finished course: not correct Grade.
~ Fixed can not see Actions buttons when adding questions into the quiz.
~ Fixed changed the logic of Continue button for Course: continue with the next incomplete item.
~ Fixed wrong code to pick up instructor email.

= 3.2.5.4 =
~ Fixed load js missing dependencies and only in LP page.

= 3.2.5.3 =
~ Changed SQL to read course items by user item ID.
~ Improved performance in admin orders page.
~ Upgraded Vue/Vuex to latest version.
~ Added new theme to LP ad.

= 3.2.5.2 =
~ Fixed bug can't access course after purchased.
~ Fixed bug user can't redo quiz with option 'Retake' is 1.
~ Fixed bug can't order questions by date in questions bank.
~ Extracted purchased date to date and time in order emails.
~ Show point of quiz in result page.

= 3.2.5.1 =
~ Fixed can not next/prev question when doing quiz.
~ Fixed get wrong total student of a course.
~ Updated language .POT file.

= 3.2.5 =
~ Fixed button for creating LP pages does not work properly.
~ Fixed warning when getting course items does not exists.
~ Added button to close warning for outdated templates.
~ Fixed search order not working.
~ Fixed get course items in incorrect order.
~ Fixed can't start quiz when the course is not required enroll.
~ Fixed the amount number of enrolled users isn't updated correctly.

= 3.2.4 =
~ Fixed cannot enroll course.
~ Fixed prev question button not working correct.
~ Fixed one extra answer option when add new question.
~ Fixed some deprecated keywords for PHP 7.3.
~ Fixed item is null for an item which doesn't support it's type (like assignment after deactivate).
~ Fixed bug the next and prev button not work in review mode of quiz.

= 3.2.3 =
~ Removed un-security code in PP library.
~ Fixed get curriculum item types doesn't work properly.
~ Fixed sort sections/items wrong in SQL query.

= 3.2.2 =
~ Fixed review quiz doesn't work properly.
~ Fixed table session create a lot of rows.
~ Fixed can't enroll to a course purchased.
~ Removed unused functions.

= 3.2.1 =
~ Fixed can't enroll course.
~ Fixed upload issue and drag user avatar on mobile.
~ Fixed course duration does not work properly.
~ Fixed question with multi language.
~ Fixed mail to user 2 times when completed course.

= 3.2.0 =
~ Fixed issue info of order added manual not correct.
~ Fixed issue course duplicated is published.
~ Fixed issue Course content column show as "No Content".
~ Fixed some issues related to cache.

= 3.1.0 =
~ Fixed issue vulnerabilities.
~ Fixed issue related to object cache when doing quiz.
~ Fixed lesson 404 with Polylang.
~ Fixed PHP Fatal error class ‘LP_Plugins_Helper’ not found

= 3.0.12 =
~ Fixed minor bug in gradebook list in admin
~ Made hook learn-press/course-tabs work
~ Fixed bug: not auto complete quiz. Add 1 more filter hook for checking publicity in profile page
~ Fixed bug: auto enroll course without permission to enroll course
~ Changed filter tag for get_default_meta of lesson
~ Auto full-screen in mobile view
~ Fixed bug: wrong count number in No Preview at Lessons List back-end page
~ Fixed bug: Instructor user cannot see comments of lesson
~ Changed version for template files
~ Added base url for construct nav in user profile
~ Added param for get_nav method
~ Fixed bug: answer correct all question but quiz result is failed with 0%
~ Fixed small bug in quiz editor
~ Fixed bug: wrong count number of the courses in back end because of the status of preview course
~ Fixed bug: not redirect to correct page after logged in in Profile page
~ Fixed bug fatal error include file
~ Added icon for chat-type-format of content item
~ Fixed header-sent when log file

= 3.0.10 =
~ Added quiz option to minus a number of points for each wrong question in quiz
~ Added admin email to send to admin when an order is completed
~ Added button allows wp admin can send a request to subscriber
~ Improved auto redirecting to current question when user go to a quiz
~ Improved UI of course editor for RTL
~ Improved content of email sending to admin and instructor
~ Fixed page show 404 with pagination in courses page (conflict with WPML)
~ Fixed course price is still showing after user enrolled course
~ Fixed not auto redirecting to checkout after logged in
~ Fixed some issues made question can't edit
~ Fixed issue for requesting to get related themes/addons in admin
~ Fixed some issues with content header when viewing in Safari
~ Fixed wrong ordering of course item when adding new
~ Fixed order for multiple users is not show in list of orders
~ Fixed some text is not translatable
~ Fixed breadcrumb not show page name when viewing archive course
~ Fixed archive course show header is title of first course
