<?php
/**
 * Custom Template for displaying sidebar in user profile.
 *
 * This template overrides the default LearnPress profile sidebar
 * to implement a custom design with user information and navigation.
 *
 * <AUTHOR> Theme
 * @package Blocksy-Child/LearnPress
 * @version 1.0.0
 */

use LearnPress\Helpers\Template;

defined( 'ABSPATH' ) || exit;

$profile = LP_Profile::instance();
$user = $profile->get_user();

if ( ! $user ) {
	return;
}

$user_name = $user->get_display_name();
$user_email = $user->get_email();
$avatar_url = get_avatar_url( $user->get_id(), array( 'size' => 80 ) );
$bio = $user->get_description();

// Get user statistics
$enrolled_courses = learn_press_get_enrolled_courses( $user->get_id() );
$completed_courses = 0;
$in_progress_courses = 0;
$total_progress = 0;

if ( ! empty( $enrolled_courses ) ) {
	foreach ( $enrolled_courses as $course_id ) {
		$progress = learn_press_get_user_course_progress( $user->get_id(), $course_id );
		$total_progress += $progress;
		if ( $progress >= 100 ) {
			$completed_courses++;
		} elseif ( $progress > 0 ) {
			$in_progress_courses++;
		}
	}
}

$followers = get_user_meta( $user->get_id(), '_lp_followers', true ) ?: 6;
$following = get_user_meta( $user->get_id(), '_lp_following', true ) ?: 0;
$join_date = get_userdata( $user->get_id() )->user_registered;
?>

<div class="custom-profile-sidebar-wrapper">
	
	<!-- User Profile Card -->
	<div class="sidebar-user-card">
		<div class="user-card-header">
			<div class="user-avatar-small">
				<img src="<?php echo esc_url( $avatar_url ); ?>" alt="<?php echo esc_attr( $user_name ); ?>" />
			</div>
			<div class="user-card-info">
				<h3 class="user-card-name"><?php echo esc_html( $user_name ); ?></h3>
				<p class="user-card-email"><?php echo esc_html( $user_email ); ?></p>
			</div>
		</div>
		
		<?php if ( $bio ) : ?>
		<div class="user-card-bio">
			<p><?php echo wp_kses_post( wp_trim_words( $bio, 20 ) ); ?></p>
		</div>
		<?php endif; ?>
	</div>

	<!-- User Statistics -->
	<div class="sidebar-stats-section">
		<h4 class="sidebar-section-title"><?php _e( 'Statistics', 'learnpress' ); ?></h4>
		<div class="stats-list">
			<div class="stat-item">
				<span class="stat-label"><?php _e( 'Total Courses', 'learnpress' ); ?></span>
				<span class="stat-value"><?php echo count( $enrolled_courses ); ?></span>
			</div>
			<div class="stat-item">
				<span class="stat-label"><?php _e( 'Completed', 'learnpress' ); ?></span>
				<span class="stat-value"><?php echo $completed_courses; ?></span>
			</div>
			<div class="stat-item">
				<span class="stat-label"><?php _e( 'In Progress', 'learnpress' ); ?></span>
				<span class="stat-value"><?php echo $in_progress_courses; ?></span>
			</div>
		</div>
	</div>

	<!-- Social Information -->
	<div class="sidebar-social-section">
		<h4 class="sidebar-section-title"><?php _e( 'Social', 'learnpress' ); ?></h4>
		<div class="social-stats">
			<div class="social-item">
				<div class="social-number"><?php echo $followers; ?></div>
				<div class="social-label"><?php _e( 'Followers', 'learnpress' ); ?></div>
			</div>
			<div class="social-item">
				<div class="social-number"><?php echo $following; ?></div>
				<div class="social-label"><?php _e( 'Following', 'learnpress' ); ?></div>
			</div>
		</div>
	</div>

	<!-- User Information -->
	<div class="sidebar-info-section">
		<h4 class="sidebar-section-title"><?php _e( 'Information', 'learnpress' ); ?></h4>
		<div class="info-list">
			<div class="info-item">
				<span class="info-label"><?php _e( 'Name', 'learnpress' ); ?></span>
				<span class="info-value"><?php echo esc_html( $user_name ); ?></span>
			</div>
			<div class="info-item">
				<span class="info-label"><?php _e( 'Email', 'learnpress' ); ?></span>
				<span class="info-value"><?php echo esc_html( $user_email ); ?></span>
			</div>
			<div class="info-item">
				<span class="info-label"><?php _e( 'Member Since', 'learnpress' ); ?></span>
				<span class="info-value"><?php echo date( 'M Y', strtotime( $join_date ) ); ?></span>
			</div>
		</div>
	</div>

	<!-- Quick Actions -->
	<?php if ( $user->get_id() === get_current_user_id() ) : ?>
	<div class="sidebar-actions-section">
		<h4 class="sidebar-section-title"><?php _e( 'Quick Actions', 'learnpress' ); ?></h4>
		<div class="action-buttons">
			<a href="<?php echo esc_url( $profile->get_tab_link( 'settings' ) ); ?>" class="action-btn">
				<i class="lp-icon-cog"></i>
				<?php _e( 'Settings', 'learnpress' ); ?>
			</a>
			<a href="<?php echo esc_url( $profile->get_tab_link( 'courses' ) ); ?>" class="action-btn">
				<i class="lp-icon-book"></i>
				<?php _e( 'My Courses', 'learnpress' ); ?>
			</a>
			<a href="<?php echo esc_url( $profile->get_tab_link( 'orders' ) ); ?>" class="action-btn">
				<i class="lp-icon-shopping-cart"></i>
				<?php _e( 'Orders', 'learnpress' ); ?>
			</a>
		</div>
	</div>
	<?php endif; ?>

</div>

<style>
/* Custom Profile Sidebar Styles */
.custom-profile-sidebar-wrapper {
	display: flex;
	flex-direction: column;
	gap: 20px;
}

.sidebar-user-card {
	background: white;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	padding: 20px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-card-header {
	display: flex;
	align-items: center;
	gap: 15px;
	margin-bottom: 15px;
}

.user-avatar-small img {
	width: 60px;
	height: 60px;
	border-radius: 50%;
	object-fit: cover;
	border: 2px solid var(--primary-color, #003087);
}

.user-card-name {
	margin: 0 0 5px 0;
	font-size: 18px;
	font-weight: 600;
	color: var(--primary-color, #003087);
}

.user-card-email {
	margin: 0;
	font-size: 14px;
	color: var(--light-text-color, #6C757D);
}

.user-card-bio {
	padding-top: 15px;
	border-top: 1px solid #f0f0f0;
	font-size: 14px;
	line-height: 1.5;
	color: var(--text-color, #000000);
}

.sidebar-stats-section,
.sidebar-social-section,
.sidebar-info-section,
.sidebar-actions-section {
	background: white;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	padding: 20px;
}

.sidebar-section-title {
	margin: 0 0 15px 0;
	font-size: 16px;
	font-weight: 600;
	color: var(--primary-color, #003087);
	border-bottom: 2px solid var(--primary-color, #003087);
	padding-bottom: 8px;
}

.stats-list,
.info-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.stat-item,
.info-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 8px 0;
	border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child,
.info-item:last-child {
	border-bottom: none;
}

.stat-label,
.info-label {
	font-weight: 500;
	color: var(--text-color, #000000);
}

.stat-value,
.info-value {
	font-weight: 600;
	color: var(--primary-color, #003087);
}

.social-stats {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 15px;
}

.social-item {
	text-align: center;
	padding: 15px;
	background: #f8f9fa;
	border-radius: 8px;
}

.social-number {
	font-size: 24px;
	font-weight: 700;
	color: var(--primary-color, #003087);
	margin-bottom: 5px;
}

.social-label {
	font-size: 12px;
	color: var(--light-text-color, #6C757D);
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.action-buttons {
	display: flex;
	flex-direction: column;
	gap: 10px;
}

.action-btn {
	display: flex;
	align-items: center;
	gap: 10px;
	padding: 12px 15px;
	background: var(--primary-color, #003087);
	color: white;
	text-decoration: none;
	border-radius: 5px;
	font-size: 14px;
	font-weight: 500;
	transition: background-color 0.3s ease;
}

.action-btn:hover {
	background: var(--button-hover-color, #002766);
	color: white;
}

.action-btn i {
	font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
	.custom-profile-sidebar-wrapper {
		gap: 15px;
	}
	
	.sidebar-user-card,
	.sidebar-stats-section,
	.sidebar-social-section,
	.sidebar-info-section,
	.sidebar-actions-section {
		padding: 15px;
	}
	
	.user-card-header {
		flex-direction: column;
		text-align: center;
		gap: 10px;
	}
	
	.social-stats {
		grid-template-columns: 1fr;
		gap: 10px;
	}
}

/* RTL Support */
[dir="rtl"] .user-card-header {
	flex-direction: row-reverse;
}

[dir="rtl"] .stat-item,
[dir="rtl"] .info-item {
	flex-direction: row-reverse;
}

[dir="rtl"] .action-btn {
	flex-direction: row-reverse;
}
</style>
