(()=>{"use strict";const e=window.React,t=window.wp.i18n,n=window.wp.blockEditor,a=window.wp.components,s=(window.wp.element,s=>{const l=(0,n.useBlockProps)(),{attributes:r,setAttributes:o,context:i}=s,{lpCourseData:c}=i;return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(n.InspectorControls,null,(0,e.createElement)(a.Panel<PERSON><PERSON>,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(a.<PERSON><PERSON>,{label:(0,t.__)("Show Label","learnpress"),checked:r.showLabel,onChange:e=>{o({showLabel:e})}}),(0,e.createElement)(a.<PERSON>ggle<PERSON>ont<PERSON>,{label:(0,t.__)("Show Icon","learnpress"),checked:r.showIcon,onChange:e=>{o({showIcon:e})}}))),(0,e.createElement)("div",{...l},(0,e.createElement)("div",{className:"info-meta-item"},(0,e.createElement)("span",{className:"info-meta-left"},r.showIcon&&(0,e.createElement)("span",{dangerouslySetInnerHTML:{__html:'<i class="lp-icon-students"></i>'}}),r.showLabel?(0,t.__)("Capacity:","learnpress"):""),(0,e.createElement)("span",{className:"info-meta-right",dangerouslySetInnerHTML:{__html:'<span class="course-capacity">Unlimited</span>'}}))))}),l=e=>null,r=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-capacity","title":"Course Capacity","category":"learnpress-course-elements","description":"Show number capacity of course.","textdomain":"learnpress","keywords":["capacity","count","learnpress"],"ancestor":["learnpress/single-course"],"usesContext":["lpCourseData"],"attributes":{"showIcon":{"type":"boolean","default":true},"showLabel":{"type":"boolean","default":true}},"supports":{"multiple":true,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),o=window.wp.blocks,i=window.wp.data;let c=null;const p=window.wp.primitives,u=window.ReactJSXRuntime,m=(0,u.jsx)(p.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,u.jsx)(p.Path,{d:"M15.5 9.5a1 1 0 100-2 1 1 0 000 2zm0 1.5a2.5 2.5 0 100-5 2.5 2.5 0 000 5zm-2.25 6v-2a2.75 2.75 0 00-2.75-2.75h-4A2.75 2.75 0 003.75 15v2h1.5v-2c0-.69.56-1.25 1.25-1.25h4c.69 0 1.25.56 1.25 1.25v2h1.5zm7-2v2h-1.5v-2c0-.69-.56-1.25-1.25-1.25H15v-1.5h2.5A2.75 2.75 0 0120.25 15zM9.5 8.5a1 1 0 11-2 0 1 1 0 012 0zm1.5 0a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z",fillRule:"evenodd"})});var w,d,g;w=["learnpress/learnpress//single-lp_course-offline"],d=r,g=e=>{(0,o.registerBlockType)(e.name,{...e,icon:m,edit:s,save:l})},(0,i.subscribe)(()=>{const e={...d},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const n=t.getCurrentPostId();null!==n&&c!==n&&(c=n,(0,o.getBlockType)(e.name)&&((0,o.unregisterBlockType)(e.name),w.includes(n)?(e.ancestor=null,g(e)):(e.ancestor||(e.ancestor=[]),g(e))))}),(0,o.registerBlockType)(r.name,{...r,icon:m,edit:s,save:l})})();