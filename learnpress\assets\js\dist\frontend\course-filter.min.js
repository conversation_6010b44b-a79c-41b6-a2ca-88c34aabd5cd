(()=>{"use strict";const e={};let t;"undefined"!=typeof lpDataAdmin&&(t=lpDataAdmin.lp_rest_url,e.admin={apiAdminNotice:t+"lp/v1/admin/tools/admin-notices",apiAdminOrderStatic:t+"lp/v1/orders/statistic",apiAddons:t+"lp/v1/addon/all",apiAddonAction:t+"lp/v1/addon/action-n",apiAddonsPurchase:t+"lp/v1/addon/info-addons-purchase",apiSearchCourses:t+"lp/v1/admin/tools/search-course",apiSearchUsers:t+"lp/v1/admin/tools/search-user",apiAssignUserCourse:t+"lp/v1/admin/tools/assign-user-course",apiUnAssignUserCourse:t+"lp/v1/admin/tools/unassign-user-course"}),"undefined"!=typeof lpData&&(t=lpData.lp_rest_url,e.frontend={apiWidgets:t+"lp/v1/widgets/api",apiCourses:t+"lp/v1/courses/archive-course",apiAJAX:t+"lp/v1/load_content_via_ajax/",apiProfileCoverImage:t+"lp/v1/profile/cover-image"}),t&&(e.apiCourses=t+"lp/v1/courses/");const s=e,r="lp-hidden",a=(e,t)=>{const s=new URL(e);return Object.keys(t).forEach(e=>{s.searchParams.set(e,t[e])}),s},l=(e,t=0)=>{e&&(t?e.classList.remove(r):e.classList.add(r))},o="lp-form-course-filter",n="processing",i="show-lp-course-filter-mobile";let c,u,d;document.addEventListener("submit",function(e){const t=e.target;t.classList.contains(o)&&(e.preventDefault(),window.lpCourseFilter.submit(t))}),document.addEventListener("click",function(e){const t=e.target;if(t.classList.contains("course-filter-reset")&&(e.preventDefault(),window.lpCourseFilter.reset(t)),t.closest(".lp-form-course-filter__close")&&(e.preventDefault(),document.querySelector("body").classList.remove(`${i}`)),window.lpCourseFilter.showHideSearchResult(t),window.lpCourseFilter.triggerInputChoice(t),window.lpCourseFilter.clickBtnFilterMobile(t),!t.closest(`.${o}`)&&!t.closest(".course-filter-btn-mobile")){const e=document.querySelector("body");window.outerWidth<=991&&e.classList.contains(`${i}`)&&e.classList.remove(`${i}`)}}),document.addEventListener("keyup",function(e){e.preventDefault();const t=e.target;t.classList.contains("lp-course-filter-search")&&window.lpCourseFilter.searchSuggestion(t)}),window.lpCourseFilter={searchSuggestion:e=>{if(1!==parseInt(e.dataset.searchSuggest||1))return;const t=e.value.trim(),s=e.closest(`.${o}`),r=s.querySelector(".lp-loading-circle");void 0!==c&&clearTimeout(c),t&&t.length>2?(r.classList.remove("hide"),c=setTimeout(function(){window.lpCourseFilter.callAPICourseSuggest(t,e=>{document.querySelector(".lp-course-filter-search-result").innerHTML=e.data.content,r.classList.add("hide")})},500)):(s.querySelector(".lp-course-filter-search-result").innerHTML="",r.classList.add("hide"))},callAPICourseSuggest:(e,t,r)=>{void 0!==u&&u.abort(),u=new AbortController,d=u.signal;let a=s.frontend.apiCourses+"?c_search="+e+"&c_suggest=1";lpData.urlParams.hasOwnProperty("lang")&&(a+="&lang="+lpData.urlParams.lang);let l={method:"GET"};0!==parseInt(lpData.user_id)&&(l={...l,headers:{"X-WP-Nonce":lpData.nonce}}),fetch(a,{...l,signal:d}).then(e=>e.json()).then(e=>{void 0!==t&&t(e)}).catch(e=>{console.log(e)}).finally(()=>{void 0!==r&&r()})},loadWidgetFilterREST:e=>{const t=e.closest(`.learnpress-widget-wrapper:not(.${n})`);if(!t)return;t.classList.add(n);const r=e.closest("div[data-widget]");let a=null;if(r){const e=JSON.parse(r.dataset.widget),t=JSON.parse(e.instance).class_list_courses_target||".lp-list-courses-default";a=document.querySelector(t)}const o=t.dataset.widget?JSON.parse(t.dataset.widget):"",i=lpData.urlParams.lang?`?lang=${lpData.urlParams.lang}`:"",c=s.frontend.apiWidgets+i,u=new FormData(e),d={paged:1},p=t.querySelector(".lp-widget-loading-change");p.style.display="block";for(const e of u.entries()){const t=e[0],s=u.getAll(t);if(!d.hasOwnProperty(t)){let e=s;"object"==typeof s&&(e=s.join(",")),d[t]=e}}void 0!==lpData.urlParams.page_term_id_current?d.page_term_id_current=lpData.urlParams.page_term_id_current:void 0!==lpData.urlParams.page_tag_id_current&&(d.page_tag_id_current=lpData.urlParams.page_tag_id_current);const g={params_url:d};lpData.urlParams.hasOwnProperty("lang")?g.params_url.lang=lpData.urlParams.lang:lpData.urlParams.hasOwnProperty("pll-current-lang")&&(g.params_url["pll-current-lang"]=lpData.urlParams["pll-current-lang"]);const m={method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...o,...g})};0!==parseInt(lpData.user_id)&&(m.headers["X-WP-Nonce"]=lpData.nonce),((e,t={},s={})=>{"function"==typeof s.before&&s.before(),fetch(e,{method:"GET",...t}).then(e=>e.json()).then(e=>{"function"==typeof s.success&&s.success(e)}).catch(e=>{"function"==typeof s.error&&s.error(e)}).finally(()=>{"function"==typeof s.completed&&s.completed()})})(c,m,{before:()=>{},success:t=>{const{data:s,status:r,message:a}=t;if(s&&"success"===r){e.innerHTML=s;const t=e.querySelector(".course-filter-submit.lp-btn-done");t&&(window.outerWidth<=991?l(t,1):l(t,0))}else a&&console.error(a)},error:e=>{},completed:()=>{const e=setInterval(()=>{a&&!a.classList.contains(n)&&(clearInterval(e),p.style.display="none",t.classList.remove(n))},1)}})},submit:e=>{let t=s.frontend.apiAJAX;const r=new FormData(e),l=document.querySelector(".learn-press-courses"),o=e.closest("div[data-widget]");let c=null;if(o){const e=JSON.parse(o.dataset.widget),t=JSON.parse(e.instance).class_list_courses_target||".lp-list-courses-default";c=document.querySelector(t)}const u={paged:1};void 0!==window.lpCourseList&&window.lpCourseList.updateEventTypeBeforeFetch("filter");for(const e of r.entries()){const t=e[0],s=r.getAll(t);u.hasOwnProperty(t)||(u[t]=s.join(","))}if(void 0!==lpData.urlParams.page_term_id_current&&(u.page_term_id_current=lpData.urlParams.page_term_id_current),void 0!==lpData.urlParams.page_tag_id_current&&(u.page_tag_id_current=lpData.urlParams.page_tag_id_current),lpData.urlParams.hasOwnProperty("lang")?(u.lang=lpData.urlParams.lang,t=a(t,{lang:lpData.urlParams.lang})):lpData.urlParams.hasOwnProperty("pll-current-lang")&&(u["pll-current-lang"]=lpData.urlParams["pll-current-lang"],t=a(t,{lang:lpData.urlParams["pll-current-lang"]})),"undefined"!=typeof lpSettingCourses&&lpData.is_course_archive&&lpSettingCourses.lpArchiveLoadAjax&&l&&!c&&void 0!==window.lpCourseList)window.lpCourseList.triggerFetchAPI(u);else if(c){if(c.classList.contains(n))return;c.classList.add(n);const t=c.querySelector(".lp-target"),s={...JSON.parse(t.dataset.send)};window.lpAJAXG.showHideLoading(c,1);const r=e.elements;for(let e=0;e<r.length;e++)u.hasOwnProperty(r[e].name)?s.args[r[e].name]=u[r[e].name]:delete s.args[r[e].name];s.args.count_fields_selected=window.lpCourseFilter.countFieldsSelected(e),s.args.paged=1,t.dataset.send=JSON.stringify(s),lpData.urlParams=u,window.history.pushState({},"",a((()=>{let e=window.location.href;return e.includes("?")&&(e=e.split("?")[0]),e})(),lpData.urlParams)),window.lpCourseFilter.loadWidgetFilterREST(e);const l={success:e=>{const{status:s,message:r,data:a}=e;t.innerHTML=a.content||""},error:e=>{console.log(e)},completed:()=>{c.classList.remove(n),window.lpAJAXG.showHideLoading(c,0)}};window.lpAJAXG.fetchAJAX(s,l),window.outerWidth<=991&&(c.scrollIntoView({behavior:"smooth"}),document.querySelector("body").classList.remove(`${i}`))}else{const e=lpData.urlParams.page_term_url||lpData.courses_url||"",t=new URL(e);Object.keys(u).forEach(e=>{t.searchParams.set(e,u[e])}),document.location.href=t.href}},reset:e=>{const t=e.closest(`.${o}`);if(!t)return;const s=t.querySelector(".course-filter-submit"),r=t.querySelector(".lp-course-filter-search-result"),a=t.querySelector(".lp-course-filter-search");t.reset(),r&&(r.innerHTML=""),a&&(a.value="");for(let e=0;e<t.elements.length;e++)t.elements[e].removeAttribute("checked");s.click()},showHideSearchResult:e=>{const t=document.querySelector(".lp-course-filter-search-result");t&&(e.closest(".lp-course-filter-search-result")||e.classList.contains("lp-course-filter-search-result")||e.classList.contains("lp-course-filter-search")?t.style.display="block":t.style.display="none")},countFieldsSelected:e=>{const t=document.querySelector(".course-filter-count-fields-selected");if(!t)return;const s=e.querySelectorAll("input:checked");let r="";return s.length&&(r=`(${s.length})`),t.innerHTML=r,r},triggerInputChoice:e=>{const t=e.closest(".lp-course-filter__field");if(!t)return;const s=t.closest(`.${o}`);if("INPUT"===e.tagName){const e=t.closest("div[data-widget]");let s=null;if(e){const r=JSON.parse(e.dataset.widget),a=JSON.parse(r.instance).class_list_courses_target||".lp-list-courses-default";if(s=document.querySelector(a),window.outerWidth>991){const e=t.closest(`.${o}`);window.lpCourseFilter.submit(e)}}}else t.querySelector("input").click();window.lpCourseFilter.countFieldsSelected(s)},clickBtnFilterMobile:e=>{e.closest(".course-filter-btn-mobile")&&document.querySelector("body").classList.toggle(`${i}`)}}})();