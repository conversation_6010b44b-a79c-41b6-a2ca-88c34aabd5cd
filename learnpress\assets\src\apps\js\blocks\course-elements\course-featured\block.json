{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "learnpress/course-featured", "title": "Course Featured", "category": "learnpress-course-elements", "icon": "star-empty", "description": "Course featured.", "textdomain": "learnpress", "keywords": ["course featured", "learnpress"], "ancestor": ["learnpress/single-course", "learnpress/course-item-template"], "attributes": {}, "usesContext": ["lpCourseData"], "supports": {"multiple": true, "align": ["wide", "full"], "html": false, "typography": {"fontSize": true, "lineHeight": false, "fontWeight": true, "textTransform": false, "__experimentalFontFamily": false, "__experimentalTextDecoration": false, "__experimentalFontStyle": true, "__experimentalFontWeight": true, "__experimentalLetterSpacing": false, "__experimentalTextTransform": true, "__experimentalDefaultControls": {"fontSize": true, "textTransform": false}}, "color": {"text": true, "background": true, "__experimentalDefaultControls": {"text": true, "background": true}}, "__experimentalBorder": {"color": true, "radius": true, "width": true}, "spacing": {"padding": true, "margin": true, "__experimentalDefaultControls": {"margin": false, "padding": false}}}}