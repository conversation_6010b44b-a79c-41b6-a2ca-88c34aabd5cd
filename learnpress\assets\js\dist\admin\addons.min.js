(()=>{"use strict";const e={};let t;"undefined"!=typeof lpDataAdmin&&(t=lpDataAdmin.lp_rest_url,e.admin={apiAdminNotice:t+"lp/v1/admin/tools/admin-notices",apiAdminOrderStatic:t+"lp/v1/orders/statistic",apiAddons:t+"lp/v1/addon/all",apiAddonAction:t+"lp/v1/addon/action-n",apiAddonsPurchase:t+"lp/v1/addon/info-addons-purchase",apiSearchCourses:t+"lp/v1/admin/tools/search-course",apiSearchUsers:t+"lp/v1/admin/tools/search-user",apiAssignUserCourse:t+"lp/v1/admin/tools/assign-user-course",apiUnAssignUserCourse:t+"lp/v1/admin/tools/unassign-user-course"}),"undefined"!=typeof lpData&&(t=lpData.lp_rest_url,e.frontend={apiWidgets:t+"lp/v1/widgets/api",apiCourses:t+"lp/v1/courses/archive-course",apiAJAX:t+"lp/v1/load_content_via_ajax/",apiProfileCoverImage:t+"lp/v1/profile/cover-image"}),t&&(e.apiCourses=t+"lp/v1/courses/");const s=e;let a,n,o,c;const l=window.location.search,i=new URLSearchParams(l),r=i.get("tab");let d;const u=[],p=(e,t)=>{const s=d.querySelector(".lp-notify-action").cloneNode(!0);s.classList.remove("clone"),d.insertBefore(s,d[0]);const a=s.querySelector(`.${s.classList.value}__success`),n=s.querySelector(`.${s.classList.value}__error`);"success"===e?(a.classList.add("show"),a.querySelector(".message").innerHTML=t):(n.classList.add("show"),n.querySelector(".message").innerHTML=t),d.classList.add("show"),setTimeout(()=>{s.remove(),1===d.querySelectorAll(".lp-notify-action").length&&d.classList.remove("show")},"success"===e?3e3:4e3)};((e="")=>{const t=r?`?tab=${r}`:`?${e}`;fetch(s.admin.apiAddons+t,{method:"GET",headers:{"X-WP-Nonce":lpDataAdmin.nonce}}).then(e=>e.json()).then(e=>{const{status:t,message:s,data:a}=e;"success"===t?(n=a.html,o=a.addons):n=s}).catch(e=>{console.log(e)})})();const v=e=>{e<4?c.classList.add("max-3-items"):c.classList.remove("max-3-items")},m=setInterval(()=>{if(a||d){if(n&&a&&d){a.innerHTML=n,c=a.querySelector("#lp-addons");const e=document.querySelector(".lp-nav-tab-wrapper"),t=e.cloneNode(!0);a.insertBefore(t,a.children[0]),t.style.display="flex",e.remove();const s=t.querySelector(".nav-tab.nav-tab-active span");v(parseInt(s.textContent)),clearInterval(m)}}else a=document.querySelector(".lp-addons-page"),d=document.querySelector(".lp-notify-action-wrapper")},1);document.addEventListener("DOMContentLoaded",e=>{}),document.addEventListener("click",e=>{const t=e.target;if("span"===t.tagName.toLowerCase()){e.preventDefault();const s=t.closest(".btn-addon-action");s&&s.click()}if(t.classList.contains("btn-addon-action")){e.preventDefault(),t.classList.add("handling");let a="";const n=t.closest(".lp-addon-item"),c=o[n.dataset.slug],l=t.dataset.action,i=n.querySelector(".lp-addon-item__purchase");if("purchase"===l)return i.style.display="block",void(i.querySelector(".purchase-install").style.display="flex");if("update-purchase-code"===l)return i.querySelector(".purchase-update").style.display="flex",void(i.style.display="block");if("buy"===l){const e=t.dataset.link;return void window.open(e,"_blank")}if("cancel"===l)return void(i.style.display="none");if("install"===l&&t.dataset.link){t.classList.remove("handling");const e=t.dataset.link;return void window.open(e,"_blank")}i&&(a=i.querySelector("input[name=purchase-code]").value),((e,t)=>{const a=e.addon.slug;-1===u.indexOf(a)&&(u.push(a),fetch(s.admin.apiAddonAction,{method:"POST",headers:{"Content-Type":"application/json","X-WP-Nonce":lpDataAdmin.nonce},body:JSON.stringify({...e})}).then(e=>e.json()).then(e=>{const s=u.indexOf(a);-1!==s&&u.splice(s,1);const{status:n,message:o,data:c}=e;t&&t(n),p(n,o)}).catch(e=>{p("error",`error js: ${e}`),console.log(e)}))})({purchase_code:a,action:l,addon:c},function(e,s,a){if("success"===e)if("install"===l){n.classList.add("installed","activated"),n.classList.remove("not_installed"),i.style.display="none";const e=document.querySelector(".nav-tab[data-tab=installed] span");e.textContent=parseInt(e.textContent)+1;const t=document.querySelector(".nav-tab[data-tab=not_installed] span");t.textContent=parseInt(t.textContent)-1}else"update"===l?(n.querySelector(".addon-version-current").innerHTML=c.version,n.classList.remove("update")):"activate"===l?n.classList.add("activated"):"deactivate"===l?n.classList.remove("activated"):"update-purchase"===l&&(i.style.display="none");t.classList.remove("handling")})}if(t.classList.contains("nav-tab")){e.preventDefault(),document.querySelectorAll(".nav-tab").forEach(function(e){e.classList.remove("nav-tab-active")}),t.classList.add("nav-tab-active");const s=t.dataset.tab,n=a.querySelectorAll(".lp-addon-item");a.querySelector("#lp-search-addons__input").value="",i.set("tab",s),window.history.pushState({},"",`${window.location.pathname}?${i.toString()}`);let o=0;n.forEach(e=>{e.classList.remove("search-not-found"),"all"===s||e.classList.contains(s)?(e.classList.remove("hide"),o++):e.classList.add("hide")}),v(o)}}),document.addEventListener("input",e=>{const t=e.target;if("lp-search-addons__input"===t.id&&(e=>{const t=a.querySelectorAll(".lp-addon-item");let s=0;t.forEach(t=>{const a=t.querySelector("a").textContent;t.classList.contains("hide")||(a.toLowerCase().includes(e.toLowerCase())?(t.classList.remove("search-not-found"),s++):t.classList.add("search-not-found"))}),v(s)})(t.value),t.classList.contains("enter-purchase-code")){e.preventDefault();const s=t.value,a=t.closest(".lp-addon-item__purchase");a&&(a.querySelector("input[name=purchase-code]").value=s)}})})();