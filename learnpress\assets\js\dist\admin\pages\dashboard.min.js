(()=>{"use strict";const t={apiAdminNotice:lpGlobalSettings.rest+"lp/v1/admin/tools/admin-notices",apiAdminOrderStatic:lpGlobalSettings.rest+"lp/v1/orders/statistic",apiAddons:lpGlobalSettings.rest+"lp/v1/addon/all",apiAddonAction:lpGlobalSettings.rest+"lp/v1/addon/action"};let l=null,e=null;lpGlobalSettings.is_admin&&fetch(t.apiAdminOrderStatic,{method:"GET"}).then(t=>t.json()).then(t=>{l=t}).catch(t=>{console.log(t)});const s=setInterval(()=>{e=document.querySelector("ul.lp-order-statuses"),e&&null!==l&&("success"===l.status&&l.data?e.innerHTML=l.data:e.innerHTML=`<div class="error">${l.message}</div>`,clearInterval(s))},1)})();