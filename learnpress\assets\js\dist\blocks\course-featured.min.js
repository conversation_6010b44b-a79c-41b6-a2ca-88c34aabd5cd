(()=>{"use strict";const e=window.React,t=window.wp.i18n,r=window.wp.blockEditor,s=s=>{const{attributes:n,setAttributes:l,context:a}=s,o=(0,r.useBlockProps)(),{lpCourseData:i}=a,u=(0,t.__)("Featured","learnpress");return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...o,style:{...o.style,display:"inline-block"}},u))},n=e=>null,l=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-featured","title":"Course Featured","category":"learnpress-course-elements","icon":"star-empty","description":"Course featured.","textdomain":"learnpress","keywords":["course featured","learnpress"],"ancestor":["learnpress/single-course","learnpress/course-item-template"],"attributes":{},"usesContext":["lpCourseData"],"supports":{"multiple":true,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"textTransform":false,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":true,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true,"textTransform":false}},"color":{"text":true,"background":true,"__experimentalDefaultControls":{"text":true,"background":true}},"__experimentalBorder":{"color":true,"radius":true,"width":true},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),a=window.wp.blocks,o=window.wp.data;let i=null;var u,p,c;u=["learnpress/learnpress//single-lp_course","learnpress/learnpress//single-lp_course-offline"],p=l,c=e=>{(0,a.registerBlockType)(e.name,{...e,edit:s,save:n})},(0,o.subscribe)(()=>{const e={...p},t=(0,o.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&i!==r&&(i=r,(0,a.getBlockType)(e.name)&&((0,a.unregisterBlockType)(e.name),u.includes(r)?(e.ancestor=null,c(e)):(e.ancestor||(e.ancestor=[]),c(e))))}),(0,a.registerBlockType)(l.name,{...l,edit:s,save:n})})();