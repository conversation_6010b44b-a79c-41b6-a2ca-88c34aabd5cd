(()=>{"use strict";!function(n){let t=null,e=null,o=null;const i=function(t){o||(o=e.clone());const i=t.toLowerCase().split(/\s+/).filter(function(n,t){return n.length>=3});e.each(function(t){const e=n(this).html(""),c=(l=o.eq(t),u=e,l.find(".plugin-card").each(function(){const t=n(this),e=t.find(".item-title").text().toLowerCase(),o=t.find(".column-description, .theme-description").text();if(i.length){if(function(){const n=new RegExp(i.join("|"),"ig");return e.match(n)||o.match(n)}()){const n=t.clone();u.append(n)}}else u.append(t.clone())}),e.children().length);var l,u;e.prev("h2").find("span").html(c)})};n(document).on("keyup",".lp-search-addon",function(n){t&&clearTimeout(t),t=setTimeout(i,300,n.target.value)}),n(function(){e=n(".addons-browse")})}(jQuery);document.addEventListener("DOMContentLoaded",function(n){})})();