(()=>{"use strict";const e=window.React,t=window.wp.blockEditor,r=r=>{const s=(0,t.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...s},(0,e.createElement)("div",{className:"instructor-avatar"},(0,e.createElement)("img",{src:"https://placehold.co/300x300?text=Avatar+Instructor"}))))},s=e=>null,n=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/instructor-avatar","title":"Instructor Avatar","category":"learnpress-course-elements","icon":"format-image","description":"Renders template Instructor Avatar PHP templates.","textdomain":"learnpress","keywords":["instructor avatar single","learnpress"],"ancestor":["learnpress/single-instructor"],"usesContext":[],"supports":{"multiple":false,"align":["wide","full"],"html":false,"__experimentalBorder":{"color":true,"radius":true,"width":true,"__experimentalDefaultControls":{"width":false,"color":false,"radius":false}}}}'),a=window.wp.blocks,l=window.wp.primitives,o=window.ReactJSXRuntime,i=(0,o.jsx)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,o.jsx)(l.Path,{fillRule:"evenodd",d:"M7.25 16.437a6.5 6.5 0 1 1 9.5 0V16A2.75 2.75 0 0 0 14 13.25h-4A2.75 2.75 0 0 0 7.25 16v.437Zm1.5 1.193a6.47 6.47 0 0 0 3.25.87 6.47 6.47 0 0 0 3.25-.87V16c0-.69-.56-1.25-1.25-1.25h-4c-.69 0-1.25.56-1.25 1.25v1.63ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm10-2a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z",clipRule:"evenodd"})}),c=window.wp.data;let u=null;const d=[Number(lpDataAdmin?.single_instructor_id)];var p,m,w;p=d,m=n,w=e=>{(0,a.registerBlockType)(e.name,{...e,edit:r,save:s})},(0,c.subscribe)(()=>{const e={...m},t=(0,c.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&u!==r&&(u=r,(0,a.getBlockType)(e.name)&&((0,a.unregisterBlockType)(e.name),p.includes(r)?(e.ancestor=null,w(e)):(e.ancestor||(e.ancestor=[]),w(e))))}),(0,a.registerBlockType)(n.name,{...n,icon:i,edit:r,save:s})})();