(()=>{"use strict";const e=window.React,t=window.wp.i18n,r=window.wp.blockEditor,n=n=>{const s=(0,r.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...s},(0,e.createElement)("div",{className:"course-requirements extra-box"},(0,e.createElement)("h3",{className:"extra-box__title"},(0,t.__)("Requirements","learnpress")),(0,e.createElement)("ul",null,(0,e.createElement)("li",null,"Afflueret videsne commoventur debilitas etsi adridens habitus placuit hoc conatum deinde fruentem dirigentes longam sapientem"),(0,e.createElement)("li",null,"Lucullo summas debeatis varietate indoctum vitae cavere cornibus avaritias sequamini persequi assignatum polemoni"),(0,e.createElement)("li",null,"Laboro excelsiores meo utebare causam arripuit levem motu seditione egregio malitias istam")))))},s=e=>null,a=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-requirements","title":"Course Requirements","category":"learnpress-course-elements","icon":"index-card","description":"Renders template Box Extra Requirements Course PHP templates.","textdomain":"learnpress","keywords":["box extra requirements single course","learnpress"],"ancestor":["learnpress/single-course"],"usesContext":[],"supports":{"multiple":false,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"heading":true,"gradients":false,"__experimentalDefaultControls":{"text":true,"h3":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),l=window.wp.blocks,i=window.wp.data;let o=null;var u,c,m;u=["learnpress/learnpress//single-lp_course","learnpress/learnpress//single-lp_course-offline"],c=a,m=e=>{(0,l.registerBlockType)(e.name,{...e,edit:n,save:s})},(0,i.subscribe)(()=>{const e={...c},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&o!==r&&(o=r,(0,l.getBlockType)(e.name)&&((0,l.unregisterBlockType)(e.name),u.includes(r)?(e.ancestor=null,m(e)):(e.ancestor||(e.ancestor=[]),m(e))))}),(0,l.registerBlockType)(a.name,{...a,edit:n,save:s})})();