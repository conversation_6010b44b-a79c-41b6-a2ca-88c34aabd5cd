import*as Utils from"../utils.js";import Tom<PERSON>ele<PERSON> from"tom-select";import Api from"../api.js";const AdminUtilsFunctions={buildTomSelect(t,e,o,i,n){if(!t)return;const s={plugins:{remove_button:{title:"Remove this item"}},onInitialize(){},onItemAdd(e){if(o){const s=Array.from(t.selectedOptions).map(t=>t.value);s.push(e),i.id_not_in=s.join(","),o("",i,n)}}};o&&(s.load=(e,s)=>{const c=Array.from(t.selectedOptions).map(t=>t.value);i.id_not_in=c.join(","),o(e,i,AdminUtilsFunctions.callBackTomSelectSearchAPI(s,n))});(e={...s,...e}).options;return new TomSelect(t,e)},callBackTomSelectSearchAPI:(t,e)=>({success:o=>{const i=e.success(o);t(i)}}),fetchCourses(t="",e={},o){const i=Api.admin.apiSearchCourses;e.search=t;const n={headers:{"Content-Type":"application/json","X-WP-Nonce":lpDataAdmin.nonce},method:"POST",body:JSON.stringify(e)};Utils.lpFetchAPI(i,n,o)},fetchUsers(t="",e={},o){const i=Api.admin.apiSearchUsers;e.search=t;const n={headers:{"Content-Type":"application/json","X-WP-Nonce":lpDataAdmin.nonce},method:"POST",body:JSON.stringify(e)};Utils.lpFetchAPI(i,n,o)}};export{Utils,AdminUtilsFunctions,Api};