/**
 * Custom LearnPress Profile Styles
 * 
 * This file contains all custom styles for the LearnPress profile page
 * implementing the design specifications with proper color scheme and responsive design.
 *
 * <AUTHOR> Theme
 * @package  Blocksy-Child/LearnPress
 * @version  1.0.0
 */

/* CSS Variables - Design Color Scheme */
:root {
	--primary-color: #003087;
	--secondary-color: #007BFF;
	--background-color: #FFFFFF;
	--text-color: #000000;
	--light-text-color: #6C757D;
	--button-color: #003087;
	--button-hover-color: #002766;
	--progress-green: #28A745;
	--progress-red: #DC3545;
	--progress-blue: #007BFF;
	--footer-background: #003087;
	--footer-text: #FFFFFF;
	--border-color: #e0e0e0;
	--shadow-light: 0 2px 4px rgba(0, 0, 0, 0.05);
	--shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.1);
	--shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.15);
	--border-radius: 8px;
	--border-radius-large: 12px;
	--transition: all 0.3s ease;
}

/* Base Profile Layout */
.custom-profile-layout {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
	line-height: 1.6;
}

.custom-profile-content {
	max-width: 1200px;
	margin: 0 auto;
	padding: 20px;
}

/* Profile Grid Layout */
.custom-profile-grid {
	display: grid;
	grid-template-columns: 300px 1fr;
	gap: 30px;
	margin-bottom: 30px;
}

.custom-profile-sidebar {
	background: var(--background-color);
	border: 1px solid var(--border-color);
	border-radius: var(--border-radius-large);
	padding: 20px;
	height: fit-content;
	box-shadow: var(--shadow-light);
}

.custom-profile-main {
	display: flex;
	flex-direction: column;
	gap: 30px;
}

/* Section Styling */
.custom-progress-section,
.custom-certificates-section,
.custom-courses-section,
.custom-user-info-section {
	background: var(--background-color);
	border: 1px solid var(--border-color);
	border-radius: var(--border-radius-large);
	padding: 25px;
	box-shadow: var(--shadow-light);
	transition: var(--transition);
}

.custom-progress-section:hover,
.custom-certificates-section:hover,
.custom-courses-section:hover,
.custom-user-info-section:hover {
	box-shadow: var(--shadow-medium);
}

.section-title {
	color: var(--primary-color);
	font-size: 20px;
	font-weight: 600;
	margin-bottom: 20px;
	border-bottom: 2px solid var(--primary-color);
	padding-bottom: 10px;
	display: flex;
	align-items: center;
	gap: 10px;
}

.section-title::before {
	content: '';
	width: 4px;
	height: 20px;
	background: var(--secondary-color);
	border-radius: 2px;
}

/* Custom Profile Tabs Wrapper */
.custom-profile-tabs-wrapper {
	background: var(--background-color);
	border: 1px solid var(--border-color);
	border-radius: var(--border-radius-large);
	padding: 25px;
	box-shadow: var(--shadow-light);
}

/* Animation Classes */
.fade-in {
	animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
	from { opacity: 0; transform: translateY(20px); }
	to { opacity: 1; transform: translateY(0); }
}

.slide-up {
	animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
	from { opacity: 0; transform: translateY(30px); }
	to { opacity: 1; transform: translateY(0); }
}

/* Loading States */
.loading-skeleton {
	background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
	background-size: 200% 100%;
	animation: loading 1.5s infinite;
}

@keyframes loading {
	0% { background-position: 200% 0; }
	100% { background-position: -200% 0; }
}

/* Responsive Design - Tablet */
@media (max-width: 1024px) {
	.custom-profile-content {
		padding: 15px;
	}
	
	.custom-profile-grid {
		grid-template-columns: 250px 1fr;
		gap: 20px;
	}
	
	.custom-profile-main {
		gap: 20px;
	}
	
	.section-title {
		font-size: 18px;
	}
}

/* Responsive Design - Mobile */
@media (max-width: 768px) {
	.custom-profile-grid {
		grid-template-columns: 1fr;
		gap: 20px;
	}
	
	.custom-profile-content {
		padding: 10px;
	}
	
	.custom-progress-section,
	.custom-certificates-section,
	.custom-courses-section,
	.custom-user-info-section,
	.custom-profile-tabs-wrapper {
		padding: 20px;
	}
	
	.section-title {
		font-size: 16px;
		flex-direction: column;
		align-items: flex-start;
		gap: 8px;
	}
	
	.section-title::before {
		width: 100%;
		height: 2px;
	}
}

/* Small Mobile */
@media (max-width: 480px) {
	.custom-profile-content {
		padding: 5px;
	}
	
	.custom-progress-section,
	.custom-certificates-section,
	.custom-courses-section,
	.custom-user-info-section,
	.custom-profile-tabs-wrapper {
		padding: 15px;
		margin-bottom: 15px;
	}
	
	.custom-profile-main {
		gap: 15px;
	}
}

/* RTL Support */
[dir="rtl"] .custom-profile-grid {
	direction: rtl;
}

[dir="rtl"] .section-title {
	text-align: right;
	flex-direction: row-reverse;
}

[dir="rtl"] .section-title::before {
	margin-left: 10px;
	margin-right: 0;
}

/* Print Styles */
@media print {
	.custom-profile-layout {
		background: white !important;
		color: black !important;
	}
	
	.custom-profile-grid {
		grid-template-columns: 1fr;
		gap: 20px;
	}
	
	.custom-progress-section,
	.custom-certificates-section,
	.custom-courses-section,
	.custom-user-info-section {
		border: 1px solid #ccc;
		box-shadow: none;
		page-break-inside: avoid;
	}
	
	.section-title {
		color: black !important;
		border-bottom-color: #ccc !important;
	}
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
	:root {
		--primary-color: #000080;
		--secondary-color: #0066CC;
		--text-color: #000000;
		--light-text-color: #333333;
		--border-color: #666666;
	}
	
	.custom-progress-section,
	.custom-certificates-section,
	.custom-courses-section,
	.custom-user-info-section {
		border-width: 2px;
	}
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
	* {
		animation-duration: 0.01ms !important;
		animation-iteration-count: 1 !important;
		transition-duration: 0.01ms !important;
	}
	
	.fade-in,
	.slide-up {
		animation: none;
	}
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
	:root {
		--background-color: #1a1a1a;
		--text-color: #ffffff;
		--light-text-color: #cccccc;
		--border-color: #333333;
	}
	
	.custom-progress-section,
	.custom-certificates-section,
	.custom-courses-section,
	.custom-user-info-section,
	.custom-profile-tabs-wrapper {
		background: #2a2a2a;
		border-color: #444444;
	}
}

/* Focus States for Accessibility */
.custom-profile-layout a:focus,
.custom-profile-layout button:focus {
	outline: 2px solid var(--secondary-color);
	outline-offset: 2px;
}

/* Screen Reader Only Content */
.sr-only {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	white-space: nowrap;
	border: 0;
}
